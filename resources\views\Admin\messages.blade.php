@extends('layouts.app')
@section('title', 'Admin Dashboard - Messages')
@section('no-header-footer', 'true')
@section('content')

@include('layouts.admin-nav')

<section id="filterAndSearch">
    <label for="search-messages">Search Message</label>
    <input type="text" name="search-messages" id="search-messages">

    <select name="filter" id="filter">
        <option value="">All Messages</option>
        <option value="read">All Read</option>
        <option value="unread">All Unread</option>
        <option value="replied">All Replied</option>
        <option value="non-replied">All Non-Replied</option>
    </select>

    <button id="mark-all-read">Mark All As Read</button>
</section>

<section id="message-section">
    <div id="message-list">
        <ul>
            @forelse ($messages as $message)
                <li class="message-item {{ $message->is_read ? '' : 'unread' }} {{ $message->is_replied ? 'replied' : '' }}"
                    data-id="{{ $message->id }}"
                    data-message="{{ $message->message }}"
                    data-email="{{ $message->email }}">
                    <strong>{{ $message->name }}</strong>
                    <p>{{ Str::limit($message->message, 30) }}</p>
                    <div class="message-meta">
                        <span class="date">{{ $message->created_at->format('M d, Y') }}</span>
                        @if(!$message->is_read)
                            <span class="status unread">Unread</span>
                        @endif
                        @if($message->is_replied)
                            <span class="status replied">Replied</span>
                        @endif
                    </div>
                </li>
            @empty
                <li>No messages</li>
            @endforelse
        </ul>
        <button id="load-more" class="btn btn-primary">
            Load More
        </button>

    </div>

    <div id="message-view">
        <p>Select a message to view</p>
    </div>
</section>

@endsection


@push('styles')
<style>
    #filterAndSearch {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

#search-messages {
    flex-grow: 1;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

#filter {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

#mark-all-read {
    padding: 0.5rem 1rem;
    background-color: #19345e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#mark-all-read:hover {
    background-color: #154da3;
}

#message-section {
    display: grid;
    grid-template-columns: 30% 70%;
    height: 80vh;
}

#message-list {
    background-color: #f8f9fa;
    border-right: 1px solid #ddd;
    padding: 1rem;
    overflow-y: auto;
}

#message-view {
    background-color: #ffffff;
    padding: 1rem;
    overflow-y: auto;
}

/* Message container styling */
.message-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.message-item {
    padding: 1rem;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.message-item.active {
    background-color: #e9ecef;
}

.message-item.unread {
    font-weight: bold;
    border-left: 3px solid #154da3;
}

.message-item.replied {
    border-left: 3px solid #28a745;
}

.message-item:hover {
    background-color: #e9ecef;
}

.message-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.status {
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
}

.status.unread {
    background-color: #cce5ff;
    color: #004085;
}

.status.replied {
    background-color: #d4edda;
    color: #155724;
}

#load-more {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #19345e;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
}

#load-more:hover {
    background-color: #154da3;
}

/* Message View Styling */
.message-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.message-header h3 {
    margin: 0 0 0.5rem 0;
    color: #343a40;
}

.message-header .email {
    color: #6c757d;
    margin: 0;
}

.message-body {
    margin-bottom: 2rem;
    line-height: 1.6;
    flex-grow: 1;
}

.message-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.primary-btn {
    background-color: #19345e;
    color: white;
}

.primary-btn:hover:not(:disabled) {
    background-color: #154da3;
}

.secondary-btn {
    background-color: #6c757d;
    color: white;
}

.secondary-btn:hover:not(:disabled) {
    background-color: #5a6268;
}

.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover:not(:disabled) {
    background-color: #c82333;
}

#reply-form {
    border-top: 1px solid #ddd;
    padding-top: 1rem;
    margin-top: 1rem;
}

#reply-form h4 {
    margin-top: 0;
    color: #343a40;
}

#reply-message {
    width: 100%;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    gap: 0.5rem;
}

.success-notice {
    background-color: #d4edda;
    color: #155724;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    border-left: 4px solid #28a745;
}

/* Responsive styles */
@media (max-width: 768px) {
    #filterAndSearch {
        flex-direction: column;
        align-items: stretch;
    }

    #message-section {
        grid-template-columns: 1fr;
    }

    #message-list {
        border-right: none;
        border-bottom: 1px solid #ddd;
        max-height: 40vh;
    }
}
</style>
@endpush

@push('scripts')
<script>
   document.addEventListener('DOMContentLoaded', function () {
    const messageView = document.getElementById('message-view');
    const messageList = document.querySelector('#message-list ul');
    const searchInput = document.getElementById('search-messages');
    const filterSelect = document.getElementById('filter');
    const loadMoreButton = document.getElementById('load-more');
    const markAllReadButton = document.getElementById('mark-all-read');
    let currentPage = 1;
    let selectedMessageId = null;
    let isLoading = false;

    init();

    function init() {

        searchInput.addEventListener('input', filterMessages);
        filterSelect.addEventListener('change', filterMessages);
        if (loadMoreButton) loadMoreButton.addEventListener('click', loadMoreMessages);
        if (markAllReadButton) markAllReadButton.addEventListener('click', markAllAsRead);


        initializeMessageItemListeners();
        updateUIBasedOnMessages();
    }

    function updateUIBasedOnMessages() {
        const hasMessages = document.querySelectorAll('.message-item').length > 0;

        if (hasMessages) {
            displayFirstMessage();
        } else {
            messageView.innerHTML = '<p>No messages to display</p>';
            if (loadMoreButton) {
                loadMoreButton.style.display = 'none';
            }
        }
    }

    function initializeMessageItemListeners() {
        document.querySelectorAll('.message-item').forEach(item => {
            item.addEventListener('click', function() {
                selectMessageItem(this);
            });
        });
    }

    function selectMessageItem(item) {
        selectedMessageId = item.dataset.id;
        const { message, email } = item.dataset;
        const senderName = item.querySelector('strong').textContent;

        document.querySelectorAll('.message-item.active').forEach(el => el.classList.remove('active'));
        item.classList.add('active');

        displayMessageContent(selectedMessageId, senderName, email, message);

        if (item.classList.contains('unread')) {
            markAsRead(selectedMessageId);
            item.classList.remove('unread');
            const status = item.querySelector('.status.unread');
            if (status) status.remove();
        }
    }

    function displayFirstMessage() {
        const firstItem = document.querySelector('.message-item');
        if (firstItem) {
            selectMessageItem(firstItem);
        }
    }

    function displayMessageContent(id, name, email, message) {
        messageView.innerHTML = `
            <div class="message-container">
                <div class="message-header">
                    <h3>${name}</h3>
                    <p class="email">${email}</p>
                </div>
                <div class="message-body">
                    <p>${message}</p>
                </div>
                <div class="message-actions d-flex justify-content-center my-3">
                    <button id="reply-button" class="btn primary-btn">Reply</button>
                </div>
                <div id="reply-form" style="display:none;">
                    <h4>Reply to ${name}</h4>
                    <textarea id="reply-message" rows="6" placeholder="Type your reply here..."></textarea>
                    <div class="form-actions">
                        <button id="send-reply" class="btn primary-btn">Send Reply</button>
                        <button id="cancel-reply" class="btn danger-btn">Cancel</button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('reply-button').addEventListener('click', () => {
            const replybutton=document.getElementById('reply-button');
            replybutton.style.display="none";
            document.getElementById('reply-form').style.display = 'block';
            document.getElementById('reply-message').focus();
        });

        document.getElementById('cancel-reply').addEventListener('click', () => {
            const replybutton=document.getElementById('reply-button');
            replybutton.style.display="block";
            document.getElementById('reply-form').style.display = 'none';
        });

        document.getElementById('send-reply').addEventListener('click', function() {
            const replyText = document.getElementById('reply-message').value.trim();
            if (replyText) {
                this.disabled = true;
                this.textContent = 'Sending...';
                sendReply(id, email, replyText, this);
            } else {
                createSlider("warning", "Please Enter a reply Message", { title: "Warning" });
            }
        });
    }

    function updateMessageItemState(id, action) {
        const item = document.querySelector(`.message-item[data-id="${id}"]`);
        if (!item) return;

        if (action === 'read' && item.classList.contains('unread')) {
            item.classList.remove('unread');
            const status = item.querySelector('.status.unread');
            if (status) status.remove();
        } else if (action === 'replied' && !item.classList.contains('replied')) {
            item.classList.add('replied');
            const meta = item.querySelector('.message-meta');
            if (meta && !item.querySelector('.status.replied')) {
                const span = document.createElement('span');
                span.className = 'status replied';
                span.textContent = 'Replied';
                meta.appendChild(span);
            }
        }
    }

    function filterMessages() {
        if (isLoading) return;

        isLoading = true;
        currentPage = 1;
        const search = searchInput.value.toLowerCase();
        const filter = filterSelect.value;

        setLoadingState(true);

        fetch(`/admin/messages/filter?search=${encodeURIComponent(search)}&filter=${encodeURIComponent(filter)}&page=${currentPage}`, {
            headers: commonHeaders()
        })
            .then(res => res.json())
            .then(data => {
                updateMessageList(data.messages);
                toggleLoadMore(data.hasMorePages);
            })
            .catch(err => {
                console.error('Error filtering messages:', err);
                showErrorNotice('Failed to filter messages. Please try again.');
            })
            .finally(() => {
                setLoadingState(false);
                isLoading = false;
            });
    }

    function loadMoreMessages() {
        if (isLoading) return;

        isLoading = true;
        currentPage++;
        const search = searchInput.value.toLowerCase();
        const filter = filterSelect.value;

        loadMoreButton.disabled = true;
        const originalText = loadMoreButton.textContent;
        loadMoreButton.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Loading...
        `;

        fetch(`/admin/messages/filter?search=${encodeURIComponent(search)}&filter=${encodeURIComponent(filter)}&page=${currentPage}`, {
            headers: commonHeaders()
        })
            .then(res => res.json())
            .then(data => {
                appendToMessageList(data.messages);
                toggleLoadMore(data.hasMorePages);
            })
            .catch(err => {
                console.error('Error loading more messages:', err);
                showErrorNotice('Failed to load more messages. Please try again.');
                // Revert to previous page on error
                currentPage--;
            })
            .finally(() => {
                loadMoreButton.disabled = false;
                loadMoreButton.textContent = originalText;
                isLoading = false;
            });
    }

    function toggleLoadMore(hasMore) {
        if (!loadMoreButton) return;


        const hasMessages = document.querySelectorAll('.message-item').length > 0;


        loadMoreButton.style.display = (hasMessages && hasMore) ? 'block' : 'none';
    }

    function setLoadingState(isLoading) {
        if (isLoading) {
            // Add loading indicators if needed
            // For now, we'll just disable buttons
            if (loadMoreButton) loadMoreButton.disabled = true;
            if (markAllReadButton) markAllReadButton.disabled = true;
        } else {
            // Remove loading state
            if (loadMoreButton) loadMoreButton.disabled = false;
            if (markAllReadButton) markAllReadButton.disabled = false;
        }
    }

    function markAsRead(messageId) {
        fetch(`/admin/messages/${messageId}/read`, {
            method: 'POST',
            headers: commonHeaders()
        })
            .then(res => res.json())
            .then(data => console.log('Marked as read:', data))
            .catch(err => console.error('Error marking as read:', err));
    }

    function markAllAsRead() {
        if (isLoading) return;

        isLoading = true;
        markAllReadButton.textContent = 'Marking...';
        markAllReadButton.disabled = true;

        fetch('/admin/messages/mark-all-read', {
            method: 'POST',
            headers: commonHeaders()
        })
            .then(res => res.json())
            .then(() => {
                document.querySelectorAll('.message-item.unread').forEach(item => {
                    item.classList.remove('unread');
                    const status = item.querySelector('.status.unread');
                    if (status) status.remove();
                });

                showSuccessNotice('All messages have been marked as read');

                markAllReadButton.textContent = 'All Messages Read';
                setTimeout(() => {
                    markAllReadButton.textContent = 'Mark All As Read';
                    markAllReadButton.disabled = false;
                }, 2000);
            })
            .catch(err => {
                console.error('Error marking all messages as read:', err);
                showErrorNotice('Failed to mark all as read. Please try again.');
                markAllReadButton.textContent = 'Mark All As Read';
                markAllReadButton.disabled = false;
            })
            .finally(() => {
                isLoading = false;
            });
    }

    function sendReply(id, email, replyText, button) {
        fetch(`/admin/messages/${id}/reply`, {
            method: 'POST',
            headers: commonHeaders(),
            body: JSON.stringify({ email, reply: replyText })
        })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('reply-form').style.display = 'none';
                    document.getElementById('reply-message').value = '';

                    updateMessageItemState(id, 'replied');
                    showSuccessNotice('Reply sent successfully!');

                    button.disabled = false;
                    button.textContent = 'Send Reply';
                } else {
                    showErrorNotice('Failed to send reply: ' + data.message);
                    button.disabled = false;
                    button.textContent = 'Send Reply';
                }
            })
            .catch(err => {
                console.error('Error sending reply:', err);
                showErrorNotice('Failed to send reply. Please try again.');
                button.disabled = false;
                button.textContent = 'Send Reply';
            });
    }

    function showSuccessNotice(message) {
        const notice = document.createElement('div');
        notice.className = 'success-notice';
        notice.textContent = message;

        const container = messageView.querySelector('.message-container');
        if (container) {
            container.prepend(notice);
            setTimeout(() => notice.remove(), 3000);
        }
    }

    function showErrorNotice(message) {
        const notice = document.createElement('div');
        notice.className = 'error-notice';
        notice.style.backgroundColor = '#f8d7da';
        notice.style.color = '#721c24';
        notice.style.padding = '0.75rem';
        notice.style.marginBottom = '1rem';
        notice.style.borderRadius = '4px';
        notice.style.borderLeft = '4px solid #dc3545';
        notice.textContent = message;

        const container = messageView.querySelector('.message-container');
        if (container) {
            container.prepend(notice);
            setTimeout(() => notice.remove(), 3000);
        } else {

            messageView.prepend(notice);
            setTimeout(() => notice.remove(), 3000);
        }
    }

    function updateMessageList(messages) {
        if (!Array.isArray(messages) || messages.length === 0) {
            messageList.innerHTML = '<li>No messages found</li>';
            messageView.innerHTML = '<p>No messages to display</p>';
            if (loadMoreButton) {
                loadMoreButton.style.display = 'none';
            }
            return;
        }

        messageList.innerHTML = '';
        messages.forEach(msg => messageList.appendChild(createMessageItem(msg)));
        initializeMessageItemListeners();
        const firstItem = messageList.querySelector('.message-item');
        if (firstItem) {
            firstItem.click();
        }
    }

    function appendToMessageList(messages) {
        if (!Array.isArray(messages) || messages.length === 0) return;

        messages.forEach(msg => messageList.appendChild(createMessageItem(msg)));
        initializeMessageItemListeners();
    }

    function createMessageItem(msg) {
        const li = document.createElement('li');
        li.className = 'message-item';
        if (!msg.is_read) li.classList.add('unread');
        if (msg.is_replied) li.classList.add('replied');
        li.dataset.id = msg.id;
        li.dataset.message = msg.message;
        li.dataset.email = msg.email;

        const truncatedMessage = msg.message.length > 30
            ? msg.message.slice(0, 30) + '...'
            : msg.message;

        const formattedDate = new Date(msg.created_at).toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        li.innerHTML = `
            <strong>${msg.name}</strong>
            <p>${truncatedMessage}</p>
            <div class="message-meta">
                <span class="date">${formattedDate}</span>
                ${!msg.is_read ? '<span class="status unread">Unread</span>' : ''}
                ${msg.is_replied ? '<span class="status replied">Replied</span>' : ''}
            </div>
        `;

        return li;
    }

    function commonHeaders() {
        return {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        };
    }
});

</script>
@endpush
