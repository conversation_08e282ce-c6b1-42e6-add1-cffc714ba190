<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\Subscription;
use Stripe\Price;
use Stripe\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class PaymentService
{
    /**
     * Initialize the payment service
     *
     * @throws InvalidArgumentException if Stripe secret key is not configured
     */
    public function __construct()
    {
        $stripeSecret = config('services.stripe.secret');

        if (empty($stripeSecret)) {
            throw new InvalidArgumentException('Stripe secret key is not configured');
        }

        Stripe::setApiKey($stripeSecret);
    }


}
