.search-athlete {
	padding-block: 5rem;

	h2 {
		color: #19345e;
		font: 700 17px $inter;
	}

	.search {
		border: 1px solid #696969;
		border-radius: 50px;
		height: 45px;
		overflow: hidden;
		padding-inline: 40px;
		position: relative;

		input {
			border: 0 none;
			font: 18px $inter;
			height: 100%;
			width: 100%;

			&:focus {
				border: 0 none;
				box-shadow: none;
				outline: 0 none;
			}
		}

		.icon {
			background: #154da3;
			border: 0 none;
			border-radius: 50%;
			color: #fff;
			font-size: 14px;
			height: 32px;
			position: absolute;
			right: 7px;
			top: 5px;
			width: 32px;
		}
	}
}

.donate-wrap {
	h2 {
		color: #19345e;
		font: 700 17px $inter;
	}

	.check {
		position: relative;

		input {
			left: 0;
			opacity: 0;
			position: absolute;
			top: 0;
			visibility: hidden;

			&:checked {
				+ label {
					background: #f05522;
				}
			}
		}

		label {
			border-radius: 50px;
			cursor: pointer;
		}
	}

	.select-frequency {
		.check {
			input {
				&:checked {
					+ label {
						background: #f05522;
					}
				}
			}

			label {
				background: #154da3;
				font: 700 13px $anaheim;
				height: 36px;
				letter-spacing: 2px;
			}
		}
	}

	.select-package {
		.check {
			input {
				&:checked {
					+ label {
						background: #f05522;
						border-color: #f05522;
						color: #fff;

						span {
							color: #fff;
						}
					}
				}
			}

			label {
				background: #fff;
				border: 1px solid #696969;
				color: #f05522;
				font: 700 22px/1.5 $anaheim;

				span {
					color: #19345e;
					font: 700 17px $inter;
				}

				.tag {
					font-size: 14px;
					font-weight: 400;
				}
			}

			.most-selected {
				color: #f05522;
				margin-top: -15px;

				.icon {
					font-size: 36px;
				}

				h2 {
					color: #f05522;
					font: 600 12px $inter;
					letter-spacing: 1px;
				}
			}
		}
	}

	.input-price {
		max-width: 280px;

		.input-group {
			border: 1px solid #696969;
			border-radius: 30px;
			overflow: hidden;

			.input-group-text {
				background: transparent;
				border: 0 none;
				border-radius: 0;
			}

			.form-control {
				border: 0 none;

				&:focus {
					border: 0 none;
					box-shadow: none;
					outline: 0 none;
				}
			}
		}
	}
}
