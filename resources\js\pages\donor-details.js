import { createSlider } from '../appTwo.js';

document.addEventListener('DOMContentLoaded', function () {
  const customDonateBtn = document.querySelector('.custom-donate-btn');
  const customAmountInput = document.getElementById('custom-amount');
  const paymentSection = document.getElementById('payment-section');
  const paymentContainer = document.getElementById('payment-container');
  const finalAmountDisplay = document.getElementById('final-amount');
  const btnFinalAmountDisplay = document.getElementById('btn-final-amount');
  let paymentFrequency;

  const stripeKey = document.querySelector('meta[name="stripe-key"]')?.content;
  const stripe = Stripe(stripeKey);
  const elements = stripe.elements();

  const style = {
    base: {
      fontSize: '16px',
      color: '#32325d',
      fontFamily: '"Anaheim",-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      '::placeholder': { color: '#aab7c4' },
      iconColor: '#6a11cb',
      // Add these properties for better mobile display
      padding: '10px',
      lineHeight: '1.5',
      '::selection': {
        backgroundColor: 'rgba(106, 17, 203, 0.1)'
      }
    },
    invalid: {
      color: '#dc3545',
      iconColor: '#dc3545'
    }
  };

  // Create card with mobile-friendly options
  const cardElement = elements.create('card', {
    style,
    hidePostalCode: false,
    // Add these options for better mobile experience
    classes: {
      base: 'stripe-card-base',
      focus: 'stripe-card-focus',
      invalid: 'stripe-card-error'
    }
  });

  const mountStripeElement = () => {
    if (!paymentSection.classList.contains('d-none')) {
      setTimeout(() => {
        cardElement.mount('#card-element');
      }, 100);
    }
  };

  customDonateBtn.addEventListener("click", () => {
    // Get selected frequency
    const frequencyInput = document.querySelector('input[name="frequency"]:checked');
    if (!frequencyInput) {
      createSlider("Confirm Action", 'Please Select The Payment Frequency', { title: "Select Frequency" });
      return;
    }
    paymentFrequency = frequencyInput.value;

    // Get selected package or custom amount
    const packageInput = document.querySelector('input[name="package"]:checked');
    let amount = null;

    let customAmount = customAmountInput.value.trim();

    if (customAmount && !isNaN(customAmount) && parseFloat(customAmount) > 0) {

      if (packageInput) {
        packageInput.checked = false;
      }
      amount = parseFloat(customAmount);
    } else if (packageInput) {
      amount = parseFloat(packageInput.value);
    } else {
      createSlider("Confirm Action", 'Please enter a valid donation amount', { title: "Enter Amount" });
      return;
    }

    console.log("Final donation amount:", amount);


    // Show payment section and amount
    finalAmountDisplay.textContent = amount.toFixed(2);
    btnFinalAmountDisplay.textContent = amount.toFixed(2);
    paymentContainer.classList.remove('d-none');
    paymentSection.classList.remove('d-none');


    mountStripeElement();


    setTimeout(() => {
      paymentSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }, 100);


    console.log("Frequency:", paymentFrequency);
    console.log("Amount:", amount);
  });


  customAmountInput.addEventListener('input', function () {
    this.classList.remove('is-invalid');
  });


  const paymentForm = document.getElementById('payment-form');
  if (paymentForm) {

    let cardComplete = false;
    cardElement.on('change', function(event) {
      cardComplete = event.complete;
    });


    paymentForm.addEventListener('submit', async function(event) {

      event.preventDefault();


      const formData = new FormData(paymentForm);

      const submitButton = document.getElementById('submit-button');
      const formObject = Object.fromEntries(formData.entries());
      const  finalAmountDisplayText = finalAmountDisplay.textContent;
      formObject.amount = parseFloat(finalAmountDisplayText.replace(/,/g, ''));

      if (paymentFrequency) {
        formObject.paymentFrequency = paymentFrequency
      } else {
        createSlider("dark", "Please Select Payment Frequency.", {
          title: "Confirm Action"
        });
        scrollToElement('.frequency-card');
        return;
      }

      if (!this.checkValidity()) {
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
      }

      if (!cardComplete) {
        createSlider("error", "Please enter complete card details", {
          title: "Card Incomplete"
        });
        scrollToElement('#card-element');
        return;
      }



      showLoader("Processing Payment");
      submitButton.disabled = true;
      if (formObject.paymentFrequency === 'oneTime') {
        await handleOneTimePayment(formObject);
      } else {
        await handleRecurringSubscription(formObject);
      }



      //handle one time payments
      async function handleOneTimePayment(formObject) {

        const intentRoute = route('createPaymentIntentforOrgOneTime');
        try {
          const response = await fetch(intentRoute, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector(
                'meta[name="csrf-token"]').getAttribute(
                'content')
            },
            body: JSON.stringify(formObject)
          });

          const {
            clientSecret
          } = await response.json();

          const {
            paymentIntent,
            error
          } = await stripe.confirmCardPayment(clientSecret, {
            payment_method: {
              card: cardElement,
              billing_details: {
                name: `${formObject.firstName} ${formObject.lastName}`,
                email: formObject.email,
              }
            }
          });

          if (error) {
            // Record the payment failure
            await recordPaymentFailure({
              paymentIntentId: paymentIntent?.id || 'unknown',
              errorCode: error.code || 'unknown',
              errorMessage: error.message,
              errorType: error.type || 'card_error',
              amount: formObject.amount,
              customerEmail: formObject.email,
              customerName: `${formObject.firstName} ${formObject.lastName}`,
              donationType: 'organization_one_time',
              donationMessage: formObject.message,
              paymentMethodType: 'card',
              failureStage: 'client_side_payment_confirmation'
            });

            hideLoader();
            createSlider("error", error.message);
            submitButton.disabled = false;
            return;
          }

          formObject.paymentIntentId = paymentIntent.id;

          const storeTransactionRoute = route('storeTransactionForOrgOneTime');

          const storeResponse = await fetch(storeTransactionRoute, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector(
                'meta[name="csrf-token"]').getAttribute(
                'content')
            },
            body: JSON.stringify(formObject)
          });

          const result = await storeResponse.json();
          if (result.success) {
            showSuccessAnimation(() => {
              window.location.href = result.redirect_url;
            });
          } else {
            hideLoader();
            submitButton.disabled = false;
            createSlider("error", result.message);
          }
        } catch (err) {
          hideLoader();
          submitButton.disabled = false;
          console.error(err);
        }
      }

      // Helper function to record payment failures
      async function recordPaymentFailure(failureData) {
        try {
          const failureRoute = route('recordPaymentFailure');
          await fetch(failureRoute, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector(
                'meta[name="csrf-token"]').getAttribute(
                'content')
            },
            body: JSON.stringify(failureData)
          });
        } catch (err) {
          console.error('Failed to record payment failure:', err);
        }
      }

      async function handleRecurringSubscription(formObject) {
        const subscriptionRoute = route('createSubscriptionForOrg');
        try {
          // Step 1: Create the customer and initial payment intent
          const response = await fetch(subscriptionRoute, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector(
                'meta[name="csrf-token"]').getAttribute(
                'content')
            },
            body: JSON.stringify(formObject)
          });

          const {
            clientSecret,
            customerId,
            priceId,
            paymentIntentId,
            error
          } = await response.json();

          if (error) {
            hideLoader();
            createSlider("error", error);
            submitButton.disabled = false;
            return;
          }

          // Step 2: Confirm the payment using the PaymentIntent
          const {
            error: paymentError,
            paymentIntent
          } = await stripe.confirmCardPayment(clientSecret, {
            payment_method: {
              card: cardElement,
              billing_details: {
                name: `${formObject.firstName} ${formObject.lastName}`,
                email: formObject.email,
              }
            },
          });

          // Handle error during payment confirmation
          if (paymentError) {
            // Record the payment failure
            await recordPaymentFailure({
              paymentIntentId: paymentIntentId,
              errorCode: paymentError.code || 'unknown',
              errorMessage: paymentError.message,
              errorType: paymentError.type || 'card_error',
              amount: formObject.amount,
              customerEmail: formObject.email,
              customerName: `${formObject.firstName} ${formObject.lastName}`,
              donationType: 'organization_subscription',
              donationMessage: formObject.message,
              paymentMethodType: 'card',
              failureStage: 'client_side_payment_confirmation'
            });

            hideLoader();
            createSlider("error", paymentError.message);
            submitButton.disabled = false;
            return;
          }

          // Step 3: Create the subscription using the confirmed payment method
          const confirmSubscriptionRoute = route('confirmSubscription');
          const confirmResponse = await fetch(confirmSubscriptionRoute, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-TOKEN': document.querySelector(
                'meta[name="csrf-token"]').getAttribute(
                'content')
            },
            body: JSON.stringify({
              paymentMethodId: paymentIntent.payment_method,
              customerId: customerId,
              priceId: priceId,
              paymentIntentId: paymentIntentId,
              amount: formObject.amount,
              paymentFrequency: formObject.paymentFrequency
            })
          });

          const confirmResult = await confirmResponse.json();
          if (confirmResult.success) {
            // Step 4: Store the transaction information
            formObject.paymentIntentId = paymentIntentId;
            formObject.customerId = customerId;
            formObject.subscriptionId = confirmResult.subscription;

            const storeTransactionRoute = route(
              'storeTransactionForOrgDonation');
            const storeResponse = await fetch(storeTransactionRoute, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector(
                  'meta[name="csrf-token"]').getAttribute(
                  'content')
              },
              body: JSON.stringify(formObject)
            });

            const result = await storeResponse.json();
            if (result.success) {
              showSuccessAnimation(() => {
                window.location.href = result.redirect_url;
              });
            } else {
              hideLoader();
              createSlider("error", result.message);
            }
          } else {
            hideLoader();
            createSlider("error", confirmResult.message);
          }
        } catch (err) {
          hideLoader();
          submitButton.disabled = false;
          console.error(err);
        }
      }
    });
  }

  // searching functionality
  let searchButton = document.getElementById("searchButton");
  let searchResults = document.getElementById("searchResults");
  let input = document.getElementById("search");

  searchButton.addEventListener("click", function() {
    let inputValue = input.value.trim();

    if (inputValue === "") {
      return;
    }

    let url = route("search-athelete-for-donation", {
      search: inputValue
    });
    fetchUsers(url);
  });

  function fetchUsers(url) {
    // Create a container with fixed height to prevent layout shifts
    searchResults.innerHTML = `
      <div class="results-container" style="min-height: 600px; position: relative;">
        <div class="d-flex justify-content-center align-items-center" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
          <div class="spinner-border" role="status" style="color: #19345E; width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    `;

    fetch(url)
      .then((response) => response.json())
      .then((data) => {
        // Create a container with the same min-height
        let resultsContainer = `<div class="results-container" style="min-height: 600px;">`;
        let users = data.data;

        if (data.next_page_url || data.prev_page_url) {
          resultsContainer += `
          <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 pagination-container p-3 rounded shadow-sm"
               style="background: linear-gradient(145deg, #ffffff, #f8f8ff);">
              <div class="mb-3 mb-md-0">
                  <span class="text-muted">Showing results ${data.from || 0} - ${data.to || 0} of ${data.total || 0}</span>
              </div>
              <div class="pagination-buttons d-flex gap-2 w-100 justify-content-center justify-content-md-end">`;

          if (data.prev_page_url) {
            resultsContainer += `
            <button class="btn prev-page" data-url="${data.prev_page_url}"
                    style="background: rgba(25,52,94,0.1); color: #19345E; border: none; border-radius: 20px; padding: 8px 16px; flex: 1 1 auto; max-width: 120px;">
                <i class="fas fa-chevron-left me-1"></i> Previous
            </button>`;
          }

          if (data.next_page_url) {
            resultsContainer += `
            <button class="btn next-page" data-url="${data.next_page_url}"
                    style="background: rgba(25, 52, 94, 0.1); color: #19345E; border: none; border-radius: 20px; padding: 8px 16px; flex: 1 1 auto; max-width: 120px;">
                Next <i class="fas fa-chevron-right ms-1"></i>
            </button>`;
          }

          resultsContainer += `
              </div>
          </div>`;
        }

        if (users.length === 0) {
          resultsContainer += `
          <div class="alert mt-2 alert-warning p-4 text-center rounded-lg shadow-sm" role="alert">
              <i class="fas fa-exclamation-circle fa-2x mb-3" style="color: #f39c12;"></i>
              <h4 class="alert-heading">No Results Found</h4>
              <p class="mb-0">We couldn't find any athletes matching your search criteria. Please try different keywords.</p>
          </div>`;
        } else {
          resultsContainer += `<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 mb-4">`;

          users.forEach((user) => {
            resultsContainer += `
          <div class="col-sm-6 col-lg-4 col-xl-3 mt-5">
              <a href="${user.donationPageUrl}" class="text-decoration-none w-100 h-100">
                  <div class="leader_box d-flex flex-column text-center align-items-center h-100">
                      <div class="img mb-3">
                          <img src="${user.profile_photo}" alt="${user.name}" />
                      </div>
                      <div class="text">
                          <h2 class="mb-0">${user.name}</h2>
                          <h3 class="price">
                              $${(user.total_collected || 0).toLocaleString()} out of
                              $${(user.fundraising_goal || 0).toLocaleString()}
                          </h3>
                      </div>
                  </div>
              </a>
          </div>
          `;
          });

          resultsContainer += `</div>`;
        }

        resultsContainer += `</div>`;
        searchResults.innerHTML = resultsContainer;

        // Add event listeners to pagination buttons
        document.querySelectorAll(".prev-page, .next-page").forEach(button => {
          button.addEventListener("click", function() {
            let newUrl = this.getAttribute("data-url");
            fetchUsers(newUrl);
          });
        });

        // Add hover effects to athlete cards
        document.querySelectorAll('.athlete-card').forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(106,17,203,0.15)';
          });

          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
          });
        });
      })
      .catch((error) => {
        searchResults.innerHTML = `
        <div class="alert alert-danger p-4 text-center rounded-lg shadow-sm" role="alert">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4 class="alert-heading">Error</h4>
            <p class="mb-0">Sorry, something went wrong: ${error.message}</p>
        </div>`;
      });
  }

  function isElementInViewport(el) {
    const rect = el.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  function scrollToElement(selector, block = 'center') {
    const element = document.querySelector(selector);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block
      });
    }
  }

  function showLoader(message) {
    if (!document.getElementById('full-screen-loader')) {
      const loader = document.createElement('div');
      loader.id = 'full-screen-loader';
      loader.innerHTML = `
        <div class="loader-icon">
          <div class="spinner-border" role="status"></div>
        </div>
        <p>${message}</p>
      `;
      document.body.appendChild(loader);
    }
  }
  // Hide Loader
  function hideLoader() {
    const loader = document.getElementById('full-screen-loader');
    if (loader) loader.remove();
  }

  function showSuccessAnimation(callback) {
    const loader = document.getElementById('full-screen-loader');
    if (loader) {
      loader.innerHTML = `
        <div class="success-animation">
          🎉
        </div>
        <p>Thank You for Your Donation!</p>
      `;
      setTimeout(() => {
        callback();
      }, 2000);
    }
  }
});
