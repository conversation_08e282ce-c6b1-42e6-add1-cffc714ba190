<?php $__env->startSection('title', '2025 ASFL Gauntlet | Donate'); ?>

<?php $__env->startSection('meta_description', 'Help fuel the fight against cancer—donate to the ASFL Gauntlet and make a lasting impact through this powerful basketball fundraiser.'); ?>

<meta name="stripe-key" content="<?php echo e(config('services.stripe.key')); ?>">

<?php $__env->startSection('content'); ?>


<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Donate</h1>
        </div>
    </div>
</section>
<section class="search-athlete sec leaderboard pt-3">
    <div class="container">
        <h2 class="text-center">Find an Athlete</h2>
        <div class="search mt-2 mb-0">
            <input type="text" id="search" placeholder="Search Athlete By Name or Town" />
            <div class="icon d-flex align-items-center justify-content-center"><i class="bi bi-search"></i></div>
        </div>
        <div class="text-center mt-4">
            <button id="searchButton" class="cta orange hoverbutton" style="border:none;">
                Search
            </button>
        </div>


    <div id="searchResults" class="row">
    </div>
    </div>
</section>
<section class="donate-wrap">
    <div class="container">
        <div class="donate-form">
            <div class="select-frequency text-center mb-5">
                <h2 class="mb-0">Choose Your Donation Frequency</h2>
                <div class="select-row select-cta row justify-content-center mt-4">
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="oneTime" id="onetime" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="onetime">One Time</label>
                        </div>
                    </div>


                    
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="monthly" id="monthly" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="monthly">Monthly</label>
                        </div>
                    </div>



                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="quarterly" id="quarterly" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="quarterly">Quarterly</label>
                        </div>
                    </div>
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="annually" id="annually" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="annually">Annually</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="select-package select-cta text-center mb-5">
                <div class="select-package row">
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="25" id="bronze" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="bronze">$25 <span>Bronze</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="50" id="silver" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="silver">$50 <span>Silver</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="100" id="gold" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="gold">$100 <span>Gold</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="250" id="platinum" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="platinum">$250 <span>Platinum</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="500" id="diamond" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="diamond">$500 <span>Diamond</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="1000" id="philanthropist" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="philanthropist">$1000 <span>Philanthropist</span></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mb-5">
                <h2 class="mb-3">Or Enter Your Own Amount</h2>
                <div class="input-price mx-auto">
                    <label class="visually-hidden" for="amount">Amount</label>
                    <div class="input-group">
                        <div class="input-group-text">$</div>
                        <input class="form-control" type="number" id="custom-amount" inputmode="numeric" />
                    </div>
                </div>
            </div>
            <div class="text-center mb-5">
                <button class="cta orange border-0 hoverbutton custom-donate-btn">Continue</button>
            </div>
        </form>
    </div>
</section>


    <section>
        <div id="payment-container" class="donation-wrapper d-none">
            <div id="payment-section" class="donation-container d-none">
                <div class="donation-header">
                    <h2 class="donation-title">Make a Difference</h2>
                    <p class="donation-subtitle">Your generosity can change lives</p>
                </div>

                <form class="donation-form" id= "payment-form" method='POST'>
                    <?php echo csrf_field(); ?>
                    <div class="form-grid">
                        <div class="form-section personal-info">
                            <div class="section-header">
                                <i class="fas fa-user-circle"></i>
                                <h3>Personal Details</h3>
                            </div>

                            <div class="name-fields">
                                <div class="form-group">
                                    <label for="firstName">First Name <span class="required">*</span></label>
                                    <input type="text" id="firstName" name="firstName" required class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name <span class="required">*</label>
                                    <input type="text" id="lastName" name="lastName"  required class="form-control">
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="email">Email Address <span class="required">*</span></label>
                                <input type="email" id="email" name="email" required class="form-control">
                            </div>

                            <div class="name-fields">
                                <div class="form-group">
                                    <label for="city">City <span class="required">*</span></label>
                                    <input type="text" id="city" name="city" required class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="state">State <span class="required">*</span></label>
                                    <input type="text" id="state" name="state" class="form-control">
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="billing-address"> Billing Address <span class="required">*</span></label>
                                <input type="text" id="billing-address" name="address" class="form-control">
                            </div>
                        </div>

                        <div class="form-section payment-details">
                            <div class="section-header">
                                <i class="fas fa-credit-card"></i>
                                <h3>Payment Information</h3>
                            </div>

                            <div class="stripe-card-wrapper">
                                <div id="card-element" class="stripe-card-element"></div>
                            </div>

                            <div id="card-errors" class="error-message"></div>

                            <div class="donation-amount">
                                <span class="amount-label">Donation Amount</span>
                                <div class="amount-display">$<span id="final-amount">0</span></div>
                            </div>
                        </div>

                        <div class="form-section message-section">
                            <div class="section-header">
                                <i class="fas fa-comment-dots"></i>
                                <h3>Your Message</h3>
                            </div>

                            <div class="form-group">
                                <label for="donationMessage">Share Your Inspiration <span
                                        class="optional">(Optional)</span></label>
                                <textarea id="donationMessage" name="message" class="form-control" rows="4"
                                    placeholder="Write a message of hope..."></textarea>
                            </div>
                        </div>

                        <div class="form-section privacy-section">
                            <div class="section-header">
                                <i class="fas fa-shield-alt"></i>
                                <h3>Privacy Options</h3>
                            </div>

                            <div class="privacy-options">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="anonymousToPublic" name="anonymousToPublic"
                                        class="custom-checkbox">
                                    <label for="anonymousToPublic">Visible to recipient, hidden from public</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="checkbox" id="completeAnonymity" name="completeAnonymity"
                                        class="custom-checkbox">
                                    <label for="completeAnonymity">Complete anonymity</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="donation-submit">
                        <button type="submit" class="btn-donate" id="submit-button">
                            <span class="btn-text">Donate $<span id="btn-final-amount">0</span></span>
                            <span class="btn-icon">
                                <i class="fas fa-heart"></i>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('styles'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/pages/card-details.css'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://js.stripe.com/v3/"></script>

      <?php echo app('Illuminate\Foundation\Vite')('resources/js/pages/donor-details.js'); ?>


    
<?php $__env->stopPush(); ?>


















<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/home/<USER>/ ?>