<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('donors', function (Blueprint $table) {
            $table->id();
            $table->string('name')->default('Anonymous');
            $table->string('email')->nullable();
            $table->string('address')->nullable();
            $table->string('message_for_fundraiser')->nullable();
            $table->decimal('amount_donate', 10, 2);
            $table->boolean('anonymous_for_public')->default(0);
            $table->boolean('anonymous_for_all')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('donors');
    }
};
