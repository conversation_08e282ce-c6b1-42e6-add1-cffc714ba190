@media (max-width: 991px) {
    .header {
        position: relative;
        z-index: 1000;
    }
}
@media (max-width: 768px) {
    .header .brand img {
        height: auto;
        width: 80px;
    }
}
@media (max-width: 991px) {
    .header .site_nav {
        background: #fff;
        border-bottom: 1px solid #ccc;
        left: 0;
        position: absolute;
        top: 100%;
        width: 100%;
    }
}
.header .site_nav li {
    color: #000;
    font: 700 16px/1 "Anaheim", sans-serif;
}
@media (max-width: 991px) {
    .header .site_nav li {
        border-top: 1px solid #ccc;
        padding-block: 0.8rem;
    }
}
.header .site_nav li a {
    padding: 0.5rem 1rem;
}
@media (min-width: 992px) {
    .header .site_nav li a {
        border: 1px solid #fff;
        border-radius: 20px;
    }
}
@media (min-width: 992px) {
    .header .site_nav li a:hover {
        color: #f05522;
    }
}
.header .site_nav li.active a {
    color: #f05522;
    font-weight: 800;
}
@media (min-width: 992px) {
    .header .site_nav li.active a {
        border-color: #f05522;
    }
}
@media (min-width: 992px) {
    .header .site_nav li + li {
        margin-left: 0.625rem;
    }
}
.header .site_nav a {
    color: inherit;
    text-decoration: none;
}
.header .call_nav {
    background-color: #f05522;
    border-radius: 0.5rem;
    cursor: pointer;
    height: 36px;
    padding: 0.4rem;
    width: 36px;
}
.header .call_nav .line {
    background-color: #fff;
    border-radius: 5px;
    display: block;
    height: 3px;
    width: 100%;
}
.header .call_nav .line + .line {
    margin-top: 5px;
}
.header .call_nav.is-active .line:nth-child(1) {
    -webkit-transform: translateY(6px) rotate(45deg);
    transform: translateY(6px) rotate(45deg);
}
.header .call_nav.is-active .line:nth-child(2) {
    opacity: 0;
}
.header .call_nav.is-active .line:nth-child(3) {
    margin-top: 0;
    -webkit-transform: translateY(-5px) rotate(-45deg);
    transform: translateY(-5px) rotate(-45deg);
}
.header .call_nav:hover {
    cursor: pointer;
}
@media (min-width: 768px) {
    .hero {
        position: relative;
    }
}
.hero__text {
    padding: 1.25rem 0;
}
@media (min-width: 768px) {
    .hero__text {
        left: 0;
        padding: 2.5rem 0 0;
        position: absolute;
        top: 0;
        width: 100%;
    }
}
.hero h1 {
    color: #000;
    font: 46px/0.9 "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    .hero h1 {
        font-size: 46px;
    }
}
@media (min-width: 1240px) {
    .hero h1 {
        font-size: 70px;
    }
}
.hero h1 strong {
    color: #f05522;
    font-size: 110%;
}
.hero p {
    color: #000;
    font-size: 18px;
    line-height: 1.5;
}
@media (min-width: 768px) {
    .hero p {
        font-size: 14px;
        text-wrap: balance;
    }
}
@media (min-width: 1240px) {
    .hero p {
        font-size: 20px;
        text-wrap: balance;
    }
}
.hero .cta-row {
    margin-top: 1.25rem;
}
@media (min-width: 1240px) {
    .hero .cta-row {
        margin-top: 2.25rem;
    }
}
.hero--title .bg-blue {
    background: #19345e;
    padding-block: 1.5rem;
}
@media (min-width: 768px) {
    .hero--title .bg-blue {
        padding-block: 2.25rem;
    }
}
.hero--title h1 {
    color: #fff;
    font: 500 42px/1 "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    .hero--title h1 {
        font-size: 52px;
    }
}
@media (min-width: 768px) {
    .hording {
        position: relative;
    }
}
@media (min-width: 768px) {
    .hording__text {
        left: 0;
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: 100%;
    }
}
.hording__text h2 {
    color: #19345e;
}
.hording__text h2 strong {
    color: #f05522;
    font-size: 115%;
}
.hording__text p {
    color: #363636;
}
.hording__text .p-size {
    max-width: 475px;
}
.achieve-goal .bg-blue {
    background: #19345e;
    padding-block: 1.25rem 2.5rem;
}
@media (min-width: 768px) {
    .achieve-goal .bg-blue {
        padding-block: 2.25rem 3.5rem;
    }
}
.achieve-goal .meta-timeline h2 {
    color: #fff;
    font: 700 16px "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    .achieve-goal .meta-timeline h2 {
        font-size: 20px;
    }
}
@media (min-width: 1240px) {
    .achieve-goal .meta-timeline h2 {
        font-size: 28px;
    }
}
.achieve-goal .meta-timeline h2 strong {
    color: #f05522;
    font-size: 140%;
    font-weight: 700;
}
.achieve-goal .time-line .meta-list .label {
    color: #fff;
    font: 700 12px "Inter", sans-serif;
    position: relative;
}
@media (min-width: 768px) {
    .achieve-goal .time-line .meta-list .label {
        font-size: 15px;
    }
}
.achieve-goal .time-line .meta-list .label:after {
    background: #fff;
    content: "";
    height: 45px;
    left: 50%;
    position: absolute;
    top: calc(100% + 10px);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 2px;
}
@media (min-width: 768px) {
    .achieve-goal .time-line .meta-list .label:after {
        width: 3px;
    }
}
.achieve-goal .time-line .meta-list .label.first:after {
    left: 2px;
}
.achieve-goal .time-line .meta-list .label.last:after {
    left: calc(100% - 2px);
}
.achieve-goal .time-line .bar {
    background: #19345e;
    border: 2px solid #fff;
    border-radius: 50px;
    height: 50px;
    padding: 0.5rem 0.8rem;
    position: relative;
    z-index: 1;
}
@media (min-width: 768px) {
    .achieve-goal .time-line .bar {
        border-width: 3px;
        height: 60px;
        padding: 0.75rem 1rem;
    }
}
.achieve-goal .time-line .bar .fill {
    background: #f05522;
    border-radius: 30px;
    height: 100%;
    padding: 0 1rem;
}
.achieve-goal .time-line .bar .sum {
    color: #fff;
    font: 700 18px/1 "Inter", sans-serif;
}
@media (min-width: 768px) {
    .achieve-goal .time-line .bar .sum {
        font-size: 24px;
    }
}
.leaderboard .leader_box .img {
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    height: 220px;
    overflow: hidden;
    width: 220px;
}
.leaderboard .leader_box .img img {
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%;
}
.leaderboard .leader_box h2 {
    color: #f05522;
    font: 700 22px "Anaheim", sans-serif;
}
.leaderboard .leader_box h3 {
    color: #19345e;
    font: 700 18px "Anaheim", sans-serif;
}
.leaderboard .leader_box a {
    color: inherit;
    text-decoration: none;
}
.total-page {
    font: 20px "Inter", sans-serif;
    margin-top: 2rem;
}
.recent-activities .activity-head {
    color: #19345e;
    font: 700 13px "Inter", sans-serif;
}
.recent-activities .activity-row {
    background: #e4eaf4;
    border-radius: 30px;
}
@media (min-width: 768px) {
    .recent-activities .activity-row {
        border-radius: 100px;
        min-height: 6rem;
    }
}
.recent-activities .activity-row .label {
    color: #154da3;
    font: 700 18px/1.25 "Anaheim", sans-serif;
}
@media (max-width: 767px) {
    .recent-activities .activity-row .label {
        padding-left: 30%;
        position: relative;
    }
    .recent-activities .activity-row .label:after {
        color: #19345e;
        content: attr(title);
        font: 700 13px "Inter", sans-serif;
        left: 0;
        padding-left: 1.25rem;
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        width: 30%;
    }
}
@media (min-width: 768px) {
    .recent-activities .activity-row .label {
        font-size: 22px;
    }
}
.recent-activities .cta-row .cta {
    min-width: 46px;
    width: 46px;
}
.make-difference .bg-blue {
    background: #19345e;
    padding-block: 4rem;
}
.make-difference h2 {
    color: #fff;
}
.make-difference h2 strong {
    color: #f05522;
    font-weight: 700;
}
.make-difference p {
    color: #fff;
}
form .form-label {
    color: #19345e;
    font: 700 16px "Inter", sans-serif;
}
@media (min-width: 768px) {
    form .form-label {
        font-size: 17px;
    }
}
form .form-control {
    border-radius: 30px;
    height: 46px;
}
form .form-control[type="file"] {
    line-height: 33px;
}
form .form-control,
form .form-check-input[type="checkbox"] {
    border-color: dimgray;
}
form .form-check-label {
    color: #363636;
    font: 15px "Inter", sans-serif;
}
@media (min-width: 768px) {
    form .form-check-label {
        font-size: 17px;
    }
}
form textarea.form-control {
    min-height: 120px;
}
form input::-webkit-outer-spin-button,
form input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
form input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
}
form a {
    color: #000;
}
.search-athlete {
    padding-block: 5rem;
}
.search-athlete h2 {
    color: #19345e;
    font: 700 17px "Inter", sans-serif;
}
.search-athlete .search {
    border: 1px solid dimgray;
    border-radius: 50px;
    height: 45px;
    overflow: hidden;
    padding-inline: 40px;
    position: relative;
}
.search-athlete .search input {
    border: 0 none;
    font: 18px "Inter", sans-serif;
    height: 100%;
    width: 100%;
}
.search-athlete .search input:focus {
    border: 0 none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0 none;
}
.search-athlete .search .icon {
    background: #154da3;
    border: 0 none;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
    height: 32px;
    position: absolute;
    right: 7px;
    top: 5px;
    width: 32px;
}
.donate-wrap h2 {
    color: #19345e;
    font: 700 17px "Inter", sans-serif;
}
.donate-wrap .check {
    position: relative;
}
.donate-wrap .check input {
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    visibility: hidden;
}
.donate-wrap .check input:checked + label {
    background: #f05522;
}
.donate-wrap .check label {
    border-radius: 50px;
    cursor: pointer;
}
.donate-wrap .select-frequency .check input:checked + label {
    background: #f05522;
}
.donate-wrap .select-frequency .check label {
    background: #154da3;
    font: 700 13px "Anaheim", sans-serif;
    height: 36px;
    letter-spacing: 2px;
}
.donate-wrap .select-package .check input:checked + label {
    background: #f05522;
    border-color: #f05522;
    color: #fff;
}
.donate-wrap .select-package .check input:checked + label span {
    color: #fff;
}
.donate-wrap .select-package .check label {
    background: #fff;
    border: 1px solid dimgray;
    color: #f05522;
    font: 700 22px/1.5 "Anaheim", sans-serif;
}
.donate-wrap .select-package .check label span {
    color: #19345e;
    font: 700 17px "Inter", sans-serif;
}
.donate-wrap .select-package .check label .tag {
    font-size: 14px;
    font-weight: 400;
}
.donate-wrap .select-package .check .most-selected {
    color: #f05522;
    margin-top: -15px;
}
.donate-wrap .select-package .check .most-selected .icon {
    font-size: 36px;
}
.donate-wrap .select-package .check .most-selected h2 {
    color: #f05522;
    font: 600 12px "Inter", sans-serif;
    letter-spacing: 1px;
}
.donate-wrap .input-price {
    max-width: 280px;
}
.donate-wrap .input-price .input-group {
    border: 1px solid dimgray;
    border-radius: 30px;
    overflow: hidden;
}
.donate-wrap .input-price .input-group .input-group-text {
    background: rgba(0, 0, 0, 0);
    border: 0 none;
    border-radius: 0;
}
.donate-wrap .input-price .input-group .form-control {
    border: 0 none;
}
.donate-wrap .input-price .input-group .form-control:focus {
    border: 0 none;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: 0 none;
}
.profile-hero {
    background: #e4eaf4;
    border-top: 1.25rem solid #19345e;
    padding-block: 3rem;
}
.profile-hero .img-player {
    border-radius: 50%;
    isolation: isolate;
    overflow: hidden;
    position: relative;
}
.profile-hero .img-player:before {
    content: "";
    display: block;
    padding-top: 100%;
}
.profile-hero .img-player img {
    height: 100%;
    left: 0;
    -o-object-fit: cover;
    object-fit: cover;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 1;
}
.profile-hero h1 {
    color: #19345e;
    font: 60px/0.9 "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    .profile-hero h1 {
        font-size: 90px;
    }
}
.profile-hero h1 strong {
    color: #f05522;
    font-size: 110%;
}
.select-level {
    padding-block: 6rem;
}
.your-donation {
    background: #e4eaf4;
    padding-block: 4.5rem;
}
.your-donation .box h2 {
    color: #f05522;
    font: 700 22px "Anaheim", sans-serif;
}
.your-donation .box p {
    color: #676464;
    font: 14px/1.5 "Inter", sans-serif;
}
.about-hero {
    background: #f05522;
}
.about-text {
    padding-top: 2.5rem;
}
@media (min-width: 768px) {
    .about-text {
        padding-top: 3rem;
    }
}
.about-text h1 {
    color: #19345e;
    font: 60px/1 "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    .about-text h1 {
        font-size: 90px;
    }
}
.about-text p {
    color: #000;
}
.about-text a {
    color: inherit;
}
.card-about {
    padding-block: 0 2.5rem;
}
@media (min-width: 768px) {
    .card-about {
        padding-block: 3rem 6rem;
    }
}
.card-about .box-meta {
    background: #e4eaf4;
    border-radius: 50px;
    height: 100%;
    padding: 2rem 1.5rem;
}
.card-about .box-meta h2 {
    color: #f05522;
    font: 700 32px "Anaheim", sans-serif;
}
.card-about .box-meta h3 {
    color: #19345e;
    font: 700 17px/1.5 "Inter", sans-serif;
}
.card-about .box-meta p {
    color: #000;
}
.team-time {
    padding-block: 2.5rem;
}
@media (min-width: 768px) {
    .team-time {
        padding-block: 6rem;
    }
}
.team-time h2 {
    color: #154da3;
    font-size: 40px;
    font-weight: 700;
}
.team-time p {
    color: #000;
}
.fund-riser {
    padding-block: 2.5rem;
}
@media (min-width: 768px) {
    .fund-riser {
        padding-block: 6rem;
    }
}
.fund-riser h2 {
    color: #154da3;
    font-size: 40px;
    font-weight: 700;
}
.fund-riser p {
    color: #000;
}
.fund-riser .copy p {
    color: #154da3;
    font-style: italic;
}
h2 {
    font: 42px/1 "Anaheim", sans-serif;
}
@media (min-width: 768px) {
    h2 {
        font-size: 34px;
    }
}
@media (min-width: 1240px) {
    h2 {
        font-size: 52px;
    }
}
p,
li,
.p {
    color: #19345e;
    font: 16px/1.65 "Inter", sans-serif;
}
@media (min-width: 768px) {
    p,
    li,
    .p {
        font-size: 14px;
    }
}
@media (min-width: 1240px) {
    p,
    li,
    .p {
        font-size: 17px;
    }
}
p:last-of-type {
    margin-bottom: 0;
}
.brand-text {
    color: #f05522 !important;
}
.footer .bg-orange {
    background: #f05522;
}
.footer .hash_logo {
    color: #fff;
    font: 52px/1 "Anaheim", sans-serif;
}
.footer .social a {
    color: #fff;
    font-size: 30px;
}
.footer .social li + li {
    margin-left: 1rem;
}
@media (min-width: 768px) {
    .footer .copyright {
        left: 50%;
        position: absolute;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        width: auto;
    }
}
.footer .copyright p {
    color: #fff;
    font: 14px/2 "Inter", sans-serif;
}
.cta {
    border-radius: 20px;
    color: #fff;
    font: 700 14px "Anaheim", sans-serif;
    letter-spacing: 2px;
    min-width: 160px;
}
.cta.orange {
    background: #f05522;
}
.cta.blue {
    background: #154da3;
}
.cta-row .cta + .cta {
    margin-left: 1rem;
}
@media (min-width: 768px) {
    .cta-row .cta + .cta {
        margin-left: 2rem;
    }
}
.sec {
    padding-block: 2.5rem;
}
@media (min-width: 768px) {
    .sec {
        padding-block: 5rem;
    }
}
.sec .head h2 {
    color: #154da3;
    font: 700 40px "Anaheim", sans-serif;
}
.sec .head h3 {
    color: #363636;
    font: 24px "Inter", sans-serif;
}
.content-row {
    padding-block: 3rem;
}
.contact-meta h2 {
    color: #f05522;
    font: 700 22px "Inter", sans-serif;
}
.contact-meta p {
    color: #000;
    font: 22px "Inter", sans-serif;
}
.contact-meta a {
    color: inherit;
    text-decoration: none;
}
body {
    font-family: "Inter", sans-serif;
}
.container {
    max-width: 100%;
}
@media (min-width: 768px) {
    .container {
        max-width: 95%;
    }
}
@media (min-width: 1240px) {
    .container {
        max-width: 1030px;
    }
}
.container.wide {
    max-width: 1304px;
}
@media (max-width: 1240px) {
    .container.wide {
        padding: 0;
    }
} /*# sourceMappingURL=app.min.css.map */
