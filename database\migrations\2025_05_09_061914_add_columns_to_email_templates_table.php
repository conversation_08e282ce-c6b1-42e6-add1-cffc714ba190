<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('email_templates', 'name')) {
                $table->string('name')->after('id');
            }
            if (!Schema::hasColumn('email_templates', 'category')) {
                $table->string('category')->after('name');
            }
            if (!Schema::hasColumn('email_templates', 'description')) {
                $table->text('description')->nullable()->after('category');
            }
            if (!Schema::hasColumn('email_templates', 'content')) {
                $table->text('content')->after('description');
            }
            if (!Schema::hasColumn('email_templates', 'subject')) {
                $table->string('subject')->nullable()->after('content');
            }
            if (!Schema::hasColumn('email_templates', 'recipient_group')) {
                $table->string('recipient_group')->nullable()->after('subject');
            }
            if (!Schema::hasColumn('email_templates', 'is_template')) {
                $table->boolean('is_template')->default(true)->after('recipient_group');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_templates', function (Blueprint $table) {
            // Only drop columns if they exist
            $columns = [
                'name',
                'category',
                'description',
                'content',
                'subject',
                'recipient_group',
                'is_template'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('email_templates', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
