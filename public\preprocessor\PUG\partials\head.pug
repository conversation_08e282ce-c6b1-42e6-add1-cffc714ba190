mixin metaTag(name, content)
    meta(name=name, content=content)

mixin linkStylesheet(href, integrity=null, crossorigin=null, referrerpolicy=null)
    link(rel='stylesheet', href=href, integrity=integrity, crossorigin=crossorigin, referrerpolicy=referrerpolicy)

mixin head(title)
    head
        meta(charset='UTF-8')
        +metaTag('viewport', 'width=device-width, initial-scale=1')
        //- +metaTag('robots', 'noindex, nofollow')

        title=title

        link(rel="preconnect", href="https://fonts.googleapis.com")
        link(rel="preconnect", href="https://fonts.gstatic.com", crossorigin)
        link(rel="stylesheet", href="https://fonts.googleapis.com/css2?family=Anaheim:wght@400..800&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap")
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css", integrity="sha512-5A8nwdMOWrSz20fDsjczgUidUBR8liPYU+WymTZP1lmY9G6Oc7HlZv156XqnsgNUzTyMefFTcsFH/tnJE/+xBg==", crossorigin="anonymous", referrerpolicy="no-referrer")
        +linkStylesheet('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css')
        +linkStylesheet('https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css', 'sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH', 'anonymous')
        //- +linkStylesheet('https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css', 'sha512-tS3S5qG0BlhnQROyJXvNjeEM4UpMXHrQfTGmbQ1gKmelCxlSEBUaxhRBj/EFTzpbP4RVSrpEikbmdJobCvhE3g==', 'anonymous', 'no-referrer')
        +linkStylesheet('css/app.min.css')