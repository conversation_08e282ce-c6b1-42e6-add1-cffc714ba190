<?php

namespace App\Services;

use App\Models\Donor;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Models\SubscriberForLatestEvents;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class AdminService
{



    public function storeAdmin(Request $request): User
    {

        try {
            $data = $request->validate([
                'adminName' => 'required|string|max:255',
                'adminEmail' => 'required|email|unique:users,email',
                'adminPassword' => 'required|min:8|'
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        $admin = User::create([
            'name' => $data['adminName'],
            'email' => $data['adminEmail'],
            'password' => Hash::make($data['adminPassword']),
            'address' => 'N/A',
            'city' => 'N/A',
            'state' => 'N/A',
            'fund_raise_message' => 'N/A'
        ]);




        $admin->roles()->attach(Role::where('name', 'admin')->first());



        return $admin;
    }


    public function storeUser(Request $request): User
    {


        try {
            $data = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => 'required|min:8|',
                'fund_raise_message' => 'required|string',
                'file' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5000',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'fund_raise_goal' => 'required|numeric|min:1000',
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        if ($request->hasFile('file')) {
            $data['file_path'] = $request->file('file')->store('uploads', 'public');
        } else {
            $data['file_path'] = 'uploads/default.png';
        }

        $data['commit_fundraising'] = 1;
        $data['use_photo_videos'] = 1;
        $data['liability_waiver'] = 1;


        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'fund_raise_message' => $data['fund_raise_message'],
            'address' => $data['address'],
            'city' => $data['city'],
            'state' => $data['state'],
            'fundraising_goal' => $data['fund_raise_goal'],
            'commit_fundraising' => $data['commit_fundraising'],
            'use_photo_videos' => $data['use_photo_videos'],
            'liability_waiver' => $data['liability_waiver'],
            'profile_photo' => $data['file_path'] ?? null,

        ]);

        SubscriberForLatestEvents::create([
            'email' => $user->email
        ]);


        $user->roles()->attach(Role::where('name', 'user')->first());



        return $user;
    }

    public function storeEditedUser(Request $request): User
    {

        $user = User::findOrFail($request->input('id'));

        try {
            $data = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'fund_raise_message' => 'required|string',
                'file' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5000',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'fundraising_goal' => 'required|numeric|min:1000',
                'password' => 'nullable|min:8',
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }


        if ($request->hasFile('file')) {
            if ($user->profile_photo && $user->profile_photo !== 'uploads/default.png') {
                Storage::disk('public')->delete($user->profile_photo);
            }

            $data['profile_photo'] = $request->file('file')->store('uploads', 'public');
        }


        if ($request->has('password') && $request->input('password') !== '') {
            $data['password'] = Hash::make($request->input('password'));
        }


        $user->update($data);

        return $user;
    }


    public function getDashboardData(Request $request): array
    {
        $user = $request->user();

        if (!$user) {
            throw new \Exception('Unable to find user.');
        }


        $totalCollected = DB::table('donors')->sum('amount_donate');
        $currentYear = date('Y');
        $goal = DB::table('settings')
            ->where('year', $currentYear)
            ->value('goal_amount');

        $totalUsers = User::whereHas('roles', function ($query) {
            $query->where('name', 'user');
        })->count();

        $totalAdmins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->count();

        $totalDonors = Donor::count();



        return [
            'user' => $user,
            'totalCollected' => $totalCollected,
            'goal' =>          $goal,
            'totalUsers' =>  $totalUsers,
            'totalAdmins' => $totalAdmins,
            'totalDonors' => $totalDonors,

        ];
    }


    public function storeFaq(Request $request): array
    {
        try {
            $data = $request->validate([
                'question' => 'required|string|max:1000',
                'answer' => 'required|string|max:5000',
                'order' => 'required|integer|min:1'
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        $id = DB::table('faqs')->insertGetId([
            'question' => $data['question'],
            'answer' => $data['answer'],
            'order' => $data['order'],
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $faq = DB::table('faqs')->where('id', $id)->first();

        return (array) $faq;
    }


    public function updateFaq(Request $request): array
    {
        try {
            $data = $request->validate([
                'id' => 'required|integer|exists:faqs,id',
                'question' => 'required|string|max:1000',
                'answer' => 'required|string|max:5000',
                'order' => 'required|integer|min:1'
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        $faq = DB::table('faqs')->where('id', $data['id'])->first();

        if (!$faq) {
            throw new ModelNotFoundException('FAQ not found.');
        }

        DB::table('faqs')->where('id', $data['id'])->update([
            'question' => $data['question'],
            'answer' => $data['answer'],
            'order' => $data['order'],
            'updated_at' => now(),
        ]);

        $updatedFaq = DB::table('faqs')->where('id', $data['id'])->first();

        return (array) $updatedFaq;
    }


    public function deleteFaq(Request $request): bool
    {
        try {
            $data = $request->validate([
                'id' => 'required|integer|exists:faqs,id'
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        $deleted = DB::table('faqs')->where('id', $data['id'])->delete();

        if (!$deleted) {
            throw new ModelNotFoundException('FAQ could not be deleted.');
        }

        return true;
    }
}
