@import url("https://fonts.googleapis.com/css2?family=Anaheim&display=swap");

:root {
    --primary-color: #19345e;
    --secondary-color: #154da3;
    --accent-color: #f05522;
    --background-color: #f4f7fc;
    --light-bg: #ffffff;
    --muted-text: #6c7a90;
    --input-border: #cdd8ee;
    --gradient-light: linear-gradient(135deg, #f4f7fc 0%, #e6eeff 100%);
    --box-shadow: 0 10px 25px rgba(25, 52, 94, 0.12);
    --input-shadow: 0 4px 8px rgba(21, 77, 163, 0.06);
    --button-shadow: 0 10px 20px rgba(240, 85, 34, 0.25);
}

.donation-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
    background: linear-gradient(
        135deg,
        rgba(255, 155, 62, 0.05) 0%,
        rgba(21, 77, 163, 0.05) 100%
    );
}

.donation-container {
    background-color: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(25, 52, 94, 0.15);
    max-width: 1000px;
    width: 100%;
    padding: 3.5rem;
    animation: fadeIn 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.donation-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(
        90deg,
        var(--accent-color),
        var(--secondary-color)
    );
}

.donation-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.donation-header::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(
        90deg,
        var(--accent-color),
        var(--secondary-color)
    );
    margin: 1.5rem auto 0;
    border-radius: 2px;
}

.donation-title {
    font-size: 2.8rem;
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.75rem;
    letter-spacing: -0.5px;
}

.donation-subtitle {
    color: var(--secondary-color);
    font-size: 1.3rem;
    opacity: 0.85;
    font-weight: 400;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
}

.form-section {
    background-color: var(--light-bg);
    border-radius: 18px;
    padding: 2.25rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.04);
    transition: all 0.4s ease;
    border: 1px solid rgba(205, 216, 238, 0.5);
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-light);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.form-section:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(21, 77, 163, 0.2);
}

.form-section:hover::before {
    opacity: 1;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(25, 52, 94, 0.08);
}

.section-header i {
    margin-right: 1rem;
    font-size: 1.6rem;
    color: var(--accent-color);
    background: rgba(240, 85, 34, 0.1);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.form-section:hover .section-header i {
    background: var(--accent-color);
    color: white;
    transform: scale(1.05);
}

.section-header h3 {
    font-size: 1.4rem;
    margin: 0;
    color: var(--primary-color);
    font-weight: 600;
    letter-spacing: -0.3px;
}

.name-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control {
    width: 100%;
    padding: 0.9rem 1rem;
    border: 2px solid var(--input-border);
    background-color: #f9fbff;
    color: var(--primary-color);
    border-radius: 12px;
    transition: all 0.3s ease;
    font-family: "Anaheim", sans-serif;
    font-size: 1rem;
    box-shadow: var(--input-shadow);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 4px rgba(21, 77, 163, 0.1);
    background-color: white;
}

.form-group:focus-within label {
    color: var(--secondary-color);
}

.required {
    color: var(--accent-color);
    margin-left: 0.25rem;
}

.optional {
    color: var(--muted-text);
    font-size: 0.9rem;
    margin-left: 0.5rem;
    font-style: italic;
}

.donation-amount {
    text-align: center;
    margin-top: 2rem;
    padding: 1.25rem;
    background: rgba(21, 77, 163, 0.05);
    border-radius: 12px;
    border: 1px dashed rgba(21, 77, 163, 0.2);
}

.amount-label {
    display: block;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.amount-display {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.donation-submit {
    text-align: center;
    margin-top: 3rem;
    position: relative;
}

.donation-submit::before {
    content: "";
    position: absolute;
    top: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(
        90deg,
        rgba(25, 52, 94, 0.1),
        rgba(25, 52, 94, 0.3),
        rgba(25, 52, 94, 0.1)
    );
}

.btn-donate {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-color), #ff7643);
    color: white;
    border: none;
    padding: 1.25rem 3rem;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s ease;
    box-shadow: var(--button-shadow);
    position: relative;
    overflow: hidden;
    font-family: "Anaheim", sans-serif;
    letter-spacing: 0.5px;
}

.btn-donate::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: all 0.6s ease;
}

.btn-donate:hover {
    transform: translateY(-6px);
    box-shadow: 0 15px 30px rgba(240, 85, 34, 0.35);
    background: linear-gradient(135deg, #ff7643, var(--accent-color));
}

.btn-donate:hover::before {
    left: 100%;
}

.btn-donate .btn-icon {
    margin-left: 1rem;
    animation: pulse 1.5s infinite;
}

/* Processing state styles */
.btn-donate.processing {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-donate.processing:hover {
    transform: none;
    background: linear-gradient(135deg, #6c757d, #5a6268);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-donate.processing::before {
    display: none;
}

.btn-donate:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Enhanced Full-Screen Loader Styles */
#full-screen-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loader-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 2rem;
}

.loader-icon {
    margin-bottom: 1.5rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border: 0.25em solid rgba(255, 255, 255, 0.25);
    border-right-color: var(--accent-color);
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

.loader-message {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: white;
}

.loader-steps {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step.completed .step-icon {
    background: var(--accent-color);
    color: white;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step span {
    font-size: 0.9rem;
    text-align: center;
}

.privacy-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(25, 52, 94, 0.02);
}

.checkbox-group:hover {
    background: rgba(25, 52, 94, 0.05);
}

.custom-checkbox {
    appearance: none;
    width: 24px;
    height: 24px;
    border: 2px solid var(--secondary-color);
    border-radius: 6px;
    margin-right: 1rem;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    background-color: white;
}

.custom-checkbox:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.custom-checkbox:checked::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1rem;
    font-weight: bold;
    font-family: "Segoe UI Symbol", "Arial", sans-serif;
}

.stripe-card-wrapper {
    margin: 20px auto;
    padding: 20px;
    background: linear-gradient(145deg, #19345e, #154da3);
    border-radius: 16px;
    box-shadow: 0 12px 30px rgba(25, 52, 94, 0.15);
    transition: all 0.4s ease-in-out;
    position: relative;
    overflow: hidden;
}

.stripe-card-wrapper::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 60%
    );
    animation: shimmer 3s infinite linear;
}

.stripe-card-wrapper:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px) scale(1.02);
}

.stripe-card-element {
    background: rgba(255, 255, 255, 0.9);
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.error-message {
    color: #e74c3c;
    margin-top: 1rem;
    font-size: 0.9rem;
    text-align: center;
    font-weight: 500;
}

#full-screen-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(25, 52, 94, 0.85);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: white;
    font-size: 1.5rem;
    transition: all 0.5s ease-in-out;
    backdrop-filter: blur(8px);
}

.success-animation {
    animation: success-pulse 1.2s ease-in-out infinite;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes success-pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .donation-container {
        padding: 2.5rem;
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .donation-title {
        font-size: 2.2rem;
    }

    .donation-container {
        padding: 2rem;
    }

    .btn-donate {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .name-fields {
        grid-template-columns: 1fr;
    }

    .form-section {
        padding: 1.5rem;
    }

    .donation-title {
        font-size: 1.8rem;
    }

    .loader-content {
        padding: 1rem;
        max-width: 300px;
    }

    .loader-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .step {
        flex-direction: row;
        justify-content: flex-start;
        text-align: left;
    }

    .step-icon {
        margin-right: 1rem;
        margin-bottom: 0;
    }
}

@media (max-width: 768px) {
    .stripe-card-wrapper {
        padding: 20px 15px;
        margin: 20px auto;
        /* Ensure minimum width for proper Stripe rendering */
        min-width: 320px;
        width: 100%;
        max-width: 100%;
    }

    .donation-wrapper {
        padding: 0;
        width: 100%;
    }

    .donation-container {
        padding: 0;
        width: 100%;
    }

    .form-grid {
        padding: 0;
        width: 100%;
    }

    .stripe-card-element {
        padding: 20px 15px;
        margin-bottom: 15px;
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        /* Critical: Set minimum dimensions for Stripe Elements */
        min-width: 300px;
        min-height: 45px;
        width: 100%;
        overflow: visible;
    }

    /* Ensure form section accommodates the minimum width */
    .payment-details {
        min-width: 320px;
        overflow-x: auto;
    }

    /* Allow horizontal scrolling if absolutely necessary */
    .form-section {
        min-width: 320px;
    }
}

@media (max-width: 480px) {
    .stripe-card-wrapper {
        padding: 18px 12px;
        margin: 15px auto;
        /* Maintain minimum width even on very small screens */
        min-width: 300px;
        width: calc(100vw - 40px);
        max-width: 350px;
    }

    .stripe-card-element {
        padding: 18px 12px;
        margin-bottom: 20px;
        /* Increased minimum dimensions for very small screens */
        min-width: 280px;
        min-height: 50px;
        /* Ensure content doesn't get compressed */
        box-sizing: border-box;
    }

    /* Make the entire form section horizontally scrollable if needed */
    .form-grid {
        min-width: 300px;
    }

    .payment-details {
        min-width: 300px;
        overflow-x: visible;
    }

    /* Adjust donation container to handle minimum widths */
    .donation-container {
        min-width: 320px;
        width: calc(100vw - 40px);
        max-width: 100%;
        overflow-x: auto;
    }
}

/* Additional fix for very narrow screens (below 320px) */
@media (max-width: 320px) {
    .donation-wrapper {
        padding: 1rem 0.5rem;
    }

    .donation-container {
        min-width: 280px;
        padding: 1.5rem 1rem;
    }

    .stripe-card-wrapper {
        min-width: 260px;
        margin: 15px auto;
    }

    .stripe-card-element {
        min-width: 240px;
        min-height: 55px;
    }
}
