.about-hero {
	background: #f05522;
}

.about-text {
	padding-top: 2.5rem;

	@media (min-width: 768px) {
		padding-top: 3rem;
	}

	h1 {
		color: #19345e;
		font: 60px/1 $anaheim;

		@media (min-width: 768px) {
			font-size: 90px;
		}
	}

	p {
		color: #000;
	}

	a {
		color: inherit;
	}
}

.card-about {
	padding-block: 0 2.5rem;

	@media (min-width: 768px) {
		padding-block: 3rem 6rem;
	}

	.box-meta {
		background: #e4eaf4;
		border-radius: 50px;
		height: 100%;
		padding: 2rem 1.5rem;

		h2 {
			color: #f05522;
			font: 700 32px $anaheim;
		}

		h3 {
			color: #19345e;
			font: 700 17px/1.5 $inter;
		}

		p {
			color: #000;
		}
	}
}

.team-time {
	padding-block: 2.5rem;

	@media (min-width: 768px) {
		padding-block: 6rem;
	}

	h2 {
		color: #154da3;
		font-size: 40px;
		font-weight: 700;
	}

	p {
		color: #000;
	}
}

.fund-riser {
	padding-block: 2.5rem;

	@media (min-width: 768px) {
		padding-block: 6rem;
	}

	h2 {
		color: #154da3;
		font-size: 40px;
		font-weight: 700;
	}

	p {
		color: #000;
	}

	.copy {
		p {
			color: #154da3;
			font-style: italic;
		}
	}
}
