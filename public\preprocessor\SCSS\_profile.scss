.profile-hero {
	background: #e4eaf4;
	border-top: 1.25rem solid #19345e;
	padding-block: 3rem;

	.img-player {
		border-radius: 50%;
		isolation: isolate;
		overflow: hidden;
		position: relative;

		&:before {
			content: '';
			display: block;
			padding-top: 100%;
		}

		img {
			height: 100%;
			left: 0;
			object-fit: cover;
			position: absolute;
			top: 0;
			width: 100%;
			z-index: 1;
		}
	}

	h1 {
		color: #19345e;
		font: 60px/0.9 $anaheim;

		@media (min-width: 768px) {
			font-size: 90px;
		}

		strong {
			color: #f05522;
			font-size: 110%;
		}
	}
}

.select-level {
	padding-block: 6rem;
}

.your-donation {
	background: #e4eaf4;
	padding-block: 4.5rem;

	.box {
		h2 {
			color: #f05522;
			font: 700 22px $anaheim;
		}

		p {
			color: #676464;
			font: 14px/1.5 $inter;
		}
	}
}
