@extends('layouts.app')
@section('title', '2025 ASFL Gauntlet | Dashboard')
@section('content')
    <div class="dashboard-wrapper">
        {{-- Hero Section --}}
        <section class="hero-section text-white"
            style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); padding: 80px 0; position: relative; overflow: hidden;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-12">
                        <div class="d-flex flex-wrap flex-md-nowrap align-items-center justify-content-between gap-4">
                            <div class="d-flex align-items-center flex-grow-1" style="min-width: 0;">
                                <img src="{{ Storage::url($data['user']->profile_photo) }}"
                                    class="rounded-circle me-4 shadow flex-shrink-0"
                                    style="width: 100px; height: 100px; object-fit: cover; border: 4px solid rgba(255,255,255,0.3);">
                                <div class="min-width-0 flex-grow-1">
                                    <h1 class="mb-2 fw-bold text-truncate"
                                        style="font-family: 'Anaheim', sans-serif; font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); max-width: 100%;">
                                        Welcome, {{ $data['user']->name }}
                                    </h1>
                                    <p class="lead mb-0 text-truncate"
                                        style="color: rgba(255,255,255,0.9); max-width: 100%; font-family: 'Inter', sans-serif;">
                                        {{ $data['user']->email }}
                                    </p>
                                </div>
                            </div>
                            <div class="d-flex gap-3 flex-shrink-0">
                                <a href="#" class="btn btn-light rounded-pill px-4 py-2" data-bs-toggle="modal"
                                    data-bs-target="#editProfileModal"
                                    style="color: #19345e; font-weight: 600; transition: all 0.3s ease; font-family: 'Inter', sans-serif;"
                                    onmouseover="this.style.transform='scale(1.05)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="fas fa-user-edit me-2"></i>Edit Profile
                                </a>
                                <a href="#" class="btn btn-outline-light rounded-pill px-4 py-2"
                                    data-bs-toggle="modal" data-bs-target="#shareUrlModal"
                                    style="border: 2px solid white; transition: all 0.3s ease; font-family: 'Inter', sans-serif;"
                                    onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='scale(1.05)'"
                                    onmouseout="this.style.backgroundColor='transparent'; this.style.transform='scale(1)'">
                                    <i class="fas fa-share-alt me-2"></i>Share Page
                                </a>

                                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                    @csrf
                                    <button class="btn btn-outline-light rounded-pill px-4 py-2"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        {{-- Fundraising Progress Section --}}
        <section class="goal-section py-5" style="background: linear-gradient(135deg, #f4f7fc 0%, #e6eeff 100%);">
            <div class="container">
                <div class="row g-4">
                    {{-- Fundraising Goal Card --}}
                    <div class="col-md-8">
                        <div class="card shadow-lg h-100 border-0"
                            style="background: white; border-radius: 20px; overflow: hidden;">
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h3 class="card-title mb-0"
                                        style="color: #19345e; font-family: 'Anaheim', sans-serif; font-size: 1.8rem;">
                                        <i class="fas fa-chart-line me-2" style="color: #f05522;"></i>Fundraising Progress
                                    </h3>
                                    {{-- <a href="#" class="btn btn-outline-primary rounded-pill" data-bs-toggle="modal"
                                        data-bs-target="#editGoalModal"
                                        style="transition: all 0.3s ease; border-color: #f05522; color: #f05522; font-family: 'Inter', sans-serif;"
                                        onmouseover="this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.transform='scale(1)'">
                                        <i class="fas fa-edit me-2"></i>Adjust Goal
                                    </a> --}}
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h2 class="mb-0"
                                        style="color: #154da3; font-family: 'Anaheim', sans-serif; font-size: 2rem;">
                                        ${{ number_format($data['user']->fundraising_goal, 2) }} Goal
                                    </h2>
                                    <h4 class="mb-0"
                                        style="color: #f05522; font-family: 'Anaheim', sans-serif; font-size: 1.5rem;">
                                        ${{ number_format($data['totalAmount'] ?? 0, 2) }} Raised
                                    </h4>
                                </div>

                                <div class="progress position-relative"
                                    style="height: 35px; background-color: rgba(25,52,94,0.1); border-radius: 20px; overflow: hidden; box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ $data['user']->fundraising_goal > 0 ? min(100, (($data['totalAmount'] ?? 0) / $data['user']->fundraising_goal) * 100) : 0 }}%;
            background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); min-width: 2em; border-radius: 20px;"
                                        aria-valuenow="{{ $data['user']->fundraising_goal > 0 ? min(100, (($data['totalAmount'] ?? 0) / $data['user']->fundraising_goal) * 100) : 0 }}"
                                        aria-valuemin="0" aria-valuemax="100">
                                    </div>

                                    <!-- Percentage indicator that's always visible -->
                                    <span class="position-absolute d-flex align-items-center justify-content-center"
                                        style="left: 0; top: 0; right: 0; bottom: 0; color: {{ $data['user']->fundraising_goal > 0 && (($data['totalAmount'] ?? 0) / $data['user']->fundraising_goal) * 100 > 25 ? 'white' : '#19345e' }}; font-weight: 600; font-family: 'Inter', sans-serif; font-size: 1.1rem;">
                                        {{ $data['user']->fundraising_goal > 0 ? number_format((($data['totalAmount'] ?? 0) / $data['user']->fundraising_goal) * 100, 1) : 0 }}%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Quick Stats --}}
                    <div class="col-md-4">
                        <div class="card shadow-lg h-100 border-0"
                            style="background: white; border-radius: 20px; overflow: hidden;">
                            <div class="card-body text-center d-flex flex-column justify-content-center p-4">
                                <h2 style="color: #f05522; font-family: 'Anaheim', sans-serif; font-size: 2.5rem;">
                                    {{ $data['totalDonors'] }}</h2>
                                <p class="lead" style="color: #19345e;">Total Donors</p>
                                <h3 class="mt-3"
                                    style="color: #154da3; font-family: 'Anaheim', sans-serif; font-size: 1.8rem;">
                                    ${{ $data['averageAmount'] }}</h3>
                                <p class="text-muted" style="font-family: 'Inter', sans-serif;">Average Donation</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        {{-- Leaderboard Section --}}
        <section class="leaderboard-section py-5" style="background-color: white;">
            <div class="container">
                <h2 class="text-center mb-5" style="color: #19345e; font-family: 'Anaheim', sans-serif; font-size: 2.5rem;">
                    <i class="fas fa-trophy me-3" style="color: #f05522;"></i>Top Donors
                </h2>

                @if (count($data['topDonors']) > 0)
                    <div class="slider-container position-relative overflow-hidden">
                        <!-- For desktop - slider view when enough items -->
                        <div class="slider d-flex gap-3 flex-nowrap" id="donorSlider"
                            style="transition: transform 1s ease-in-out;">
                            @foreach ($data['topDonors'] as $donor)
                                <div class="donor-card flex-shrink-0" style="width: 100%; max-width: 350px;">
                                    <div class="card shadow-lg mb-4 h-100 border-0"
                                        style="border-radius: 20px; overflow: hidden; background: linear-gradient(145deg, #f4f7fc 0%, #e6eeff 100%);">
                                        <div class="card-body text-center p-4">
                                            <div class="donor-img-wrapper mx-auto mb-3 position-relative">
                                                <img src="{{ Storage::url('uploads/default.png') }}"
                                                    class="rounded-circle mb-3 shadow"
                                                    style="width: 150px; height: 150px; object-fit: cover; border: 4px solid #f05522;">
                                                <div class="donor-rank position-absolute"
                                                    style="bottom: 0; right: 0; background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
                                            color: white; width: 40px; height: 40px; border-radius: 50%; display: flex;
                                            align-items: center; justify-content: center; font-weight: bold; box-shadow: 0 2px 10px rgba(240,85,34,0.3); font-family: 'Inter', sans-serif;">
                                                    {{ $loop->iteration }}
                                                </div>
                                            </div>
                                            <h5
                                                style="color: #19345e; font-family: 'Inter', sans-serif; font-weight: 600;">
                                                {{ $donor['name'] }}</h5>
                                            <p class="mb-0"
                                                style="font-size: 1.5rem; font-weight: bold; background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-family: 'Anaheim', sans-serif;">
                                                ${{ number_format($donor['amount'], 2) }}
                                            </p>
                                            <span
                                                style="font-size: 0.9rem; color: #6c7a90; font-family: 'Inter', sans-serif;">Donated</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        @if (count($data['topDonors']) > 3)
                            <button class="slider-arrow left-arrow" id="prevBtn">&#10094;</button>
                            <button class="slider-arrow right-arrow" id="nextBtn">&#10095;</button>
                        @endif
                    </div>

                    @if (count($data['topDonors']) <= 3)
                        <div class="row justify-content-center d-none d-md-flex" id="smallDonorGrid">
                            @foreach ($data['topDonors'] as $donor)
                                <div
                                    class="col-md-{{ 12 / min(count($data['topDonors']), 3) }} col-lg-{{ 12 / min(count($data['topDonors']), 3) }}">
                                </div>
                            @endforeach
                        </div>
                    @endif
                @else
                    <div class="text-center py-5">
                        <div class="empty-state-icon mb-3">
                            <i class="fas fa-trophy" style="font-size: 3rem; color: #d1d1d1;"></i>
                        </div>
                        <p class="text-muted" style="font-family: 'Inter', sans-serif;">No donors found yet.</p>
                    </div>
                @endif
            </div>
        </section>

        {{-- Recent Donors --}}
        @livewire('donor-of-user')
    </div>

    {{-- Edit Profile Modal --}}
    <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content rounded-lg shadow-lg border-0" style="border-radius: 20px; overflow: hidden;">
                <div class="modal-header position-relative p-0 border-0">
                    <div class="w-100 text-white p-4"
                        style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">
                        <h4 class="modal-title fw-bold mb-0" id="editProfileModalLabel"
                            style="font-family: 'Anaheim', sans-serif;">
                            <i class="fas fa-user-edit me-2"></i>Edit Your Profile
                        </h4>
                        <p class="text-white-50 mb-0" style="font-family: 'Inter', sans-serif;">Update your personal
                            information and settings</p>
                    </div>
                    <button type="button" class="btn-close btn-close-white position-absolute top-0 end-0 m-3"
                        data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body p-4" style="background: #f8f9fa;">
                    <ul class="nav nav-pills nav-justified mb-4" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active px-4 py-3 rounded-pill" id="profile-tab" data-bs-toggle="tab"
                                data-bs-target="#profile" type="button" role="tab" aria-controls="profile"
                                aria-selected="true" style="font-family: 'Inter', sans-serif;">
                                <i class="bi bi-person-fill me-2"></i><span class="fw-medium">Profile</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link px-4 py-3 rounded-pill" id="security-tab" data-bs-toggle="tab"
                                data-bs-target="#security" type="button" role="tab" aria-controls="security"
                                aria-selected="false" style="font-family: 'Inter', sans-serif;">
                                <i class="bi bi-shield-lock-fill me-2"></i><span class="fw-medium">Security</span>
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content bg-white p-4 rounded-3 shadow-sm" id="profileTabsContent"
                        style="border-radius: 15px;">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile" role="tabpanel"
                            aria-labelledby="profile-tab">
                            <form id="profileForm" method="POST">
                                @csrf
                                <div class="text-center mb-4">
                                    <div class="position-relative d-inline-block">
                                        <img src="{{ Storage::url($data['user']->profile_photo) }}"
                                            class="rounded-circle img-thumbnail border-3 shadow-lg"
                                            style="width: 160px; height: 160px; object-fit: cover; border: 4px solid #f05522;"
                                            id="profilePreview" alt="Profile Picture">
                                        <div class="position-absolute bottom-0 end-0">
                                            <label for="profilePictureInput"
                                                class="btn btn-primary rounded-circle p-2 shadow"
                                                style="background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); border: none;">
                                                <i class="fas fa-camera"></i>
                                            </label>
                                            <input type="file" name="file" class="d-none" id="profilePictureInput"
                                                accept="image/*">
                                        </div>
                                    </div>
                                    <p class="text-muted mt-2 small" style="font-family: 'Inter', sans-serif;">Click on
                                        camera icon to update your profile picture</p>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="fullName" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-user text-primary me-2" style="color: #f05522;"></i>Full Name
                                        </label>
                                        <input type="text" name="name"
                                            class="form-control form-control-lg rounded-pill" id="fullName"
                                            value="{{ Auth::user()->name }}" style="font-family: 'Inter', sans-serif;">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="goalAmount" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-bullseye text-primary me-2" style="color: #f05522;"></i>Goal
                                            Amount
                                        </label>
                                        <input type="number" name="fundraising_goal"
                                            class="form-control form-control-lg rounded-pill" id="goalAmount"
                                            value="{{ Auth::user()->fundraising_goal }}"
                                            style="font-family: 'Inter', sans-serif;">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label fw-medium"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-envelope text-primary me-2" style="color: #f05522;"></i>Email
                                        Address
                                    </label>
                                    <input type="email" name="email"
                                        class="form-control form-control-lg rounded-pill" id="email"
                                        value="{{ Auth::user()->email }}" style="font-family: 'Inter', sans-serif;">
                                </div>

                                <div class="mb-4">
                                    <label for="messageForDonors" class="form-label fw-medium"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-comment-alt text-primary me-2"
                                            style="color: #f05522;"></i>Message To Donors
                                    </label>
                                    <div class="card shadow-sm border-0">
                                        <div class="card-body bg-white rounded">
                                            <textarea id="messageForDonors" name="fund_raise_message" class="form-control border-0" rows="4"
                                                style="font-family: 'Inter', sans-serif;">{!! old('fund_raise_message', Auth::user()->fund_raise_message) !!}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-phone text-primary me-2" style="color: #f05522;"></i>Phone
                                            Number
                                        </label>
                                        <input type="tel" class="form-control form-control-lg rounded-pill"
                                            id="phone" name="phone_number" value="{{ Auth::user()->phone_number }}"
                                            style="font-family: 'Inter', sans-serif;">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="location" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-map-marker-alt text-primary me-2"
                                                style="color: #f05522;"></i>Address
                                        </label>
                                        <input type="text" name="address"
                                            class="form-control form-control-lg rounded-pill" id="location"
                                            value="{{ Auth::user()->address }}"
                                            style="font-family: 'Inter', sans-serif;">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="city" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-city text-primary me-2" style="color: #f05522;"></i>City
                                        </label>
                                        <input type="text" name="city"
                                            class="form-control form-control-lg rounded-pill" id="city"
                                            value="{{ Auth::user()->city }}" style="font-family: 'Inter', sans-serif;">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="state" class="form-label fw-medium"
                                            style="font-family: 'Inter', sans-serif;">
                                            <i class="fas fa-flag text-primary me-2" style="color: #f05522;"></i>State
                                        </label>
                                        <input type="text" name="state"
                                            class="form-control form-control-lg rounded-pill" id="state"
                                            value="{{ Auth::user()->state }}" style="font-family: 'Inter', sans-serif;">
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                            <form id="securityForm" method="POST">
                                <div class="alert alert-info bg-info bg-opacity-10 border-start border-info border-4 rounded-3 p-3"
                                    role="alert" style="border-left-color: #f05522 !important;">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle fa-2x me-3" style="color: #f05522;"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h5 class="mb-1" style="font-family: 'Inter', sans-serif;">Security Tips
                                            </h5>
                                            <p class="mb-0" style="font-family: 'Inter', sans-serif;">Keep your account
                                                secure by using a strong password that you don't use elsewhere.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="currentPassword" class="form-label fw-medium"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-lock text-primary me-2" style="color: #f05522;"></i>Current
                                        Password
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <input type="password" name="current_password"
                                            class="form-control rounded-pill rounded-end-0" id="currentPassword"
                                            style="font-family: 'Inter', sans-serif;">
                                        <button
                                            class="btn btn-outline-secondary rounded-pill rounded-start-0 toggle-password"
                                            type="button" data-target="currentPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="newPassword" class="form-label fw-medium"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-key text-primary me-2" style="color: #f05522;"></i>New Password
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <input type="password" name="new_password"
                                            class="form-control rounded-pill rounded-end-0" id="newPassword"
                                            style="font-family: 'Inter', sans-serif;">
                                        <button
                                            class="btn btn-outline-secondary rounded-pill rounded-start-0 toggle-password"
                                            type="button" data-target="newPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength mt-2">
                                        <div class="progress" style="height: 8px; border-radius: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%;"
                                                aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted mt-1 d-block"
                                            style="font-family: 'Inter', sans-serif;">Password strength: <span
                                                id="passwordStrength" class="fw-bold">Too weak</span></small>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="confirmPassword" class="form-label fw-medium"
                                        style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-check-circle text-primary me-2"
                                            style="color: #f05522;"></i>Confirm New Password
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <input type="password" name="new_password_confirmation"
                                            class="form-control rounded-pill rounded-end-0" id="confirmPassword"
                                            style="font-family: 'Inter', sans-serif;">
                                        <button
                                            class="btn btn-outline-secondary rounded-pill rounded-start-0 toggle-password"
                                            type="button" data-target="confirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light p-4 rounded-bottom">
                    <button type="button" class="btn btn-outline-secondary btn-lg px-4 rounded-pill"
                        data-bs-dismiss="modal" style="font-family: 'Inter', sans-serif;">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" id="saveChangesButton" class="btn btn-primary btn-lg px-4 rounded-pill"
                        style="background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); border: none; font-family: 'Inter', sans-serif;">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Share URL Modal --}}
    <div class="modal fade" id="shareUrlModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0" style="border-radius: 20px; overflow: hidden;">
                <div class="modal-header border-0"
                    style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); color: white;">
                    <h5 class="modal-title" style="font-family: 'Anaheim', sans-serif;">Share Your Fundraising Page</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="form-group position-relative">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control rounded-pill" id="shareUrlInput"
                                value="{{ $data['donationPageUrl'] }}" readonly
                                style="font-family: 'Inter', sans-serif;">
                            <button class="btn btn-primary rounded-pill" type="button" id="copyUrlButton"
                                style="background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); border: none; font-family: 'Inter', sans-serif;">
                                <i class="fas fa-copy me-2"></i>Copy
                            </button>
                        </div>
                        <small id="copyMessage" class="text-muted d-block"
                            style="font-family: 'Inter', sans-serif;"></small>
                    </div>


                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Header Section Responsive Styles */
        .min-width-0 {
            min-width: 0;
        }

        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        @media (max-width: 991px) {
            .hero-section h1 {
                font-size: 1.75rem;
            }

            .hero-section .lead {
                font-size: 1rem;
            }
        }

        @media (max-width: 767px) {
            .donor-card {
                max-width: 280px;
            }
        }

        /* Modal enhancements */
        .modal-content {
            overflow: hidden;
            border: none;
            transition: all 0.3s ease;
        }

        /* Form controls styling */
        .form-control {
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(240, 85, 34, 0.25);
            border-color: #f05522;
        }

        /* Tab styling */
        .nav-pills .nav-link {
            transition: all 0.3s ease;
            color: #495057;
            background-color: #f8f9fa;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
            box-shadow: 0 4px 12px rgba(240, 85, 34, 0.3);
            transform: translateY(-2px);
        }

        /* Profile picture hover effect */
        #profilePreview {
            transition: all 0.3s ease;
            border: 4px solid #f05522;
        }

        #profilePreview:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2) !important;
        }

        /* Password strength indicator */
        .password-strength .progress {
            height: 8px;
            overflow: hidden;
            background-color: #e9ecef;
            border-radius: 4px;
        }

        .password-strength .progress-bar {
            transition: width 0.5s ease;
        }

        /* Button styling */
        .btn-primary {
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(240, 85, 34, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(240, 85, 34, 0.4);
        }

        .btn-primary:active {
            transform: translateY(1px);
        }

        /* Animation classes */
        .animated-input {
            animation: pulse 0.3s ease;
        }

        @keyframes pulse {
            0% {
                background-color: rgba(240, 85, 34, 0.1);
            }

            100% {
                background-color: transparent;
            }
        }

        .shake-error {
            animation: shake 0.5s cubic-bezier(.36, .07, .19, .97) both;
        }

        @keyframes shake {

            10%,
            90% {
                transform: translate3d(-1px, 0, 0);
            }

            20%,
            80% {
                transform: translate3d(2px, 0, 0);
            }

            30%,
            50%,
            70% {
                transform: translate3d(-4px, 0, 0);
            }

            40%,
            60% {
                transform: translate3d(4px, 0, 0);
            }
        }

        .btn-pulse {
            animation: button-pulse 1.5s infinite;
        }

        @keyframes button-pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(240, 85, 34, 0.7);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(240, 85, 34, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(240, 85, 34, 0);
            }
        }

        /* Custom toast notification */
        .custom-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            display: flex;
            align-items: center;
            transform: translateX(100%);
            transition: transform 0.3s ease, opacity 0.3s ease;
            opacity: 0;
        }

        .custom-toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-success {
            border-left: 4px solid #198754;
        }

        .toast-error {
            border-left: 4px solid #dc3545;
        }

        .toast-info {
            border-left: 4px solid #f05522;
        }

        .toast-content {
            display: flex;
            align-items: center;
        }

        .toast-content i {
            margin-right: 10px;
            font-size: 1.25rem;
        }

        .toast-success i {
            color: #198754;
        }

        .toast-error i {
            color: #dc3545;
        }

        .toast-info i {
            color: #f05522;
        }

        /* Tab content fade effect */
        #profileTabsContent {
            transition: opacity 0.15s ease;
        }

        /* Input icons coloring */
        .form-label i {
            color: #f05522;
        }

        /* Better file input styling */
        label[for="profilePictureInput"] {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        label[for="profilePictureInput"]:hover {
            transform: scale(1.1);
        }

        /* Improved form focus states */
        .form-control:focus,
        .btn:focus {
            box-shadow: 0 0 0 0.25rem rgba(240, 85, 34, 0.25);
        }

        /* Custom switch styling */
        .form-switch .form-check-input:checked {
            background-color: #f05522;
            border-color: #f05522;
        }

        .form-switch .form-check-input:focus {
            box-shadow: 0 0 0 0.25rem rgba(240, 85, 34, 0.25);
        }

        /* Improved slider arrows */
        .slider-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            cursor: pointer;
            border-radius: 50%;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            box-shadow: 0 3px 10px rgba(240, 85, 34, 0.3);
            transition: all 0.2s ease;
        }

        .slider-arrow:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .left-arrow {
            left: 10px;
        }

        .right-arrow {
            right: 10px;
        }

        .slider:has(.donor-card:only-child) {
            justify-content: center;
        }



        .cta-button.clicked {
            transform: scale(0.95);
            opacity: 0.8;
            transition: transform 0.2s ease, opacity 0.2s ease;
        }


        .cta.orange.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            width: 47px !important;
            height: 33px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: opacity 0.3s ease;
            border: none;
        }

        .activities-container {
            transition: opacity 0.3s ease;
            min-height: 300px;
        }
    </style>
@endpush

@push('scripts')
    <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {

            tinymce.init({
                selector: '#messageForDonors',
                height: 300,
                menubar: false,
                branding: false,
                plugins: 'advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table help wordcount emoticons hr nonbreaking',
                toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image emoticons | removeformat fullscreen',
                content_style: "body { font-family: Arial, sans-serif; font-size: 14px; }",
                relative_urls: false,
                remove_script_host: false
            });

            const copyBtn = document.getElementById('copyUrlButton');
            if (copyBtn) {
                copyBtn.addEventListener('click', function() {
                    const input = document.getElementById('shareUrlInput');
                    const message = document.getElementById('copyMessage');

                    navigator.clipboard.writeText(input.value)
                        .then(() => {
                            message.textContent = "URL copied successfully!";
                            message.className = "text-success";
                            setTimeout(() => message.textContent = "", 3000);
                        })
                        .catch(() => {
                            message.textContent = "Failed to copy URL. Try again.";
                            message.className = "text-danger";
                        });
                });
            }

            const slider = document.getElementById('donorSlider');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (slider && slider.children.length > 3) {
                const donorCards = slider.querySelectorAll('.donor-card');
                let currentIndex = 0;
                let slideWidth = donorCards[0].offsetWidth + 16;
                const maxIndex = Math.max(0, donorCards.length - 3);

                const updateSlider = () => {
                    slider.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
                    prevBtn.style.opacity = currentIndex <= 0 ? '0.5' : '1';
                    nextBtn.style.opacity = currentIndex >= maxIndex ? '0.5' : '1';
                };

                const autoSlide = () => {
                    currentIndex = currentIndex < maxIndex ? currentIndex + 1 : 0;
                    updateSlider();
                };

                let interval = setInterval(autoSlide, 3000);

                prevBtn.addEventListener('click', () => {
                    if (currentIndex > 0) {
                        currentIndex--;
                        updateSlider();
                        clearInterval(interval);
                        interval = setInterval(autoSlide, 3000);
                    }
                });

                nextBtn.addEventListener('click', () => {
                    if (currentIndex < maxIndex) {
                        currentIndex++;
                        updateSlider();
                        clearInterval(interval);
                        interval = setInterval(autoSlide, 3000);
                    }
                });

                window.addEventListener('resize', () => {
                    slideWidth = donorCards[0].offsetWidth + 16;
                    updateSlider();
                });

                updateSlider();
            } else {
                prevBtn && (prevBtn.style.display = 'none');
                nextBtn && (nextBtn.style.display = 'none');
            }


            document.querySelectorAll('.toggle-password').forEach(button => {
                button.addEventListener('click', function() {
                    const input = document.getElementById(this.dataset.target);
                    const isPassword = input.type === 'password';
                    input.type = isPassword ? 'text' : 'password';
                    this.innerHTML =
                    `<i class="fas ${isPassword ? 'fa-eye-slash' : 'fa-eye'}"></i>`;


                    input.classList.add('animated-input');
                    setTimeout(() => input.classList.remove('animated-input'), 300);
                });
            });


            const fileInput = document.getElementById('profilePictureInput');
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        const reader = new FileReader();
                        const previewImg = document.getElementById('profilePreview');

                        reader.onload = e => {

                            previewImg.style.opacity = 0;
                            setTimeout(() => {
                                previewImg.src = e.target.result;
                                previewImg.style.opacity = 1;
                            }, 300);
                        };
                        reader.readAsDataURL(e.target.files[0]);

                        showToast('Profile picture selected!', 'success');
                    }
                });
            }


            const newPasswordInput = document.getElementById('newPassword');
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let feedbackText = '';


                    if (password.length >= 8) {
                        strength += 25;
                        feedbackText +=
                            '<span class="text-success"><i class="fas fa-check-circle"></i> Length</span> ';
                    } else {
                        feedbackText +=
                            '<span class="text-muted"><i class="fas fa-times-circle"></i> Length</span> ';
                    }

                    if (/[A-Z]/.test(password)) {
                        strength += 25;
                        feedbackText +=
                            '<span class="text-success"><i class="fas fa-check-circle"></i> Uppercase</span> ';
                    } else {
                        feedbackText +=
                            '<span class="text-muted"><i class="fas fa-times-circle"></i> Uppercase</span> ';
                    }

                    if (/[0-9]/.test(password)) {
                        strength += 25;
                        feedbackText +=
                            '<span class="text-success"><i class="fas fa-check-circle"></i> Number</span> ';
                    } else {
                        feedbackText +=
                            '<span class="text-muted"><i class="fas fa-times-circle"></i> Number</span> ';
                    }

                    if (/[^A-Za-z0-9]/.test(password)) {
                        strength += 25;
                        feedbackText +=
                            '<span class="text-success"><i class="fas fa-check-circle"></i> Symbol</span>';
                    } else {
                        feedbackText +=
                            '<span class="text-muted"><i class="fas fa-times-circle"></i> Symbol</span>';
                    }

                    const progressBar = this.parentElement.nextElementSibling.querySelector(
                    '.progress-bar');
                    const strengthText = document.getElementById('passwordStrength');


                    progressBar.style.width = `${strength}%`;
                    progressBar.className = 'progress-bar progress-bar-animated progress-bar-striped';

                    if (strength <= 25) {
                        progressBar.classList.add('bg-danger');
                        strengthText.textContent = 'Too weak';
                        strengthText.className = 'fw-bold text-danger';
                    } else if (strength <= 50) {
                        progressBar.classList.add('bg-warning');
                        strengthText.textContent = 'Weak';
                        strengthText.className = 'fw-bold text-warning';
                    } else if (strength <= 75) {
                        progressBar.classList.add('bg-info');
                        strengthText.textContent = 'Medium';
                        strengthText.className = 'fw-bold text-info';
                    } else {
                        progressBar.classList.add('bg-success');
                        strengthText.textContent = 'Strong';
                        strengthText.className = 'fw-bold text-success';
                    }


                    let feedbackEl = document.getElementById('password-feedback');
                    if (!feedbackEl) {
                        feedbackEl = document.createElement('div');
                        feedbackEl.id = 'password-feedback';
                        feedbackEl.className = 'small mt-2';
                        this.parentElement.nextElementSibling.appendChild(feedbackEl);
                    }
                    feedbackEl.innerHTML = feedbackText;
                });
            }


            document.querySelectorAll('#profileTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabsContent = document.getElementById('profileTabsContent');
                    tabsContent.style.opacity = 0;
                    setTimeout(() => {
                        tabsContent.style.opacity = 1;
                    }, 150);
                });
            });


            function showToast(message, type = 'info') {

                const existingToast = document.querySelector('.custom-toast');
                if (existingToast) existingToast.remove();

                const toast = document.createElement('div');
                toast.className = `custom-toast toast-${type}`;
                toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
                document.body.appendChild(toast);


                setTimeout(() => toast.classList.add('show'), 10);


                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }


            const saveChangesButton = document.getElementById('saveChangesButton');

            if (saveChangesButton) {
                saveChangesButton.addEventListener('click', function() {
                    const activeTab = document.querySelector('.tab-pane.show.active');
                    if (!activeTab) return;

                    const form = activeTab.querySelector('form');
                    if (!form) return;

                    let url;

                    const formId = form.getAttribute('id');
                    let formData;

                    if (formId == 'profileForm') {
                        formData = new FormData(form);
                        formData.append('fund_raise_message', tinymce.get('messageForDonors').getContent());
                        url = route('user.editProfile');
                    } else {
                        url = route('user.updatePassword');
                        formData = new FormData(form);
                    }

                    form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
                    form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());


                    saveChangesButton.disabled = true;
                    saveChangesButton.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                <span>Saving...</span>
            </div>
        `;
                    saveChangesButton.classList.add('btn-pulse');

                    fetch(url, {
                            method: form.method,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .content
                            },
                            body: formData
                        })
                        .then(async res => {
                            const data = await res.json();
                            if (res.ok && data.success) {
                                saveChangesButton.classList.remove('btn-pulse');
                                saveChangesButton.innerHTML =
                                    `<i class="fas fa-check-circle text-white me-2"></i> Saved Successfully!`;


                                if (typeof confetti === 'function') {
                                    confetti({
                                        particleCount: 100,
                                        spread: 70,
                                        origin: {
                                            y: 0.6
                                        }
                                    });
                                }

                                setTimeout(() => {
                                    const modalEl = document.getElementById(
                                        'editProfileModal');
                                    if (modalEl) {
                                        const modal = bootstrap.Modal.getInstance(modalEl);
                                        if (modal) modal.hide();
                                    }
                                    showToast('Profile updated successfully!', 'success');
                                    location.reload();
                                }, 1500);
                            } else if (res.status === 422) {
                                handleValidationErrors(data.errors, form);
                                resetButton();
                                showToast('Please correct the errors below', 'error');
                            } else {
                                showError(data.message || 'Something went wrong');
                                resetButton();
                                showToast('Update failed. Please try again.', 'error');
                            }
                        })
                        .catch(error => {
                            console.error(error);
                            showError('Something went wrong. Try again.');
                            resetButton();
                            showToast('Connection error. Please try again.', 'error');
                        });

                    function resetButton() {
                        saveChangesButton.classList.remove('btn-pulse');
                        saveChangesButton.innerHTML = `<i class="fas fa-save me-2"></i> Save Changes`;
                        saveChangesButton.disabled = false;
                    }

                    function handleValidationErrors(errors, form) {
                        for (let field in errors) {
                            const input = form.querySelector(`[name="${field}"]`);
                            if (input) {
                                input.classList.add('is-invalid');
                                const error = document.createElement('div');
                                error.classList.add('invalid-feedback');
                                error.textContent = errors[field][0];
                                input.parentNode.appendChild(error);


                                input.classList.add('shake-error');
                                setTimeout(() => input.classList.remove('shake-error'), 500);
                            }
                        }


                        const firstError = form.querySelector('.is-invalid');
                        if (firstError) {
                            firstError.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    }

                    function showError(message) {
                        const alertBox = document.createElement('div');
                        alertBox.className = 'alert alert-danger mt-3 alert-dismissible fade show';
                        alertBox.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-lg me-2"></i>
                    </div>
                    <div class="flex-grow-1">
                        ${message}
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
                        form.prepend(alertBox);


                        setTimeout(() => alertBox.classList.add('show'), 10);
                        setTimeout(() => {
                            alertBox.classList.remove('show');
                            setTimeout(() => alertBox.remove(), 300);
                        }, 4000);
                    }
                });
            }

        });
    </script>
@endpush
