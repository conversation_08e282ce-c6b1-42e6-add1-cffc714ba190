<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('failed_transactions', function (Blueprint $table) {
            $table->id();

            // Payment identification
            $table->string('payment_intent_id')->nullable()->index(); // May not exist if intent creation failed
            $table->string('customer_id')->nullable()->index();
            $table->string('subscription_id')->nullable()->index();

            // Basic payment info
            $table->decimal('amount', 10, 2)->nullable(); // Requested amount
            $table->decimal('amount_charged', 10, 2)->nullable(); // Amount that was actually charged (if any)
            $table->string('currency', 10)->default('usd');

            // Failure details
            $table->enum('failure_stage', [
                'intent_creation',
                'payment_processing',
                'payment_confirmation',
                'client_side_payment_confirmation',
                'subscription_creation',
                'webhook_processing',
                'database_storage',
                'email_sending',
                'refund_processing'
            ])->index();

            $table->string('failure_code')->nullable(); // Stripe error code
            $table->string('failure_message')->nullable(); // Human readable error
            $table->string('failure_reason')->nullable(); // Categorized reason
            $table->text('failure_description')->nullable(); // Detailed description

            // Payment method info
            $table->string('payment_method_id')->nullable();
            $table->string('payment_method_type')->nullable();
            $table->string('payment_method_brand')->nullable();
            $table->string('payment_method_last4')->nullable();

            // Customer info (for failed transactions we still want to track who tried to pay)
            $table->string('customer_email')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_phone')->nullable();

            // Donation context
            $table->string('donated_to_slug')->nullable(); // User slug they were donating to
            $table->string('donation_type')->nullable(); // one-time, subscription, etc.
            $table->text('donation_message')->nullable();

            // Processing details
            $table->json('error_details')->nullable(); // Full error response from Stripe
            $table->json('processing_log')->nullable(); // Step-by-step processing log
            $table->json('request_data')->nullable(); // Original request data

            // Status and timing
            $table->enum('status', [
                'failed',
                'retry_pending',
                'retry_processing',
                'resolved',
                'abandoned'
            ])->default('failed')->index();

            $table->integer('retry_count')->default(0);
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('last_retry_at')->nullable();
            $table->timestamp('next_retry_at')->nullable();
            $table->timestamp('resolved_at')->nullable();

            // Resolution info
            $table->string('resolved_by')->nullable(); // admin user who resolved it
            $table->text('resolution_notes')->nullable();
            $table->string('resolution_action')->nullable(); // refund, manual_credit, etc.

            // Metadata
            $table->json('metadata')->nullable();

            $table->timestamps();

            // Indexes for efficient querying
            $table->index(['failure_stage', 'status']);
            $table->index(['customer_email', 'failed_at']);
            $table->index(['donated_to_slug', 'failed_at']);
            $table->index(['failure_code', 'failed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('failed_transactions');
    }
};
