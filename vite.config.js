import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css',
                'resources/css/app.min.css',
                'resources/css/appTwo.min.css',
                'resources/css/pages/admin-donor-list.css',
                'resources/css/pages/card-details.css',
                'resources/css/pages/donate.css',
                'resources/css/pages/login.css',
                'resources/css/pages/register.css',
                'resources/css/pages/program-selection.css',
                'resources/js/app.js',
                'resources/js/appOne.js',
                'resources/js/appTwo.js',
                'resources/js/pages/login.js',
                'resources/js/pages/register.js',
                'resources/js/admin/admin-dashboard.js',
                'resources/js/pages/leaderboard.js',
                'resources/js/pages/home.js',
                'resources/js/pages/donor-details.js',
                'resources/js/pages/contact.js',
                'resources/js/pages/details.js',
                'resources/js/pages/program-selection.js'


            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
});
