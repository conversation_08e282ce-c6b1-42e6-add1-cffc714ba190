- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet';
- let heroName = 'Donate';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName} ${heroName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/heroText.pug
include ../partials/footer.pug

html(lang="en")
    +head(pageTitle)

    body
        +header()

        +heroText(heroName)

        section.search-athlete
            .container
                h2.text-center Find an Athlete
                form.search-wrap.mt-2.mb-0(action="")
                    .search
                        input(type="text")
                        .icon.d-flex.align-items-center.justify-content-center #[i.bi.bi-search]
                    .cta-row.text-center.mt-4.pt-2
                        button.cta.orange.border-0 Search

        section.donate-wrap
            .container
                form.donate-form(action="")
                    .select-frequency.text-center.mb-5
                        h2.mb-0 Choose Your Donation Frequency

                        mixin radioOption(label, name = 'frequency')
                            - const id = label.toLowerCase().replace(/\s+/g, '')
                            .check
                                input(type="radio", name=name, value=label, id=id)
                                label.d-flex.align-items-center.justify-content-center.text-white.text-uppercase(for=id)= label

                        .select-row.select-cta.row.justify-content-center.mt-4
                            .col-md-3.col-lg-2.mb-4
                                +radioOption('One Time')
                            .col-md-3.col-lg-2.mb-4
                                +radioOption('Monthly')
                            .col-md-3.col-lg-2.mb-4
                                +radioOption('Quarterly')
                            .col-md-3.col-lg-2.mb-4
                                +radioOption('Annually')

                    .select-package.select-cta.text-center.mb-5
                        mixin radioOptionPackage(label, idFor, name = 'package')
                            - const id = idFor.toLowerCase().replace(/\s+/g, '')
                            .check
                                input(type="radio", name=name, value=id, id=id)
                                label.d-flex.align-items-center.justify-content-center.flex-column.p-3(for=id) !{label}

                        .select-package.row
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$25 <span>Bronze</span>', 'Bronze')
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$50 <span>Silver</span>', 'Silver')
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$100 <span>Gold</span>', 'Gold')
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$250 <span>Platinum</span>', 'Platinum')
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$500 <span>Diamond</span>', 'Diamond')
                            .col-6.col-md-4.mb-4
                                +radioOptionPackage('$1000 <span>Philanthropist</span>', 'Philanthropist')

                    .text-center.mb-5
                        h2.mb-3 Or Enter Your Own Amount
                        .input-price.mx-auto
                            label.visually-hidden(for="amount") Amount
                            .input-group
                                .input-group-text $
                                input.form-control(type="number", id="amount", inputmode="numeric")

                    .text-center.mb-5
                        button.cta.orange.border-0 Continue

        +footer()
