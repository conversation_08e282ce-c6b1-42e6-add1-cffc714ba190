<?php

namespace App\Livewire;

use App\Models\Donor;
use Livewire\Component;
use Livewire\WithPagination;

class AllDonors extends Component
{
    use WithPagination;

    public $search;

    protected $queryString = ['search'];
    protected $paginationTheme = 'bootstrap';

    public function updatingSearch()
    {
        $this->search = trim($this->search);
        $this->resetPage();
    }

    public function render()
    {
        $donorsQuery = Donor::whereHas('users')
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    if (strtolower($this->search) === 'anonymous') {
                        $query->where('anonymous_for_public', true);
                    } else {
                        $query->where('anonymous_for_public', false)
                            ->where(function ($subQuery) {
                                $subQuery->where('name', 'like', '%' . $this->search . '%')
                                    ->orWhere('email', 'like', '%' . $this->search . '%');
                            });
                    }
                });
            })
            ->with(['users' => function ($query) {}])
            ->orderByDesc('created_at')
            ->paginate(5);



        $donors = $donorsQuery->through(function ($donor) {
            $latestUser = $donor->users
                ->sortByDesc(fn($user) => $user->pivot->created_at)
                ->first();

            return [
                'name' => $donor->anonymous_for_public ? 'Anonymous' : $donor->name,
                'message' => $donor->message_for_fundraiser,
                'amount' => $latestUser?->pivot->amount,
                'created_at' => $latestUser?->pivot->created_at,
                'donated_to' => $latestUser?->name,
            ];
        });

        return view('livewire.all-donors', [
            'recentDonors' => $donors,
        ]);
    }
}


// namespace App\Livewire;

// use App\Models\Donor;
// use Livewire\Component;
// use Livewire\WithPagination;

// class AllDonors extends Component
// {
//     use WithPagination;

//     public $search;

//     protected $queryString = ['search'];
//     protected $paginationTheme = 'bootstrap';

//     public function updatingSearch()
//     {
//         $this->search = trim($this->search);
//         $this->resetPage();
//     }

//     public function render()
//     {


//         $donorsQuery = Donor::query()
//             ->when($this->search, function ($query) {
//                 $query->where(function ($query) {
//                     if (strtolower($this->search) === 'anonymous') {

//                         $query->where('anonymous_for_public', true);
//                     } else {

//                         $query->where('anonymous_for_public', false)
//                             ->where(function ($subQuery) {
//                                 $subQuery->where('name', 'like', '%' . $this->search . '%')
//                                     ->orWhere('email', 'like', '%' . $this->search . '%');
//                             });
//                     }
//                 });
//             })
//             ->orderByDesc('created_at')
//             ->paginate(5);

//         $donors = $donorsQuery->through(function ($donor) {
//             return [
//                 'name' => $donor->anonymous_for_public ? 'Anonymous' : $donor->name,
//                 'amount' => $donor->amount_donate,
//                 'created_at' => $donor->created_at,
//             ];
//         });

//         return view('livewire.all-donors', [
//             'recentDonors' => $donors,
//         ]);
//     }
// }
