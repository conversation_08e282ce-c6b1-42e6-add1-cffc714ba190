<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;

class DonorOfUser extends Component
{
    use WithPagination;

    public $search;

    protected $queryString = ['search'];
    protected $paginationTheme = 'bootstrap';

    public function updatingSearch()
    {
        $this->search = trim($this->search);
        $this->resetPage();
    }

    public function render()
    {
        $user = request()->user();

        $donorsQuery = $user->donors()
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    if (strtolower($this->search) === 'anonymous') {

                        $query->where('anonymous_for_all', true);
                    } else {

                        $query->where('anonymous_for_all', false)
                            ->where(function ($subQuery) {
                                $subQuery->where('name', 'like', '%' . $this->search . '%')
                                    ->orWhere('email', 'like', '%' . $this->search . '%');
                            });
                    }
                });
            })
            ->orderByDesc('user_donors.created_at')
            ->paginate(5);

        $donors = $donorsQuery->through(function ($donor) {
            return [
                'name' => $donor->anonymous_for_all ? 'Anonymous' : $donor->name,
                'amount' => $donor->pivot->amount,
                'created_at' => $donor->pivot->created_at,
                'message' => $donor->message_for_fundraiser,
            ];
        });

        return view('livewire.donor-of-user', [
            'recentDonors' => $donors,
        ]);
    }
}
