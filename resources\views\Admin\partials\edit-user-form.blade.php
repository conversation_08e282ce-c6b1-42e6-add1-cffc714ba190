<form id="editUserForm" method="POST">
    @csrf
    <div class="modal-body p-4">
        <div class="text-center mb-4">
            <div class="position-relative d-inline-block">
                <img src="{{ Storage::url($user->profile_photo) }}"
                    class="rounded-circle img-thumbnail border-3 shadow-lg"
                    style="width: 160px; height: 160px; object-fit: cover; border: 4px solid #6a11cb;"
                    id="userProfilePreview" alt="Profile Picture">
                <div class="position-absolute bottom-0 end-0 d-flex gap-2">
                    <button type="button" id="cropProfileBtn" class="btn btn-warning rounded-circle p-2 shadow"
                        style="background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%); border: none;"
                        title="Crop current profile picture">
                        <i class="fas fa-crop-alt"></i>
                    </button>
                    <label for="userPictureInput" class="btn btn-primary rounded-circle p-2 shadow"
                        style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); border: none;">
                        <i class="fas fa-camera"></i>
                    </label>
                    <input type="file" name="file" class="d-none" id="userPictureInput" accept="image/*">
                </div>
            </div>
            <p class="text-muted mt-2 small">Click camera icon to upload new image or crop icon to edit current picture
            </p>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="fullName" class="form-label fw-medium">
                    <i class="fas fa-user text-primary me-2"></i>Full Name
                </label>
                <input type="text" name="name" class="form-control form-control-lg rounded-pill" id="fullName"
                    value="{{ $user->name }}">
                <input type="hidden" name="id" value="{{ $user->id }}">
            </div>
            <div class="col-md-6 mb-3">
                <label for="goalAmount" class="form-label fw-medium">
                    <i class="fas fa-bullseye text-primary me-2"></i>Goal Amount
                </label>
                <input type="number" name="fundraising_goal" class="form-control form-control-lg rounded-pill"
                    id="goalAmount" value="{{ $user->fundraising_goal }}">
            </div>
        </div>

        <div class="mb-3">
            <label for="email" class="form-label fw-medium">
                <i class="fas fa-envelope text-primary me-2"></i>Email Address
            </label>
            <input type="email" name="email" class="form-control form-control-lg rounded-pill" id="email"
                value="{{ $user->email }}">
        </div>

        <div class="mb-4">
            <label for="messageForDonors" class="form-label fw-medium">
                <i class="fas fa-comment-alt text-primary me-2"></i>Message To Donors
            </label>
            <div class="card shadow-sm border-0">
                <div class="card-body bg-white rounded">
                    <textarea id="messageForDonors" name="fund_raise_message" class="form-control border-0" rows="4">{!! old('fund_raise_message', $user->fund_raise_message) !!}</textarea>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="phone" class="form-label fw-medium">
                    <i class="fas fa-phone text-primary me-2"></i>Phone Number
                </label>
                <input type="tel" class="form-control form-control-lg rounded-pill" id="phone"
                    name="phone_number" value="{{ $user->phone_number }}">
            </div>
            <div class="col-md-6 mb-3">
                <label for="location" class="form-label fw-medium">
                    <i class="fas fa-map-marker-alt text-primary me-2"></i>Address
                </label>
                <input type="text" name="address" class="form-control form-control-lg rounded-pill" id="location"
                    value="{{ $user->address }}">
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="city" class="form-label fw-medium">
                    <i class="fas fa-city text-primary me-2"></i>City
                </label>
                <input type="text" name="city" class="form-control form-control-lg rounded-pill" id="city"
                    value="{{ $user->city }}">
            </div>
            <div class="col-md-6 mb-3">
                <label for="state" class="form-label fw-medium">
                    <i class="fas fa-flag text-primary me-2"></i>State
                </label>
                <input type="text" name="state" class="form-control form-control-lg rounded-pill"
                    id="state" value="{{ $user->state }}">
            </div>
        </div>

        <hr class="my-4">

        <div class="alert alert-warning bg-warning bg-opacity-10 border-start border-warning border-4 rounded-3 p-3"
            role="alert">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-shield-alt fa-2x text-warning me-3"></i>
                </div>
                <div class="flex-grow-1">
                    <h5 class="mb-1">Admin Access</h5>
                    <p class="mb-0">As an admin, you can directly modify user passwords. Use this
                        feature carefully.</p>
                </div>
            </div>
        </div>

        <div class="mb-4">
            <label for="password" class="form-label fw-medium">
                <i class="fas fa-key text-primary me-2"></i>Password
            </label>
            <input type="text" name="password" class="form-control form-control-lg rounded-pill" id="password"
                placeholder="Leave blank to keep current password">
            <small class="text-muted">Enter new password or leave blank to keep current
                password</small>
        </div>
    </div>

    <div class="modal-footer bg-light p-4 rounded-bottom">
        <button type="button" class="btn btn-outline-secondary btn-lg px-4 rounded-pill" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>Cancel
        </button>
        <button type="submit" id="saveUserChanges" class="btn btn-primary btn-lg px-4 rounded-pill"
            style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); border: none;">
            <i class="fas fa-save me-2"></i>Save Changes
        </button>
    </div>
</form>
