@extends('layouts.app')
@section('title', 'Admin Dashboard')
@section('content')

    @include('layouts.admin-nav')

    <section class="p-6">
        <h1 class="text-2xl font-bold mb-4">Subscriptions</h1>

        @if ($subscriptions->count())
            <div class="overflow-x-auto rounded shadow">
                <table class="min-w-full border border-gray-200 bg-white text-sm text-left">
                    <thead class="bg-gray-100 text-gray-700 uppercase text-xs">
                        <tr>
                            <th class="px-4 py-3 border-b">Subscription ID</th>
                            <th class="px-4 py-3 border-b">Customer ID</th>
                            <th class="px-4 py-3 border-b">Frequency</th>
                            <th class="px-4 py-3 border-b">Amount</th>
                            <th class="px-4 py-3 border-b">Start Date</th>
                            <th class="px-4 py-3 border-b">Next Payment</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        @foreach ($subscriptions as $subscription)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 border-b">{{ $subscription->subscription_id }}</td>
                                <td class="px-4 py-3 border-b">{{ $subscription->customer_id }}</td>
                                <td class="px-4 py-3 border-b capitalize">{{ $subscription->payment_frequency }}</td>
                                <td class="px-4 py-3 border-b">${{ number_format($subscription->amount, 2) }}</td>
                                <td class="px-4 py-3 border-b">
                                    {{ \Carbon\Carbon::parse($subscription->start_date)->format('M d, Y') }}</td>
                                <td class="px-4 py-3 border-b">
                                    {{ \Carbon\Carbon::parse($subscription->next_payment_at)->diffForHumans() }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-gray-500 italic">No subscriptions found.</p>
        @endif
    </section>

@endsection
