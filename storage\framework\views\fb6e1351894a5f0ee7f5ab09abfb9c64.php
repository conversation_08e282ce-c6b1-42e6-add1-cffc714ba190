<?php $__env->startSection('title', '2025 ASFL Gauntlet | Fundraiser Profile'); ?>
<?php $__env->startSection('content'); ?>



<section class="profile-hero">
    <div class="container">
        <div class="row align-items-center gx-xl-5">
            <div class="col-md-4">
                <div class="img-player"><img class="w-100" src="<?php echo e(Storage::url($user->profile_photo)); ?>" alt="" /></div>
            </div>
            <div class="col-md-8">
                <div class="name">
                    <h1 class="text-uppercase">
                        Support<br />
                        <strong class="text-orange"><?php echo e($user->name); ?>'s</strong><br />
                        Fundraiser
                    </h1>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="achieve-goal">
    <div class="container-fluid gx-0">
        <div class="bg-blue">
            <div class="container">
                <div class="meta-timeline d-flex justify-content-between">
                    <div class="meta text-center">
                        <h2>
                            Raised:<br />
                            <strong>$<?php echo e(number_format($totalAmount, 0)); ?></strong>
                        </h2>
                    </div>
                    <div class="meta text-center">
                        <h2>
                            Goal:<br />
                            <strong>$<?php echo e(number_format($user->fundraising_goal, 0)); ?></strong>
                        </h2>
                    </div>
                </div>
                <div class="time-line mt-3">
                    <div class="bar">
                        <?php if($totalAmount > 0): ?>
                          <span class="fill align-items-center d-flex"
                                style="width: <?php echo e(min(($totalAmount / $user->fundraising_goal) * 100, 100)); ?>%">
                          </span>
                        <?php endif; ?>
                      </div>
                </div>
            </div>
        </div>
    </div>
</section>

 <?php if($user->fund_raise_message): ?>
<section class="sec your-donation">
			<div class="container">
				<div class="head text-center">
					<h2 class="text-uppercase">Message For Donors</h2>
					<h3 class="mb-0"><?php echo $user->fund_raise_message; ?> </h3>
				</div>

			</div>
		</section>
        <?php endif; ?>




<section class="sec donate-wrap select-level">
    <div class="container">
        <div class="head text-center mb-5">
            <h2 class="text-uppercase">Choose your impact level</h2>
            <h3>Every donation makes a difference in the fight against cancer!</h3>
        </div>
        <div class="select-package row donation-cards-container">
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4 donation-cards">
                <div class="check donation-card" data-amount="25">
                    <input type="radio" name="package" value="supporter" id="supporter" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="supporter">$25 <span>Supporter</span> <span class="tag text-nowrap">Basic Donation</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check star donation-card" data-amount="200">
                    <input type="radio" name="package" value="champion" id="champion" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="champion">$200 <span>Champion</span> <span class="tag text-nowrap">Recommended</span></label>
                    <div class="most-selected d-flex align-items-center flex-column">
                        <div class="icon"><i class="bi bi-star-fill"></i></div>
                        <h2 class="text-uppercase">Popular Choice</h2>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="250">
                    <input type="radio" name="package" value="advocate" id="advocate" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="advocate">$250 <span>Advocate</span> <span class="tag text-nowrap">Supporter</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="500">
                    <input type="radio" name="package" value="hero" id="hero" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="hero">$500 <span>Hero</span> <span class="tag text-nowrap">Generous</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="1000">
                    <input type="radio" name="package" value="guardian" id="guardian" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="guardian">$1,000 <span>Guardian</span> <span class="tag text-nowrap">Major Contribution</span></label>
                </div>
            </div>
        </div>

        <form action="<?php echo e(route('donor.details')); ?>" method="POST" id="customAmountForm">
            <?php echo csrf_field(); ?>
        <div class="text-center mb-5">
            <h2 class="mb-3">Or Enter Your Own Amount</h2>
            <div class="input-price mx-auto mb-4">
                <label class="visually-hidden" for="amount">Amount</label>
                <div class="input-group">
                    <div class="input-group-text">$</div>
                    <input class="form-control donation-amount-input" type="number"  name="amount"  id="donationAmountInput" inputmode="numeric" />
                </div>
            </div>
            <input type="hidden" name="slug" value="<?php echo e($user->slug); ?>">
            <div class="text-center mb-5 donation-proceed-container">
                <button id="proceedToDonationCustom" class="cta orange border-0">Continue</button>
            </div>
        </div>
    </form>
    </div>
</section>
<section class="sec your-donation">
    <div class="container">
        <div class="head text-center mb-5">
            <h2 class="text-uppercase">Your Donation Makes a Difference</h2>
            <h3>Here’s how your contribution helps in the fight against cancer!</h3>
        </div>
        <div class="row">
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src=<?php echo e(asset('images/research-icon.svg')); ?> alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Research</h2>
                        <p>Your donation helps fund vital cancer research to develop new treatments and therapies.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src=<?php echo e(asset('images/support-icon.svg')); ?> alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Support</h2>
                        <p>Provides essential support services for patients and families throughout their cancer journey.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src=<?php echo e(asset('images/prevention-icon.svg')); ?> alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Prevention</h2>
                        <p>Supports education and awareness programs to help prevent cancer through early detection.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('donors-in-donation-page', ['slug'=>$user->slug]);

$__html = app('livewire')->mount($__name, $__params, 'lw-875836453-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>



    /* Donation Cards Styling */
    .donation-cards-container {
        padding: 30px;
        transition: all 0.3s ease;
    }


    .donation-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .donation-card:hover {
        transform: translateY(-10px);
    }


    .donation-input-wrapper:hover {
        box-shadow: 0 20px 40px rgba(106, 17, 203, 0.15);
    }

    .donation-input-group {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .donation-amount-input::placeholder {
        color: #a0a0a0;
    }

    .donation-amount-input:focus + .donation-input-underline {
        height: 5px;
        box-shadow: 0 2px 10px rgba(106, 17, 203, 0.3);
    }

    .donation-description {
        margin-bottom: 30px;
        color: #6c757d;
    }

    .proceed-button:active {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(106, 17, 203, 0.2);
    }
    .cta.orange {

width: 47px !important;
height: 33px;
display: inline-flex;
align-items: center;
justify-content: center;
transition: opacity 0.3s ease;
border: none;
}

.cta-button.clicked {
transform: scale(0.95);
opacity: 0.8;
transition: transform 0.2s ease, opacity 0.2s ease;
}


.cta.orange.disabled {
opacity: 0.5;
cursor: not-allowed;
width: 47px !important;
height: 33px;
display: inline-flex;
align-items: center;
justify-content: center;
transition: opacity 0.3s ease;
border: none;
}

.activities-container {
transition: opacity 0.3s ease;
min-height: 300px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {

    const donationCards = document.querySelectorAll('.donation-card');
    const customAmountInput = document.getElementById('donationAmountInput');
    const customProceedButton = document.getElementById('proceedToDonationCustom');
    let selectedAmount = null;

    customProceedButton.innerHTML="Continue";
    customProceedButton.disabled = false;

    donationCards.forEach(card => {
    card.addEventListener('click', function(e) {
        // Prevent the event from being handled if the radio button itself is clicked
        if (e.target.tagName === 'INPUT') return;




        donationCards.forEach(c => c.classList.remove('selected'));
        this.classList.add('selected');

        selectedAmount = this.getAttribute('data-amount');
        customAmountInput.value = selectedAmount;
    }, { once: false });
});


    // const impactCards = document.querySelectorAll('.impact-card');
    // if (impactCards.length > 0) {
    //     impactCards.forEach(card => {
    //         card.addEventListener('mouseenter', function() {
    //             this.style.transform = 'translateY(-10px)';
    //             this.style.boxShadow = '0 15px 30px rgba(106, 17, 203, 0.15)';
    //         });

    //         card.addEventListener('mouseleave', function() {
    //             this.style.transform = 'translateY(0)';
    //             this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
    //         });
    //     });
    // }


    customProceedButton.addEventListener('click', function(e) {
        e.preventDefault();

        let form = document.getElementById('customAmountForm');
        selectedAmount = customAmountInput.value;

        if (!selectedAmount || selectedAmount <= 0) {

            customAmountInput.style.border = '2px solid #ff5252';
            customAmountInput.style.backgroundColor = 'rgba(255,82,82,0.1)';


            if (!document.querySelector('.error-message')) {
                const errorMessage = document.createElement('div');
                errorMessage.classList.add('error-message');
                errorMessage.style.color = '#ff5252';
                errorMessage.style.marginTop = '10px';
                errorMessage.innerHTML = 'Please enter a valid donation amount';
                document.querySelector('.donation-description').appendChild(errorMessage);


                setTimeout(() => {
                    customAmountInput.style.border = 'none';
                    customAmountInput.style.backgroundColor = 'transparent';
                    if (document.querySelector('.error-message')) {
                        document.querySelector('.error-message').remove();
                    }
                }, 3000);
            }
        } else {



            customProceedButton.innerHTML = '<i class="spinner-border spinner-border-sm text-white" role="status"></i>';
            customProceedButton.disabled = true;


            setTimeout(() => {
                form.submit();

            }, 800);
        }

    });


    customAmountInput.addEventListener('input', function() {
        this.style.border = 'none';
        this.style.backgroundColor = 'transparent';
        if (document.querySelector('.error-message')) {
            document.querySelector('.error-message').remove();
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/User/donationPage.blade.php ENDPATH**/ ?>