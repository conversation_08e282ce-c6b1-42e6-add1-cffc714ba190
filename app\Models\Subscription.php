<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'subscription_id',
        'customer_id',
        'payment_frequency',
        'amount',
        'currency',
        'start_date',
        'next_payment_at',
        'status',
        'current_period_start',
        'current_period_end',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
    ];

    /**
     * Get the donor associated with the subscription.
     */
    public function donor()
    {
        return $this->belongsTo(Donor::class, 'customer_id', 'customer_id');
    }
}
