document.addEventListener('DOMContentLoaded', async function () {
    try {
        const res = await fetch('/home-data');
        const json = await res.json();



        if (!json.success) throw new Error(json.message || 'Unknown error');

        const { totalDonated, goal } = json.data;

        const progressBar = document.querySelector(".progress-bar");
        if (progressBar && goal > 0) {
            const percentage = Math.min(100, (totalDonated / goal) * 100);
            progressBar.style.width = percentage + "%";
        }

        initializeSlider();
        initializeNewsletter();
    } catch (err) {
        console.error("Home page data error:", err);
    }
});




        //slider if we need it in future
        function initializeSlider() {
            const slider = document.getElementById('fundraiserSlider');
            if (!slider) return;

            const slides = slider.querySelectorAll('.custom-slide');
            if (slides.length === 0) return;

            const gap = 25;
            const prevBtn = document.getElementById('sliderPrev');
            const nextBtn = document.getElementById('sliderNext');
            const dotsContainer = document.getElementById('sliderDots');
            const sliderContainer = slider.closest('.custom-slider-container');

            if (!sliderContainer) return;

            let currentIndex = 0;
            let slideWidth = 0;
            let slidesToShow = 1;
            let autoplayInterval = null;
            let isReversed = false;
            let sliderActive = false;
            let touchStartX = 0;
            let touchEndX = 0;

            function updateSlidesToShow() {
                const previousSlidesToShow = slidesToShow;

                if (window.innerWidth >= 1024) {
                    slidesToShow = 3;
                } else if (window.innerWidth >= 768) {
                    slidesToShow = 2;
                } else {
                    slidesToShow = 1;
                }

                const slidesChanged = previousSlidesToShow !== slidesToShow;

                if (slides.length <= slidesToShow) {
                    deactivateSlider();
                } else {
                    activateSlider(slidesChanged);
                }
            }

            function activateSlider(recalculate = false) {
                if (sliderActive && !recalculate) return;

                sliderActive = true;
                sliderContainer.classList.add('slider-active');
                slider.style.justifyContent = 'flex-start';
                slider.style.transition = 'transform 0.5s ease';

                if (prevBtn) prevBtn.style.display = 'flex';
                if (nextBtn) nextBtn.style.display = 'flex';
                if (dotsContainer) dotsContainer.style.display = 'flex';

                const containerWidth = slider.parentElement.offsetWidth;
                slideWidth = (containerWidth - (slidesToShow - 1) * gap) / slidesToShow;

                slides.forEach(slide => {
                    slide.style.flex = `0 0 ${slideWidth}px`;
                    slide.style.maxWidth = `${slideWidth}px`;
                });

                // If recalculating, ensure current index is valid
                if (recalculate) {
                    currentIndex = Math.min(currentIndex, slides.length - slidesToShow);
                    currentIndex = Math.max(0, currentIndex);
                } else {
                    currentIndex = 0;
                }

                moveToSlide(currentIndex, false);
                createDots();
                startAutoplay();
            }

            function deactivateSlider() {
                sliderActive = false;
                sliderContainer.classList.remove('slider-active');

                if (prevBtn) prevBtn.style.display = 'none';
                if (nextBtn) nextBtn.style.display = 'none';
                if (dotsContainer) {
                    dotsContainer.style.display = 'none';
                    dotsContainer.innerHTML = '';
                }

                slider.style.transform = 'translateX(0)';
                slider.style.transition = 'none';

                const containerWidth = slider.parentElement ? slider.parentElement.offsetWidth : 0;

                if (slides.length === 1) {
                    // Handle single slide case
                    slider.style.display = 'flex';
                    slider.style.justifyContent = 'center';
                    slider.style.alignItems = 'center';
                    slider.style.flexDirection = 'column';
                    slider.style.width = '100%';
                    slider.style.maxWidth = 'none';
                    slider.style.transform = 'none';

                    const slideWidth = window.innerWidth <= 768 ? '100%' : '50%';
                    slides[0].style.cssText = `
                all: unset !important;
                display: block !important;
                width: ${slideWidth} !important;
                max-width: 500px !important;
                margin: 0 auto !important;
                text-align: center !important;
            `;
                } else {
                    // Handle multiple slides with no sliding
                    slider.style.justifyContent = 'center';

                    const slidesPerRow = Math.min(slides.length, window.innerWidth >= 1024 ? 3 : (window.innerWidth >= 768 ?
                        2 : 1));
                    const totalGaps = Math.max(0, slidesPerRow - 1) * gap;
                    const flexBasis = containerWidth > 0 ? (containerWidth - totalGaps) / slidesPerRow : 0;

                    slides.forEach(slide => {
                        if (window.innerWidth >= 768) {
                            slide.style.flex = `0 0 ${flexBasis}px`;
                            slide.style.maxWidth = `${flexBasis}px`;
                        } else {
                            slide.style.flex = '0 0 100%';
                            slide.style.maxWidth = '100%';
                        }
                    });
                }

                stopAutoplay();
            }

            function createDots() {
                if (!dotsContainer || !sliderActive) return;

                dotsContainer.innerHTML = '';
                const totalDots = Math.ceil((slides.length - slidesToShow + 1) / 1);

                for (let i = 0; i < totalDots; i++) {
                    const dot = document.createElement('span');
                    dot.classList.add('custom-slider-dot');
                    if (i === Math.floor(currentIndex / 1)) {
                        dot.classList.add('active');
                    }

                    dot.addEventListener('click', () => {
                        moveToSlide(i * 1);
                        stopAutoplay();
                    });

                    dotsContainer.appendChild(dot);
                }
            }

            function updateDots() {
                if (!dotsContainer || !sliderActive) return;

                const dots = dotsContainer.querySelectorAll('.custom-slider-dot');
                const activeDotIndex = Math.floor(currentIndex / 1);

                dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === activeDotIndex);
                });
            }

            function moveToSlide(index, animate = true) {
                if (!sliderActive) return;

                const maxIndex = slides.length - slidesToShow;
                index = Math.max(0, Math.min(index, maxIndex));

                currentIndex = index;
                const translateValue = -currentIndex * (slideWidth + gap);

                slider.style.transition = animate ? 'transform 0.5s ease' : 'none';
                slider.style.transform = `translateX(${translateValue}px)`;

                updateDots();

                if (currentIndex >= slides.length - slidesToShow) {
                    isReversed = true;
                } else if (currentIndex === 0) {
                    isReversed = false;
                }
            }

            function nextSlide() {
                if (!sliderActive) return;
                moveToSlide(currentIndex + 1);
            }

            function prevSlide() {
                if (!sliderActive) return;
                moveToSlide(currentIndex - 1);
            }

            function startAutoplay() {
                if (!sliderActive) return;
                stopAutoplay();
                autoplayInterval = setInterval(() => {
                    if (isReversed) {
                        prevSlide();
                    } else {
                        nextSlide();
                    }
                }, 5000);
            }

            function stopAutoplay() {
                if (autoplayInterval) {
                    clearInterval(autoplayInterval);
                    autoplayInterval = null;
                }
            }

            function handleSwipe() {
                if (!sliderActive) return;
                const swipeThreshold = 50;
                if (touchEndX < touchStartX - swipeThreshold) {
                    nextSlide();
                } else if (touchEndX > touchStartX + swipeThreshold) {
                    prevSlide();
                }
            }


            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    prevSlide();
                    stopAutoplay();
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    nextSlide();
                    stopAutoplay();
                });
            }

            slider.addEventListener('touchstart', (e) => {
                touchStartX = e.changedTouches[0].screenX;
                stopAutoplay();
            }, {
                passive: true
            });

            slider.addEventListener('touchend', (e) => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, {
                passive: true
            });

            slider.addEventListener('mouseenter', stopAutoplay);
            slider.addEventListener('mouseleave', () => {
                if (sliderActive) startAutoplay();
            });


            const resizeObserver = new ResizeObserver(() => {
                updateSlidesToShow();
            });

            resizeObserver.observe(sliderContainer);
            updateSlidesToShow();
        }

        function initializeNewsletter() {
            // Newsletter form handling
            const newsletterForm = document.getElementById('newsletterForm');

            if (newsletterForm) {
                newsletterForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const emailInput = this.querySelector('input[type="email"]');
                    if (!emailInput) return;

                    const email = emailInput.value.trim();
                    if (!email || !isValidEmail(email)) {
                        showAlert('Please enter a valid email address', 'danger');
                        return;
                    }


                    const csrfToken = document.querySelector('meta[name="csrf-token"]');
                    if (!csrfToken) {
                        showAlert('Security token missing. Please refresh the page.', 'danger');
                        return;
                    }

                    let subscribeButton = document.getElementById('subscribeForNewsLetter');

                    const subscribeUrl = route('subscribeForLatestEvents');

                    subscribeButton.disabled = true;
                    subscribeButton.innerHTML =
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Subscribing...`;

                    fetch(subscribeUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                            },
                            body: JSON.stringify({
                                email: email
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                subscribeButton.disabled = false;
                                subscribeButton.innerHTML = 'Subscribe';
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                subscribeButton.disabled = false;
                                subscribeButton.innerHTML = 'Subscribe';
                                emailInput.value = '';
                                showAlert('Thank you! You have successfully subscribed to our newsletter',
                                    'success');
                            } else {
                                subscribeButton.disabled = false;
                                subscribeButton.innerHTML = 'Subscribe';
                                showAlert(data.message || 'An error occurred. Please try again.', 'danger');
                            }
                        })
                        .catch(error => {
                            subscribeButton.disabled = false;
                            subscribeButton.innerHTML = 'Subscribe';
                            console.error('Error:', error);
                            showAlert('An error occurred. Please try again.', 'danger');
                        });
                });
            }

            // Opt-out form handling
            const optOutForm = document.getElementById('optOutForm');
            if (optOutForm) {
                optOutForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const emailInput = document.getElementById('optOutEmail');
                    if (!emailInput) return;

                    const email = emailInput.value.trim();
                    if (!email || !isValidEmail(email)) {
                        showModalAlert('Please enter a valid email address', 'danger');
                        return;
                    }


                    const csrfToken = document.querySelector('meta[name="csrf-token"]');
                    if (!csrfToken) {
                        showModalAlert('Security token missing. Please refresh the page.', 'danger');
                        return;
                    }

                    const unSubscribeUrl = route('unSubscribeFromLatestEvents');

                    let optOutButton = document.getElementById('optOutButton');

                    optOutButton.disabled = true;
                    optOutButton.innerHTML =
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> UnSubscribing...`;

                    fetch(unSubscribeUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                            },
                            body: JSON.stringify({
                                email: email
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                optOutButton.disabled = false;
                                optOutButton.innerHTML = 'UnSubscribe';

                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                optOutButton.disabled = false;
                                optOutButton.innerHTML = 'UnSubscribe';
                                emailInput.value = '';
                                showModalAlert('You have been successfully unsubscribed', 'success');

                                setTimeout(() => {
                                    const modal = bootstrap.Modal.getInstance(document.getElementById(
                                        'optOutModal'));
                                    if (modal) {
                                        modal.hide();
                                    }
                                }, 2000);
                            } else {
                                optOutButton.disabled = false;
                                optOutButton.innerHTML = 'UnSubscribe';
                                showModalAlert(data.message || 'An error occurred. Please try again.',
                                    'danger');
                            }
                        })
                        .catch(error => {
                            optOutButton.disabled = false;
                            optOutButton.innerHTML = 'UnSubscribe';
                            console.error('Error:', error);
                            showModalAlert('An error occurred. Please try again.', 'danger');
                        });
                });
            }
        }


        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showAlert(message, type) {
            const formElement = document.getElementById('newsletterForm');
            if (!formElement) return;


            let alertContainer = document.querySelector('.email-signup .alert-container');
            if (!alertContainer) {
                alertContainer = document.createElement('div');
                alertContainer.className = 'alert-container mt-3';
                formElement.parentNode.insertBefore(alertContainer, formElement.nextSibling);
            }


            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.role = 'alert';
            alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;


            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);


            setTimeout(() => {
                try {
                    if (document.body.contains(alert)) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                } catch (e) {
                    alert.remove();
                }
            }, 5000);
        }

        function showModalAlert(message, type) {
            const formElement = document.getElementById('optOutForm');
            if (!formElement) return;


            let alertContainer = document.querySelector('#optOutModal .modal-alert-container');
            if (!alertContainer) {
                alertContainer = document.createElement('div');
                alertContainer.className = 'modal-alert-container mb-3';
                formElement.parentNode.insertBefore(alertContainer, formElement);
            }


            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.role = 'alert';
            alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;


            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);
            setTimeout(() => {
                try {
                    if (document.body.contains(alert)) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                } catch (e) {
                    alert.remove();
                }
            }, 5000);
        }
