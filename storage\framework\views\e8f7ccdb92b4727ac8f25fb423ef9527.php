<?php $__env->startSection('title', '2025 ASFL Gauntlet | Home'); ?>

<?php $__env->startSection('meta_description', 'Step onto the court for 24 hours of purpose. The ASFL Gauntlet brings players together to honor, uplift, and fight for those facing cancer.'); ?>

<?php $__env->startSection('content'); ?>



    <section class="hero">
        <div class="container-fluid gx-0">
            <div class="hero-img">
               <img class="w-100 d-none d-md-block"
                    src="<?php echo e(asset('images/newHero.webp')); ?>"
                    alt="banner desktop"
                    style="aspect-ratio: 1351 / 461;"
                    decoding="async" />
                <img class="w-100  d-md-none" src="<?php echo e(asset('images/Banner-mob1.jpg')); ?>" alt="banner mobile"
                  loading="lazy"
                   decoding="async" />
            </div>
        </div>
        <div class="hero__text">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h1 class="text-uppercase mb-3">A Shot for Life <strong class="d-block">Gauntlet</strong></h1>
                        <p><strong>Join the A Shot for Life Gauntlet:</strong> Fundraise, Inspire, and Make a Difference in
                            the Fight Against Cancer!</p>
                        <div class="cta-row"><a class="cta orange hoverbutton" href="<?php echo e(route('register')); ?>">Register</a><a
                                class="cta blue hoverbutton" href="<?php echo e(route('donate')); ?>">Donate</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="achieve-goal">
        <div class="container-fluid gx-0">
            <div class="bg-blue">
                <div class="container">
                    <div class="meta-timeline d-flex justify-content-between">
                        <div class="meta text-center">
                            <h2>
                                Help Us Achieve Our Goal:<br />
                                <strong>$<?php echo e(preg_replace('/\.00$/', '', number_format($data['goal'], 2))); ?></strong>


                            </h2>
                        </div>
                        <div class="meta text-center">
                            <h2>
                                Active Participants:<br />

                                <a href="<?php echo e(route('leaderBoard')); ?>" style="text-decoration: none">
                                <strong><?php echo e($data['participants']); ?></strong>
                                </a>
                            </h2>
                        </div>
                    </div>
                    <div class="time-line mt-5">
                        <div class="meta-list d-flex justify-content-between"><span class="label first">Let’s
                                Go!</span><span class="label">Keep Going!</span><span class="label">Almost
                                there!</span><span class="label last">You Did it!</span></div>
                        <div class="bar mt-4">
                            <span class="fill align-items-center d-flex"
                                style="width: <?php echo e(min(($data['totalDonated'] / $data['goal']) * 100, 100)); ?>%">
                                <!-- Sum positioned based on width -->
                                <?php if(($data['totalDonated'] / $data['goal']) * 100 < 15): ?>
                                    <span class="sum position-absolute"
                                        style="left: calc(<?php echo e(min(($data['totalDonated'] / $data['goal']) * 100, 100)); ?>% + 10px);">$<?php echo e(number_format($data['totalDonated'])); ?></span>
                                <?php else: ?>
                                    <span class="sum">$<?php echo e(number_format($data['totalDonated'])); ?></span>
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec leaderboard">
        <div class="container">
            <div class="head text-center">
                <h2 class="text-uppercase">LEADERBOARD</h2>
            </div>
            <div class="row justify-content-center">

                <?php $__empty_1 = true; $__currentLoopData = $data['topFundraisers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fundraiser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-sm-6 col-lg-4 col-xl-3 mt-5">
                        <div class="leader_box d-flex flex-column text-center align-items-center">
                            <a href="<?php echo e($fundraiser->donationPageUrl); ?>" class="text-decoration-none">
                                <div class="img mb-3">
                                    <img src="<?php echo e(Storage::url($fundraiser->profile_photo)); ?>"
                                        alt="<?php echo e("fundraiser". ' '. $fundraiser->name); ?>" />
                                </div>
                                <div class="text">
                                    <h2 class="mb-0"><?php echo e($fundraiser->name); ?></h2>
                                </div>
                            </a>
                            <div class="text">
                                <h3 class="price">$<?php echo e($fundraiser->total_collected); ?> out of
                                    $<?php echo e($fundraiser->fundraising_goal); ?></h3>
                            </div>
                        </div>
                    </div>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="custom-slider w-100" id="noFundraisers">
                        <div class="py-5 px-4 text-center mx-auto"
                            style="
                background-color: rgba(106,17,203,0.05);
                border-radius: 12px;
                height: 420px;
                max-width: 500px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            ">
                            <img src="https://cdn-icons-png.flaticon.com/512/2748/2748558.png" alt="No fundraisers"
                                class="mb-4" style="opacity: 0.5; width: 100px;">
                            <h4 style="color: #19345e;">No Fundraisers Yet</h4>
                            <p class="text-muted mb-4">Be the first to start fundraising!</p>
                            <a href="<?php echo e(route('register')); ?>" class="btn text-white px-4 py-2"
                                style="
                    background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
                    border-radius: 50px;
                ">
                                Start Now
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="cta-row mt-5 text-center"><a class="cta orange hoverbutton"
                        href="<?php echo e(route('leaderBoard')); ?>">See All</a></div>
            </div>
        </div>
    </section>
    <section class="hording">
        <div class="container-fluid gx-0">
            <div class="img">
                <img class="w-100 d-none d-md-block" src="<?php echo e(asset('images/wGauntlet.jpg')); ?>" alt="" />
                <img class="w-100  d-md-none" src="<?php echo e(asset('images/Banner-mob2.jpg')); ?>" alt="" />
            </div>
        </div>
        <div class="hording__text">
            <div class="container">
                <div class="row justify-content-md-end">
                    <div class="col-md-7 text-md-end py-5 d-flex flex-column align-items-md-end">
                        <h2 class="text-uppercase mb-0">What is A Shot for Life <strong
                                class="d-md-block">Gauntlet?</strong></h2>
                        <div class="p-size my-3 my-lg-4">
                            <p>The A Shot for Life Gauntlet is your chance to step up, unleash your inner champion, and
                                score big in the fight against cancer with every dollar you raise. Join a thrilling showdown
                                of grit and heart, where your passion fuels a game-changing impact for an unbeatable cause!
                            </p>
                        </div>
                        <div class="cta-row"><a class="cta orange hoverbutton" href="<?php echo e(route('register')); ?>">Register
                                Now</a><a class="cta blue hoverbutton" href="<?php echo e(route('about')); ?>">Find Out More</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('all-donors');

$__html = app('livewire')->mount($__name, $__params, 'lw-1303741213-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    <section class="hording">
        <div class="container-fluid gx-0">
            <div class="img">
                   <img class="w-100 d-none d-md-block" src="<?php echo e(asset('images/ourImpact.jpg')); ?>" alt="" />
                <img class="w-100  d-md-none" src="<?php echo e(asset('images/Banner-mob3.jpg')); ?>" alt="" />
            </div>
        </div>
        <div class="hording__text">
            <div class="container">
                <div class="row">
                  <div class="col-md-7 py-5">
                   <h2 class="text-uppercase mb-0">Our <strong>Impact</strong></h2>
                    <div class="p-size my-3 my-lg-4">
                        <blockquote class="blockquote mb-3">
                            <p class="mb-2">
                                “The tremendous support from A Shot For Life is instrumental in enabling our group to execute
                                studies that enhance our understanding of the immune system in the context of malignant
                                brain tumors and provides hope that we can continue to provide meaningful, new therapies for
                                our patients. Thanks to ASFL for leading the way and making it all possible.”
                            </p>
                            <footer class="blockquote-footer mt-2">MGH Cancer Center</footer>
                        </blockquote>
                    </div>
                    <div class="cta-row">
                        <a class="cta blue hoverbutton" href="<?php echo e(route('about')); ?>">Find Out More</a>
                    </div>
                </div>

                </div>
            </div>
        </div>
    </section>
    <section class="make-difference">
        <div class="container-fluid gx-0">
            <div class="bg-blue text-center">
                <div class="container">
                    <h2 class="text-uppercase mb-4">How to make <strong>a difference!</strong></h2>
                    <p>
                        Join the A Shot for Life Gauntlet: Fundraise, Inspire, and<br class="d-none d-md-block" />
                        Make a Difference in the Fight Against Cancer!
                    </p>
                    <div class="cta-row mt-5"><a class="cta orange hoverbutton"
                            href="<?php echo e(route('register')); ?>">Register</a><a class="cta blue hoverbutton"
                            href="<?php echo e(route('donate')); ?>">Donate</a></div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
 <?php echo app('Illuminate\Foundation\Vite')('resources/js/pages/home.js'); ?>
<?php $__env->stopPush(); ?>














<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/home/<USER>/ ?>