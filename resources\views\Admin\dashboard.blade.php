@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('no-header-footer', 'true')

@section('content')




    @include('layouts.admin-nav')

    <section class="header-section text-white"
        style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); padding: 50px 0;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="fw-bold" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        Admin Dashboard
                    </h1>
                    <p class="lead" style="color: rgba(255,255,255,0.9);">
                        Manage users, track fundraising progress, and oversee event operations
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                    <span class="d-block text-white-50 mb-1">Last Login</span>
                    <span class="fs-5">
                        {{ \Carbon\Carbon::parse($data['user']->last_login_at)->format('F d, Y - h:i A') ?? 'Never Logged In' }}
                    </span>
                </div>
            </div>
        </div>
    </section>

    <section class="stats-section py-4">
        <div class="container">
            <div class="row g-4">
                <!-- Total Users -->
                <div class="col-md-3">
                    <div class="stat-card p-3 h-100">
                        <div class="d-flex align-items-center">
                            <div class="p-3 rounded-circle me-3" style="background: #19345e;">
                                <i class="fas fa-users" style="color: #e4eaf4; font-size: 24px;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" style="color: #19345e;">{{ $data['totalUsers'] }}</h3>
                                <p class="text-muted mb-0">Total Users</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Raised -->
                <div class="col-md-3">
                    <div class="stat-card p-3 h-100">
                        <div class="d-flex align-items-center">
                            <div class="p-3 rounded-circle me-3" style="background: #19345e;">
                                <i class="fas fa-dollar-sign" style="color: #e4eaf4; font-size: 24px;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" style="color: ##19345e;">
                                    ${{ number_format($data['totalCollected'] ?? 0, 0) }}</h3>
                                <p class="text-muted mb-0">Total Raised</p>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="col-md-3">
                    <div class="stat-card p-3 h-100">
                        <div class="d-flex align-items-center">
                            <div class="p-3 rounded-circle me-3" style="background: #19345e;">
                                <i class="fas fa-chart-line" style="color: #e4eaf4; font-size: 24px;"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" style="color: #19345e;">{{ $data['totalDonors'] ?? 0 }}</h3>
                                <p class="text-muted mb-0">Total Donors</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Goal Progress -->
                <div class="col-md-3">
                    <div class="stat-card p-3 h-100">
                        <div class="d-flex align-items-center">
                            <div class="p-3 rounded-circle me-3" style="background: rgba(37,117,252,0.1);">
                                <i class="fas fa-bullseye" style="color: #19345e; font-size: 24px;"></i>
                            </div>
                            <div class="w-100">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h3 class="mb-0" style="color: #19345e;">
                                        {{ number_format((($data['totalCollected'] ?? 0) / ($data['goal'] ?? 23000)) * 100, 1) }}%
                                    </h3>
                                    <span class="text-muted">${{ number_format($data['goal'] ?? 0, 0) }}</span>
                                </div>
                                <div class="progress mt-1" style="height: 8px; background-color: #e4eaf4;">
                                    <div class="progress-bar"
                                        style="width: {{ (($data['totalCollected'] ?? 0) / $data['goal']) * 100 }}%; background: #f05522;">
                                    </div>
                                </div>
                                <div class="mt-2 d-flex justify-content-between">
                                    <button type="button" class="btn btn-sm"
                                        style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); color: white;"
                                        data-bs-toggle="modal" data-bs-target="#editGoalModal">
                                        <i class="fas fa-edit me-1"></i> Edit Goal
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="editGoalModal" tabindex="-1" aria-labelledby="editGoalModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header"
                        style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); color: white;">
                        <h5 class="modal-title" id="editGoalModalLabel">Edit Current Goal</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <form action="{{ route('admin.editCurrrentYearGoal') }}" id="editGoalForm" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="currentGoal" class="form-label">Current Goal Amount ($)</label>
                                <input type="number" name="goalAmount" class="form-control" id="currentGoal"
                                    value="{{ $data['goal'] }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="goalYear" class="form-label">Year</label>
                                <input type="number" class="form-control" id="goalYear" name="year"
                                    value="{{ date('Y') }}" readonly>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" id="saveChangesButton" class="btn text-white"
                                style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">Save
                                Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    <!-- Account Creation Section -->
    <section class="account-section py-4">
        <div class="container">
            <div class="row g-4">
                <!-- Add User Account Button -->
                <div class="col-md-6">
                    <div class="admin-card p-4 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 style="color: #19345e;">User Management</h4>
                            <span class="badge rounded-pill"
                                style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">{{ $data['totalUsers'] ?? 0 }}
                                Users</span>
                        </div>
                        <p class="text-muted mb-4">Create and manage user accounts for participants</p>
                        <button type="button" class="btn gradient-btn btn-lg rounded-pill px-4 py-2"
                            data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-user-plus me-2"></i> Add User Account
                        </button>
                    </div>
                </div>

                <!-- Add Admin Account Button -->
                <div class="col-md-6">
                    <div class="admin-card p-4 h-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4 style="color: #19345e;">Admin Management</h4>
                            <span class="badge rounded-pill"
                                style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">
                                {{ $data['totalAdmins'] ?? 0 }}
                                {{ ($data['totalAdmins'] ?? 0) == 1 ? 'Admin' : 'Admins' }}
                            </span>

                        </div>
                        <p class="text-muted mb-4">Add administrators with full dashboard access</p>
                        <button type="button" class="btn gradient-btn btn-lg rounded-pill px-4 py-2"
                            data-bs-toggle="modal" data-bs-target="#addAdminModal">
                            <i class="fas fa-user-shield me-2"></i> Add Admin Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">
                    <h5 class="modal-title text-white" id="addUserModalLabel">
                        <i class="fas fa-user-plus"></i> Add User Account
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="userRegistrationForm" method="POST" action="#" enctype="multipart/form-data">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 field-animation delay-1">
                                    <input type="text" class="form-control" id="name" name="name" required
                                        placeholder="Full Name" style="border-color: #19345e;">
                                    <label for="name" style="color: #19345e;"><i class="fas fa-user"></i> Full
                                        Name</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3 field-animation delay-1">
                                    <input type="email" class="form-control" id="email" name="email" required
                                        placeholder="Email Address" style="border-color: #19345e;">
                                    <label for="email" style="color: #19345e;"><i class="fas fa-envelope"></i> Email
                                        Address</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 field-animation delay-2">
                                    <input type="text" class="form-control" id="address" name="address" required
                                        placeholder="Address" style="border-color: #19345e;">
                                    <label for="address" style="color: #19345e;"><i class="fas fa-home"></i>
                                        Address</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3 field-animation delay-2">
                                    <input type="text" class="form-control" id="city" name="city" required
                                        placeholder="City" style="border-color: #19345e;">
                                    <label for="city" style="color: #19345e;"><i class="fas fa-city"></i>
                                        City</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-floating mb-3 field-animation delay-3">
                                    <input type="text" class="form-control" id="state" name="state" required
                                        placeholder="State" style="border-color: #19345e;">
                                    <label for="state" style="color: #19345e;"><i class="fas fa-map-marked-alt"></i>
                                        State</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="input-icon-wrapper field-animation delay-3">
                                    <div class="form-floating mb-3">
                                        <input type="password" class="form-control" id="password" name="password"
                                            required placeholder="Password" style="border-color: #19345e;">
                                        <label for="password" style="color: #19345e;"><i class="fas fa-lock"></i>
                                            Password</label>
                                    </div>
                                    <i class="fas fa-eye-slash input-icon toggle-password"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced File Upload -->
                        <div class="row mb-3 field-animation delay-4">
                            <div class="col-md-12">
                                <label for="fileUpload" class="form-label"><i class="fas fa-camera"></i> Profile
                                    Picture</label>
                                <div class="file-upload-container" id="upload-container">
                                    <div id="upload-prompt">
                                        <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: #19345e;"></i>
                                        <p class="mb-1">Drag & drop your image here or click to browse</p>
                                        <small class="text-muted">Supported formats: JPG, PNG, GIF</small>
                                    </div>
                                    <div id="preview-container" style="display: none;">
                                        <img id="image-preview" class="img-thumbnail"
                                            style="max-height: 150px; max-width: 100%;" alt="Preview">
                                        <div class="mt-2">
                                            <button type="button" id="remove-image"
                                                class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                    <input type="file" class="form-control" id="fileUpload" name="file"
                                        accept="image/*">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3 field-animation delay-4">
                            <div class="col-12">
                                <label for="message" class="form-label"><i class="fas fa-comment-dots"></i> Message to
                                    Donors</label>
                                <textarea name="fund_raise_message" id="message" rows="5" class="form-control"></textarea>
                            </div>
                        </div>

                        <div class="mb-3 field-animation delay-5">
                            <label for="fundraisingGoal" class="form-label"><i class="fas fa-bullseye"></i> Goal
                                Amount</label>
                            <div class="input-group">
                                <span class="input-group-text" style="background: #19345e; color: white;"><i
                                        class="fas fa-dollar-sign"></i></span>
                                <input type="number" class="form-control" id="fundraisingGoal" name="fund_raise_goal"
                                    min="1000" required placeholder="Enter the Goal for Athlete">
                            </div>
                            <small class="form-text text-muted">Minimum $1000</small>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button type="submit" id="addUserAccount" class="gradient-btn">
                                <i class="fas fa-check"></i> Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <!-- Add Admin Modal -->
    <div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">
                    <h5 class="modal-title text-white" id="addAdminModalLabel">
                        <i class="fas fa-user-shield"></i> Add Admin Account
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAdminForm">
                        <div class="mb-3 field-animation delay-1">
                            <label for="adminName" class="form-label"><i class="fas fa-id-card"></i> Full Name</label>
                            <div class="input-group">
                                <span class="input-group-text" style="background: #19345e; color: white;"><i
                                        class="fas fa-user"></i></span>
                                <input type="text" name="adminName" class="form-control" id="adminName"
                                    placeholder="Enter full name">
                            </div>
                        </div>
                        <div class="mb-3 field-animation delay-2">
                            <label for="adminEmail" class="form-label"><i class="fas fa-at"></i> Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text" style="background: #19345e; color: white;"><i
                                        class="fas fa-envelope"></i></span>
                                <input type="email" name="adminEmail" class="form-control" id="adminEmail"
                                    placeholder="Enter email address">
                            </div>
                        </div>
                        <div class="mb-3 field-animation delay-3">
                            <label for="adminPassword" class="form-label"><i class="fas fa-key"></i> Password</label>
                            <div class="input-group">
                                <span class="input-group-text" style="background: #19345e; color: white;"><i
                                        class="fas fa-lock"></i></span>
                                <input type="password" name="adminPassword" class="form-control" id="adminPassword"
                                    placeholder="Create password">
                                <button class="btn btn-outline-secondary toggle-password-btn" type="button">
                                    <i class="fas fa-eye-slash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3 form-check field-animation delay-4">
                            <input type="checkbox" name="sendAdminCredentials" class="form-check-input"
                                id="sendAdminCredentials">
                            <label class="form-check-label" for="sendAdminCredentials">
                                <i class="fas fa-paper-plane"></i> Send login credentials via email
                            </label>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button type="submit" id="createAdminButton" class="gradient-btn">
                                <i class="fas fa-user-plus"></i> Create Admin
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- edit user modal-->

    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content rounded-lg shadow-lg">
                <div class="modal-header position-relative p-0">
                    <div class="w-100 bg-gradient-primary text-white p-4 rounded-top"
                        style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);">
                        <h4 class="modal-title fw-bold mb-0" id="editUserModalLabel">
                            <i class="fas fa-user-cog me-2"></i>Edit User Details
                        </h4>
                        <p class="text-white-50 mb-0">Manage user profile and credentials</p>
                    </div>
                    <button type="button" class="btn-close btn-close-white position-absolute top-0 end-0 m-3"
                        data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <!--form will be added here -->
                <div id="editUserFormContainer"></div>

            </div>
        </div>
    </div>

    <section>

        <div class="row justify-content-center mb-4">
            <div class="col-md-8">
                <div class="search-filter-container p-3 rounded-pill shadow-sm"
                    style="background: linear-gradient(145deg, #f0f0f0, #ffffff);">
                    <div class="row g-2 align-items-center">
                        <div class="col-md-7">
                            <div class="input-group">
                                <span class="input-group-text border-0 bg-transparent">
                                    <i class="fas fa-search" style="color: #19345e;"></i>
                                </span>
                                <input type="text" id="search" class="form-control border-0 bg-transparent"
                                    placeholder="Search fundraisers..." style="box-shadow: none;">
                            </div>
                        </div>
                        <div class="col-md-5">
                            <select id="sort-filter" class="form-select border-0 bg-transparent"
                                style="box-shadow: none; color: #19345e; font-weight: 500;">
                                <option value="">Apply Filter ↓</option>
                                <option value="total_collected">Amount Raised</option>
                                <option value="name">Name (A-Z)</option>
                                <option value="city">Town (A-Z)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="user-container" class="row justify-content-center">
            <div class="col-md-10">
                <div class="card admin-card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="py-3 px-4 fw-bold" style="color: #19345e;">Name</th>
                                        <th class="py-3 px-4 fw-bold" style="color: #19345e;">City</th>
                                        <th class="py-3 px-4 fw-bold text-center" style="color: #19345e;">Total Collected
                                        </th>
                                        <th class="py-3 px-4 fw-bold text-center" style="color: #19345e;">
                                            Actions
                                            <span class="ms-1 text-muted" style="font-size: 0.8rem;">
                                                <i class="fas fa-eye" title="View"></i>
                                                <i class="fas fa-edit mx-1" title="Edit"></i>
                                                <i class="fas fa-trash-alt" title="Delete"></i>
                                            </span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="table-body">
                                    <!-- Table content will be inserted here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Leaderboard pagination">
                <ul id="pagination-links" class="pagination pagination-lg"></ul>
            </nav>
        </div>
    </section>

    <!-- delete confimation -->

    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete the Athlete?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                    <button type="button" id="confirmDeleteBtn" class="btn btn-danger">
                        <i class="fas fa-spinner fa-spin me-2 d-none"></i> Yes, Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Crop Modal for Admin -->
    <div class="modal fade" id="imageCropModal" tabindex="-1" aria-labelledby="imageCropModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content border-0" style="border-radius: 20px; overflow: hidden;">
                <div class="modal-header border-0"
                    style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%); color: white;">
                    <h5 class="modal-title" id="imageCropModalLabel" style="font-family: 'Anaheim', sans-serif;">
                        <i class="fas fa-crop-alt me-2"></i>Crop Profile Picture
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="text-center mb-3">
                        <p class="text-muted" style="font-family: 'Inter', sans-serif;">
                            Drag and resize the crop area to frame the profile picture perfectly
                        </p>
                    </div>

                    <div class="crop-container-wrapper">
                        <div class="crop-container" id="cropContainer">
                            <img id="cropImage" src="" alt="Crop Image"
                                style="max-width: 100%; display: block;">
                        </div>
                    </div>

                    <div class="crop-controls mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="font-family: 'Inter', sans-serif;">
                                    <i class="fas fa-rotate-right text-primary me-2" style="color: #f05522;"></i>Rotation
                                </label>
                                <input type="range" class="form-range" id="rotationSlider" min="0"
                                    max="360" value="0">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">0°</small>
                                    <small class="text-muted">360°</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="font-family: 'Inter', sans-serif;">
                                    <i class="fas fa-search-plus text-primary me-2" style="color: #f05522;"></i>Zoom
                                </label>
                                <input type="range" class="form-range" id="zoomSlider" min="0.5" max="3"
                                    step="0.1" value="1">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">50%</small>
                                    <small class="text-muted">300%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-light p-4">
                    <button type="button" class="btn btn-outline-secondary rounded-pill px-4" data-bs-dismiss="modal"
                        style="font-family: 'Inter', sans-serif;">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" id="resetCropBtn" class="btn btn-outline-warning rounded-pill px-4"
                        style="font-family: 'Inter', sans-serif;">
                        <i class="fas fa-undo me-2"></i>Reset
                    </button>
                    <button type="button" id="applyCropBtn" class="btn btn-primary rounded-pill px-4"
                        style="background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); border: none; font-family: 'Inter', sans-serif;">
                        <i class="fas fa-check me-2"></i>Apply Crop
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection


@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
    <style>
        body {
            margin: 0px;
            padding: 0px;
            overflow-x: hidden;
        }


        .modal.fade .modal-dialog {
            transform: translateY(-20px);
            transition: transform 0.3s ease-out;
        }

        .modal.show .modal-dialog {
            transform: translateY(0);
        }

        #search:focus {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
        }

        .modal-content {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: none;
            animation: modalFadeIn 0.4s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .form-floating input:focus,
        .form-control:focus {
            box-shadow: 0 0 0 3px #154da3;
            border-color: #19345e;
            transition: all 0.3s ease;
        }

        .gradient-btn {
            background: linear-gradient(135deg, #f05522 0%, #f5602f 100%);
            color: white;
            border: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            padding: 10px 20px;
        }

        .gradient-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(253, 175, 72, 0.4);
        }

        .gradient-btn:active {
            transform: translateY(0);
        }

        .gradient-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: width 0.3s ease, height 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .gradient-btn:hover::after {
            width: 200%;
            height: 200%;
        }

        .form-floating input {
            border-width: 2px;
            transition: all 0.3s ease;
        }

        .form-floating input:hover {
            border-color: #19345e;
        }

        .form-control,
        .form-select {
            border-radius: 8px;
            padding: 12px;
        }

        .modal-header {
            padding: 15px 25px;
            border-bottom: none;
        }

        .modal-title {
            font-weight: 700;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
        }

        .modal-title i {
            margin-right: 10px;
        }

        .btn-close-white {
            transition: transform 0.3s ease;
        }

        .btn-close-white:hover {
            transform: rotate(90deg);
        }

        .input-icon-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #19345e;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .input-icon:hover {
            transform: translateY(-50%) scale(1.1);
        }

        .file-upload-container {
            position: relative;
            border: 2px dashed #19345e;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .file-upload-container:hover {
            background-color: rgba(106, 17, 203, 0.05);
        }

        .file-upload-container input[type="file"] {
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        #preview-container {
            width: 100%;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        #image-preview {
            border: 3px solid #19345e;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        #image-preview:hover {
            transform: scale(1.05);
        }

        #remove-image {
            transition: all 0.2s ease;
        }

        #remove-image:hover {
            background-color: #dc3545;
            color: white;
        }

        .form-check-input:checked {
            background-color: #19345e;
            border-color: #19345e;
        }

        label.form-label {
            font-weight: 600;
            color: #444;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        label.form-label i {
            margin-right: 8px;
            color: #19345e;
        }

        textarea.form-control {
            min-height: 120px;
        }

        .field-animation {
            animation: fieldAppear 0.5s ease-out;
        }

        @keyframes fieldAppear {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }


        .delay-1 {
            animation-delay: 0.1s;
        }

        .delay-2 {
            animation-delay: 0.2s;
        }

        .delay-3 {
            animation-delay: 0.3s;
        }

        .delay-4 {
            animation-delay: 0.4s;
        }

        .delay-5 {
            animation-delay: 0.5s;
        }

        .form-floating>label {
            padding: 12px;
        }

        .admin-card {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            overflow: hidden;
            border: none;
        }

        .admin-card:hover {
            box-shadow: 0 8px 25px rgba(106, 17, 203, 0.1);
            transform: translateY(-2px);
        }

        .table-hover tbody tr {
            transition: all 0.2s ease;
        }

        .table-hover tbody tr:hover {
            background-color: #193453;
        }

        .table th {
            border-top: none;
            border-bottom: 2px solid #19345e;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .table td {
            padding: 1rem 1.25rem;
            vertical-align: middle;
            border-color: rgba(0, 0, 0, 0.05);
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            margin: 0 3px;
            color: white;
        }

        .view-btn {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
        }

        .edit-btn {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .delete-btn {
            background: linear-gradient(135deg, #f05522 0%, #f38d6b 100%);
        }

        .action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .search-filter-container {
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .pagination .page-item .page-link {
            border-radius: 50%;
            margin: 0 5px;
            color: #19345e;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #f05522 0%, #f05522 100%);
            color: white;
            box-shadow: 0 4px 10px #fab7a1;
        }

        .pagination .page-item .page-link:hover {
            background-color: #f0f0f0;
            transform: scale(1.05);
        }

        .pagination .page-item.active .page-link:hover {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
        }

        .loader-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px 0;
        }

        .fundraiser-amount {
            font-weight: 600;
            color: #19345e;
            background: linear-gradient(to right, #19345e, #154da3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .empty-state-container {
            text-align: center;
            padding: 50px 0;
        }

        /* Cropper.js custom styles for admin */
        .crop-container-wrapper {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .crop-container {
            max-height: 600px;
            min-height: 500px;
            overflow: hidden;
            border-radius: 8px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .crop-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .cropper-container {
            border-radius: 8px;
            max-height: 600px !important;
            width: 100% !important;
        }

        /* Ensure cropper image fills the container properly */
        .cropper-container .cropper-image {
            max-width: none !important;
            max-height: none !important;
        }

        .cropper-view-box {
            border-radius: 50%;
            outline: 3px solid #f05522;
            outline-offset: 3px;
        }

        .cropper-face {
            background-color: rgba(240, 85, 34, 0.1);
        }

        .cropper-line,
        .cropper-point {
            background-color: #f05522;
        }

        .cropper-view-box {
            box-shadow: 0 0 0 2px #f05522;
        }

        /* Make cropper handles larger and more visible */
        .cropper-point {
            width: 12px !important;
            height: 12px !important;
            opacity: 1 !important;
            background-color: #f05522 !important;
            border: 2px solid white !important;
        }

        .cropper-line {
            background-color: #f05522 !important;
            opacity: 0.8 !important;
        }

        /* Improve modal size for better cropping experience */
        .modal-xl {
            max-width: 90vw;
        }

        @media (min-width: 1200px) {
            .modal-xl {
                max-width: 1140px;
            }
        }

        .form-range {
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(to right, #e9ecef 0%, #e9ecef 100%);
        }

        .form-range::-webkit-slider-thumb {
            background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(240, 85, 34, 0.3);
        }

        .form-range::-moz-range-thumb {
            background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(240, 85, 34, 0.3);
        }

        .crop-controls {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        /* Loading overlay for crop modal */
        .crop-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 8px;
        }

        .crop-loading .spinner-border {
            color: #f05522;
        }
    </style>
@endpush
@push('scripts')
    <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
    @vite('resources/js/admin/admin-dashboard.js')
@endpush
