@extends('layouts.app')

@section('title', 'Email Template Builder')
@section('no-header-footer', 'true')

@section('content')

@include('layouts.admin-nav')

<div class="container py-4">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="header-container">
                    <canvas id="threejs-canvas"></canvas>
                    <div class="content-overlay">
                        <div class="title-section">
                            <h4>EMAIL TEMPLATE BUILDER</h4>
                            <p class="tagline">Build Your Email Templates Effortlessly</p>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.send-mail') }}" method="POST" id="emailForm">
                        @csrf
                        <input type="hidden" name="html_content" id="html_content">

                        <!-- Email Details -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template_name" class="form-label">Template Name</label>
                                    <input type="text" name="template_name" id="template_name" class="form-control" placeholder="e.g., Welcome Email, Newsletter" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Email Subject</label>
                                    <input type="text" name="subject" id="subject" class="form-control" placeholder="Enter email subject line" required>
                                </div>
                            </div>
                        </div>

                        <!-- Saved Templates Selection -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="saved_template" class="form-label">Load Saved Template</label>
                                    <select name="saved_template" id="saved_template" class="form-select">
                                        <option value="">-- Select a saved template --</option>
                                        @if(isset($templates) && count($templates) > 0)
                                            @foreach($templates as $template)
                                                <option value="{{ $template->id }}">{{ $template->name }} ({{ ucfirst($template->category) }})</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    <small class="text-muted">Choose a saved template to load</small>
                                </div>
                            </div>
                        </div>

                        <!-- Recipients selection dropdown -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="recipient_group" class="form-label">Select Recipients</label>
                                    <select name="recipient_group" id="recipient_group" class="form-select" required>
                                        <option value="">-- Select recipient group --</option>
                                        <option value="subscribers">All Subscribers</option>
                                        <option value="athletes">All Athletes</option>
                                        {{-- <option value="athletes_completed_goals">Athletes with Completed Goals (Last 7 Days)</option> --}}
                                        <option value="athletes_incomplete_goals">Athletes who have not Completed Goals</option>
                                    </select>
                                    <small class="text-muted">Choose which group of users will receive this email</small>
                                </div>
                            </div>
                        </div>

                        <!-- Template Builder -->
                        <div class="row">
                            <div class="col-md-9">
                                <!-- Email Body Editor -->
                                <div class="mb-3">
                                    <label for="emailContent" class="form-label">Design Your Email</label>
                                    <textarea id="emailContent"></textarea>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <!-- Template Components -->
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Template Library</h5>
                                    </div>
                                    <div class="card-body p-2">
                                        <div class="accordion" id="templateComponents">
                                            <!-- Headers -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#headersCollapse">
                                                        Headers
                                                    </button>
                                                </h2>
                                                <div id="headersCollapse" class="accordion-collapse collapse" data-bs-parent="#templateComponents">
                                                    <div class="accordion-body p-2">
                                                        <div class="template-item mb-2" data-template="header1">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-heading fa-2x"></i>
                                                                <div class="mt-1">Simple Header</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="header2">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-image fa-2x"></i>
                                                                <div class="mt-1">Logo Header</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Content Blocks -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#contentCollapse">
                                                        Content Blocks
                                                    </button>
                                                </h2>
                                                <div id="contentCollapse" class="accordion-collapse collapse" data-bs-parent="#templateComponents">
                                                    <div class="accordion-body p-2">
                                                        <div class="template-item mb-2" data-template="text">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-paragraph fa-2x"></i>
                                                                <div class="mt-1">Text Block</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="image-text">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-image fa-lg"></i> + <i class="fa fa-font fa-lg"></i>
                                                                <div class="mt-1">Image + Text</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="two-column">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-columns fa-2x"></i>
                                                                <div class="mt-1">Two Columns</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Buttons -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#buttonsCollapse">
                                                        Buttons & CTAs
                                                    </button>
                                                </h2>
                                                <div id="buttonsCollapse" class="accordion-collapse collapse" data-bs-parent="#templateComponents">
                                                    <div class="accordion-body p-2">
                                                        <div class="template-item mb-2" data-template="button-primary">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-square fa-2x text-primary"></i>
                                                                <div class="mt-1">Primary Button</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="button-secondary">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-square fa-2x text-secondary"></i>
                                                                <div class="mt-1">Secondary Button</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="multi-button">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-layer-group fa-2x"></i>
                                                                <div class="mt-1">Multiple Buttons</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Images & Media -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#mediaCollapse">
                                                        Images & Media
                                                    </button>
                                                </h2>
                                                <div id="mediaCollapse" class="accordion-collapse collapse" data-bs-parent="#templateComponents">
                                                    <div class="accordion-body p-2">
                                                        <div class="template-item mb-2" data-template="single-image">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-image fa-2x"></i>
                                                                <div class="mt-1">Single Image</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="image-gallery">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-images fa-2x"></i>
                                                                <div class="mt-1">Image Gallery</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="banner">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-panorama fa-2x"></i>
                                                                <div class="mt-1">Banner</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Footers -->
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#footersCollapse">
                                                        Footers
                                                    </button>
                                                </h2>
                                                <div id="footersCollapse" class="accordion-collapse collapse" data-bs-parent="#templateComponents">
                                                    <div class="accordion-body p-2">
                                                        <div class="template-item mb-2" data-template="simple-footer">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-copyright fa-2x"></i>
                                                                <div class="mt-1">Simple Footer</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="social-footer">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-share-alt fa-2x"></i>
                                                                <div class="mt-1">Social Media Footer</div>
                                                            </div>
                                                        </div>
                                                        <div class="template-item mb-2" data-template="complete-footer">
                                                            <div class="text-center border p-2">
                                                                <i class="fa fa-th-large fa-2x"></i>
                                                                <div class="mt-1">Complete Footer</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Control Buttons -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="button" class="btn  me-2" id="previewBtn" style="background-color: #F05522; color: white; text-decoration: none;">
                                            <i class="fa fa-eye"></i> Preview
                                        </button>
                                        <button type="button" class="btn  me-2" id="saveTemplateBtn" style="background-color: #19345E; color: white; text-decoration: none;">
                                            <i class="fa fa-save"></i> Save as Template
                                        </button>
                                        {{-- <a href="{{ route('admin.email-templates') }}"
                                        class="btn"
                                        style="background-color: #F05522; color: white; text-decoration: none;">
                                         Email Templates
                                     </a> --}}





                                    </div>
                                    <div>
                                        {{-- <button type="button" class="btn btn-secondary me-2" id="cancelBtn">Cancel</button> --}}
                                        <button type="submit" class="btn " style="background-color: #19345E; color:white;">
                                            <i class="fa fa-paper-plane"></i>  Send
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Email Preview</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Subject:</strong> <span id="previewSubject"></span>
                </div>
                <div class="border p-3" id="previewContent">
                    <!-- Email content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #19345E; color: white;">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Save Template Modal -->
<div class="modal fade" id="saveTemplateModal" tabindex="-1" aria-labelledby="saveTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="saveTemplateModalLabel">Save as Template</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="saveTemplateForm">
                    <div class="mb-3">
                        <label for="template_save_name" class="form-label">Template Name</label>
                        <input type="text" class="form-control" id="template_save_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="template_category" class="form-label">Category</label>
                        <select class="form-select" id="template_category">
                            <option value="marketing">Marketing</option>
                            <option value="transactional">Transactional</option>
                            <option value="newsletter">Newsletter</option>
                            <option value="announcement">Announcement</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="template_description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="template_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #19345E; color: white;">Cancel</button>
                <button type="button" class="btn" id="confirmSaveTemplate" style="background-color: #F05522; color: white; text-decoration: none;">Save Template</button>
            </div>
        </div>
    </div>
</div>

<!-- Button Link Modal -->
<div class="modal fade" id="buttonLinkModal" tabindex="-1" aria-labelledby="buttonLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="buttonLinkModalLabel">Button Settings</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="buttonLinkForm">
                    <!-- Primary Button Settings -->
                    <div id="primaryButtonSection">
                        <div class="mb-3">
                            <label for="primary_button_text" class="form-label">Button Text</label>
                            <input type="text" class="form-control" id="primary_button_text" value="Click Here" required>
                        </div>
                        <div class="mb-3">
                            <label for="primary_button_url" class="form-label">Button URL</label>
                            <input type="url" class="form-control" id="primary_button_url" value="https://" required>
                        </div>
                    </div>

                    <!-- Secondary Button Settings (hidden by default) -->
                    <div id="secondaryButtonSection" style="display: none;">
                        <hr>
                        <h6>Secondary Button</h6>
                        <div class="mb-3">
                            <label for="secondary_button_text" class="form-label">Button Text</label>
                            <input type="text" class="form-control" id="secondary_button_text" value="Learn More">
                        </div>
                        <div class="mb-3">
                            <label for="secondary_button_url" class="form-label">Button URL</label>
                            <input type="url" class="form-control" id="secondary_button_url" value="https://">
                        </div>
                    </div>

                    <input type="hidden" id="button_template_type">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #19345E; color: white;">Cancel</button>
                <button type="button" class="btn" id="confirmButtonSettings" style="background-color: #F05522; color: white; text-decoration: none;">Insert Button</button>
            </div>
        </div>
    </div>
</div>

<!-- Footer Links Modal -->
<div class="modal fade" id="footerLinksModal" tabindex="-1" aria-labelledby="footerLinksModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="footerLinksModalLabel">Footer Settings</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="footerLinksForm">
                    <!-- Social Media Links -->
                    <div id="socialMediaSection" style="display: none;">
                        <h6>Social Media Links</h6>
                        <div class="mb-3">
                            <label for="facebook_url" class="form-label">Facebook URL</label>
                            <input type="url" class="form-control" id="facebook_url" value="https://facebook.com/">
                            <small class="text-muted">Leave empty to skip</small>
                        </div>
                        <div class="mb-3">
                            <label for="twitter_url" class="form-label">Twitter URL</label>
                            <input type="url" class="form-control" id="twitter_url" value="https://twitter.com/">
                            <small class="text-muted">Leave empty to skip</small>
                        </div>
                        <div class="mb-3">
                            <label for="instagram_url" class="form-label">Instagram URL</label>
                            <input type="url" class="form-control" id="instagram_url" value="https://instagram.com/">
                            <small class="text-muted">Leave empty to skip</small>
                        </div>
                        <div class="mb-3">
                            <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                            <input type="url" class="form-control" id="linkedin_url" value="https://linkedin.com/">
                            <small class="text-muted">Leave empty to skip</small>
                        </div>
                    </div>

                    <!-- Footer Links -->
                    <div id="footerLinksSection" style="display: none;">
                        <h6>Footer Links</h6>
                        <div class="mb-3">
                            <label for="unsubscribe_url" class="form-label">Unsubscribe URL</label>
                            <input type="url" class="form-control" id="unsubscribe_url" value="#">
                        </div>
                        <div class="mb-3">
                            <label for="view_in_browser_url" class="form-label">View in Browser URL</label>
                            <input type="url" class="form-control" id="view_in_browser_url" value="#">
                        </div>
                        <div class="mb-3">
                            <label for="privacy_policy_url" class="form-label">Privacy Policy URL</label>
                            <input type="url" class="form-control" id="privacy_policy_url" value="#">
                        </div>
                    </div>

                    <input type="hidden" id="footer_template_type">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #19345E; color: white;">Cancel</button>
                <button type="button" class="btn" id="confirmFooterSettings" style="background-color: #F05522; color: white; text-decoration: none;">Insert Footer</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>


.header-container {
            position: relative;
            height: 140px;
            overflow: hidden;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 8px 32px rgba(25, 52, 94, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.18);
            margin-bottom: 0;
        }

        #threejs-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            background: linear-gradient(135deg, rgba(25, 52, 94, 0.85) 0%, rgba(25, 52, 94, 0.95) 100%);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            border-bottom: 3px solid #F05522;
        }

        .title-section h4 {
            margin: 0;
            color: white;
            font-size: 26px;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(240, 85, 34, 0.7);
            letter-spacing: 1px;
            position: relative;
            display: inline-block;
        }

        .title-section h4:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #F05522, transparent);
        }

        .tagline {
            color: rgba(255, 255, 255, 0.8);
            margin: 8px 0 0;
            font-size: 14px;
            font-style: italic;
            animation: pulse 2s infinite;
        }


        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0px); }
        }

        @keyframes slideIn {
            from { transform: translateX(20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    .template-item {
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .template-item:hover {
        transform: scale(1.05);
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .template-item img {
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 4px;
    }

    /* Form styling */
    .form-label {
        font-weight: 500;
        color: #19345E;
    }

    .form-control:focus, .form-select:focus {
        border-color: rgba(25, 52, 94, 0.5);
        box-shadow: 0 0 0 0.25rem rgba(25, 52, 94, 0.25);
    }

    .card {
        border: none;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }

    .card-body {
        padding: 2rem;
    }

    /* Button styling */
    .btn {
        border-radius: 5px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Template library styling */
    .accordion-button:not(.collapsed) {
        background-color: rgba(25, 52, 94, 0.1);
        color: #19345E;
    }

    .accordion-button:focus {
        border-color: rgba(25, 52, 94, 0.5);
        box-shadow: 0 0 0 0.25rem rgba(25, 52, 94, 0.25);
    }

    /* Modal styling */
    .modal-header {
        background-color: #19345E;
        color: white;
    }

    .modal-title {
        font-weight: 600;
    }
</style>
@endpush

@push('scripts')

<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="{{ asset('tinymce/tinymce.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>

    const canvas = document.getElementById('threejs-canvas');
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({
        canvas: canvas,
        antialias: true,
        alpha: true
    });

    renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);


    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);


    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);


    const particleCount = 100;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        positions[i] = (Math.random() - 0.5) * 10;
        positions[i + 1] = (Math.random() - 0.5) * 5;
        positions[i + 2] = (Math.random() - 0.5) * 5;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    // Create two particle systems with our primary colors
    const particleMaterial1 = new THREE.PointsMaterial({
        color: 0x19345E, // Primary blue color
        size: 0.05,
        transparent: true,
        opacity: 0.8
    });

    const particleMaterial2 = new THREE.PointsMaterial({
        color: 0xF05522, // Primary orange color
        size: 0.04,
        transparent: true,
        opacity: 0.7
    });

    const particleSystem1 = new THREE.Points(particles, particleMaterial1);
    scene.add(particleSystem1);

    // Create a second particle system with different positions
    const particles2 = new THREE.BufferGeometry();
    const positions2 = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        positions2[i] = (Math.random() - 0.5) * 10;
        positions2[i + 1] = (Math.random() - 0.5) * 5;
        positions2[i + 2] = (Math.random() - 0.5) * 5;
    }

    particles2.setAttribute('position', new THREE.BufferAttribute(positions2, 3));
    const particleSystem2 = new THREE.Points(particles2, particleMaterial2);
    scene.add(particleSystem2);

    // Create a stylized envelope geometry
    const geometry = new THREE.BoxGeometry(1, 0.7, 0.1);
    const material = new THREE.MeshPhongMaterial({
        color: 0x19345E, // Primary blue color
        emissive: 0x072040,
        specular: 0xF05522, // Primary orange color for highlights
        shininess: 50
    });

    const emailIcon = new THREE.Mesh(geometry, material);
    scene.add(emailIcon);

    camera.position.z = 3;


    function animate() {
        requestAnimationFrame(animate);

        // Animate the first particle system (blue)
        particleSystem1.rotation.x += 0.001;
        particleSystem1.rotation.y += 0.002;

        // Animate the second particle system (orange) in a slightly different way
        particleSystem2.rotation.x -= 0.0015;
        particleSystem2.rotation.y -= 0.001;

        // Animate the email icon
        emailIcon.rotation.y += 0.01;
        emailIcon.position.y = Math.sin(Date.now() * 0.001) * 0.2;

        // Add a subtle pulse effect to the icon
        const scale = 1 + Math.sin(Date.now() * 0.002) * 0.05;
        emailIcon.scale.set(scale, scale, scale);

        // Render the scene
        renderer.render(scene, camera);
    }


    window.addEventListener('resize', () => {
        camera.aspect = canvas.clientWidth / canvas.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    });


    animate();
</script>

<script>
document.addEventListener("DOMContentLoaded", function () {
    // Check if there's a selected template to load
    @if(isset($selectedTemplate))
    const selectedTemplate = @json($selectedTemplate);
    @endif

    // Initialize TinyMCE
    tinymce.init({
        selector: '#emailContent',
        height: 600,
        menubar: true,
        branding: false,

        plugins: 'advlist autolink lists link image charmap preview anchor pagebreak ' +
                 'searchreplace visualblocks code fullscreen insertdatetime media table ' +
                 'code help wordcount emoticons hr nonbreaking paste template',

        toolbar: 'undo redo | fontselect fontsizeselect formatselect | ' +
                 'bold italic underline strikethrough forecolor backcolor | ' +
                 'alignleft aligncenter alignright alignjustify | ' +
                 'bullist numlist outdent indent | table image link insertlink insertbreak | ' +
                 'blockquote hr emoticons pagebreak | removeformat code fullscreen',

        images_upload_url: '{{ route("admin.upload-image") }}',
        automatic_uploads: true,
        relative_urls: false,
        remove_script_host: false,
        convert_urls: true,


        mobile: {
            menubar: true
        },
        browser_spellcheck: true,
        contextmenu: false,
        paste_data_images: true,


        urlconverter_callback: function(url, node, on_save, name) {

            if (url.startsWith('images/')) {
                return '{{ asset("") }}' + url;
            }
            return url;
        },


        setup: function(editor) {

        },

        table_advtab: true,
        table_class_list: [
            {title: 'None', value: ''},
            {title: 'Bordered Table', value: 'table table-bordered'},
            {title: 'Striped Table', value: 'table table-striped'},
        ],

        image_advtab: true,
        image_class_list: [
            {title: 'None', value: ''},
            {title: 'Responsive', value: 'img-fluid'},
            {title: 'Rounded', value: 'img-fluid rounded'},
            {title: 'Circle', value: 'img-fluid rounded-circle'},
        ],

        link_class_list: [
            {title: 'None', value: ''},
            {title: 'Button Primary', value: 'btn btn-primary'},
            {title: 'Button Secondary', value: 'btn btn-secondary'},
            {title: 'Button Success', value: 'btn btn-success'},
            {title: 'Button Danger', value: 'btn btn-danger'},
            {title: 'Button Info', value: 'btn btn-info'},
            {title: 'Button Warning', value: 'btn btn-warning'},
            {title: 'Button Light', value: 'btn btn-light'},
            {title: 'Button Dark', value: 'btn btn-dark'},
        ],

        // Main setup function with enhanced cursor handling
        setup: function(editor) {
            // Add save on change
            editor.on('change', function() {
                editor.save();
            });

            // Add custom button for link insertion
            editor.ui.registry.addButton('insertlink', {
                text: 'Insert Link',
                tooltip: 'Insert a link or button',
                onAction: function() {
                    editor.execCommand('mceLink');
                }
            });

            // Add custom button for inserting a paragraph break
            editor.ui.registry.addButton('insertbreak', {
                text: 'Add Break',
                tooltip: 'Insert a paragraph break to add new content',
                onAction: function() {
                    editor.execCommand('mceInsertContent', false, '<p>&nbsp;</p>');
                }
            });

            // Enhanced cursor positioning after content insertion
            editor.on('ExecCommand', function(e) {
                if (e.command === 'mceInsertContent') {
                    // Focus the editor
                    editor.focus();

                    // Ensure cursor is visible by scrolling if needed
                    setTimeout(function() {
                        // Get current selection
                        const selection = editor.selection;
                        const range = selection.getRng();

                        // Make sure we're at the end of the inserted content
                        range.collapse(false);
                        selection.setRng(range);

                        // Scroll to make cursor visible if needed
                        editor.getWin().scrollTo(0, editor.getWin().scrollY + 50);
                    }, 50);
                }
            });

            // Add keyboard shortcut to insert paragraph break
            editor.addShortcut('ctrl+enter', 'Insert paragraph break', function() {
                editor.execCommand('mceInsertContent', false, '<p>&nbsp;</p>');
            });
        },

        content_style: `
            body {
                font-family: Arial, sans-serif;
                font-size: 14px;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .btn {
                display: inline-block;
                font-weight: 400;
                text-align: center;
                white-space: nowrap;
                vertical-align: middle;
                user-select: none;
                border: 1px solid transparent;
                padding: 0.375rem 0.75rem;
                font-size: 1rem;
                line-height: 1.5;
                border-radius: 0.25rem;
                text-decoration: none;
            }
            .btn-primary {
                color: #fff;
                background-color: #007bff;
                border-color: #007bff;
            }
            .btn-secondary {
                color: #fff;
                background-color: #6c757d;
                border-color: #6c757d;
            }
            .btn-success {
                color: #fff;
                background-color: #28a745;
                border-color: #28a745;
            }
            .btn-danger {
                color: #fff;
                background-color: #dc3545;
                border-color: #dc3545;
            }
            .btn-info {
                color: #fff;
                background-color: #17a2b8;
                border-color: #17a2b8;
            }
            .btn-warning {
                color: #212529;
                background-color: #ffc107;
                border-color: #ffc107;
            }
            .btn-light {
                color: #212529;
                background-color: #f8f9fa;
                border-color: #f8f9fa;
            }
            .btn-dark {
                color: #fff;
                background-color: #343a40;
                border-color: #343a40;
            }
            table {
                border-collapse: collapse;
                width: 100%;
            }
            .table-bordered td, .table-bordered th {
                border: 1px solid #dee2e6;
                padding: 8px;
            }
            .table-striped tbody tr:nth-of-type(odd) {
                background-color: rgba(0,0,0,.05);
            }
            img {
                max-width: 100%;
                height: auto;
            }
        `,

        relative_urls: false,
        remove_script_host: false,

        images_upload_handler: function (blobInfo, progress) {
            return new Promise((resolve, reject) => {
                let formData = new FormData();
                formData.append('file', blobInfo.blob(), blobInfo.filename());

                let csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
                if (!csrfTokenElement) {
                    console.error('CSRF token meta tag not found.');
                    reject('CSRF token missing.');
                    return;
                }

                let csrfToken = csrfTokenElement.getAttribute('content');

                fetch('/admin/upload-image', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.location) {
                        resolve(result.location);
                    } else {
                        reject('Image upload failed.');
                    }
                })
                .catch(error => {
                    console.error('Error uploading image:', error);
                    reject('Image upload error.');
                });
            });
        }
    });

    initializeTemplateComponents();

    // Add event listener to preview button
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            showPreview();
        });
    }

    // Load selected template if available
    @if(isset($selectedTemplate))
    setTimeout(function() {
        // Populate the form with template data
        document.getElementById('template_name').value = selectedTemplate.name;
        document.getElementById('subject').value = selectedTemplate.subject || '';

        // Set recipient group if available
        const recipientGroupSelect = document.getElementById('recipient_group');
        if (selectedTemplate.recipient_group && recipientGroupSelect) {
            for (let i = 0; i < recipientGroupSelect.options.length; i++) {
                if (recipientGroupSelect.options[i].value === selectedTemplate.recipient_group) {
                    recipientGroupSelect.selectedIndex = i;
                    break;
                }
            }
        }

        // Set the content in TinyMCE
        tinymce.get('emailContent').setContent(selectedTemplate.content);

        // Update the saved template dropdown if it exists
        const savedTemplateDropdown = document.getElementById('saved_template');
        if (savedTemplateDropdown) {
            for (let i = 0; i < savedTemplateDropdown.options.length; i++) {
                if (savedTemplateDropdown.options[i].value == selectedTemplate.id) {
                    savedTemplateDropdown.selectedIndex = i;
                    break;
                }
            }
        }
    }, 500); // Small delay to ensure TinyMCE is fully initialized
    @endif

    // Add event listener to save template button (if it exists)
    const saveTemplateBtn = document.getElementById('saveTemplateBtn');
    if (saveTemplateBtn) {
        saveTemplateBtn.addEventListener('click', function() {
            $('#saveTemplateModal').modal('show');
        });
    }

    // Add event listener to confirm save template button (if it exists)
    const confirmSaveTemplate = document.getElementById('confirmSaveTemplate');
    if (confirmSaveTemplate) {
        confirmSaveTemplate.addEventListener('click', function() {
            saveAsTemplate();
        });
    }

    // Add event listener to confirm button settings
    const confirmButtonSettings = document.getElementById('confirmButtonSettings');
    if (confirmButtonSettings) {
        confirmButtonSettings.addEventListener('click', function() {
            processButtonTemplate();
        });
    }

    // Add event listener to confirm footer settings
    const confirmFooterSettings = document.getElementById('confirmFooterSettings');
    if (confirmFooterSettings) {
        confirmFooterSettings.addEventListener('click', function() {
            processFooterTemplate();
        });
    }

    // Add event listener to saved template dropdown
    const savedTemplateDropdown = document.getElementById('saved_template');
    if (savedTemplateDropdown) {
        savedTemplateDropdown.addEventListener('change', function() {
            const templateId = this.value;
            if (templateId) {
                loadSavedTemplate(templateId);
            }
        });
    }

    // Add event listener to cancel button (if it exists)
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            if(confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                window.location.reload();
            }
        });
    }

    // Add submit handler for the form
    document.getElementById('emailForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitForm();
    });
});

function initializeTemplateComponents() {
    const templateItems = document.querySelectorAll('.template-item');

    templateItems.forEach(item => {
        item.addEventListener('click', function() {
            const templateType = this.getAttribute('data-template');
            insertTemplate(templateType);
        });
    });
}

function insertTemplate(templateType) {
    let content = '';

    // For button templates, show the modal instead of prompts
    if (templateType === 'button-primary' || templateType === 'button-secondary' || templateType === 'multi-button') {
        // Store the template type in the hidden field
        document.getElementById('button_template_type').value = templateType;

        // Reset modal title
        document.getElementById('buttonLinkModalLabel').textContent = 'Button Settings';

        // Configure the modal based on template type
        if (templateType === 'multi-button') {
            document.getElementById('secondaryButtonSection').style.display = 'block';
            document.getElementById('primary_button_text').value = 'Primary Action';
        } else {
            document.getElementById('secondaryButtonSection').style.display = 'none';

            if (templateType === 'button-primary') {
                document.getElementById('primary_button_text').value = 'Click Here';
            } else if (templateType === 'button-secondary') {
                document.getElementById('primary_button_text').value = 'Learn More';
            }
        }

        // Reset URLs
        document.getElementById('primary_button_url').value = 'https://';
        document.getElementById('secondary_button_url').value = 'https://';

        // Show the modal
        const buttonModal = new bootstrap.Modal(document.getElementById('buttonLinkModal'));
        buttonModal.show();

        // Return early - the actual insertion will happen when the modal is confirmed
        return;
    }

    switch(templateType) {
        case 'header1':
            content = `<div style="padding: 20px; background-color: #f8f9fa; text-align: center; margin-bottom: 20px;">
                <h1 style="color: #333; margin: 0;">Your Email Header</h1>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'header2':
            content = `<div style="padding: 20px; background-color: #f8f9fa; text-align: center; margin-bottom: 20px;">
                <img src="{{ asset('images/logo.png') }}" alt="Company Logo" style="max-width: 200px;">
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'text':
            content = `<div style="margin-bottom: 20px;">
                <p>This is a paragraph of text. You can edit this to add your own content. Double-click to start editing.</p>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'image-text':
            content = `<div style="display: flex; margin-bottom: 20px;">
                <div style="flex: 1; padding-right: 10px;">
                    <div style="text-align: center;">
                        <i class="fa fa-image fa-5x" style="color: #ddd;"></i>
                    </div>
                </div>
                <div style="flex: 1; padding-left: 10px;">
                    <h3>Image with Text</h3>
                    <p>This is a section with an image and text side by side. You can edit this content.</p>
                </div>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'two-column':
            content = `<div style="display: flex; margin-bottom: 20px;">
                <div style="flex: 1; padding-right: 10px;">
                    <h3>Left Column</h3>
                    <p>Content for the left column goes here.</p>
                </div>
                <div style="flex: 1; padding-left: 10px;">
                    <h3>Right Column</h3>
                    <p>Content for the right column goes here.</p>
                </div>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'button-primary':
            // This case is now handled by the modal and processButtonTemplate function
            break;
        case 'button-secondary':
            // This case is now handled by the modal and processButtonTemplate function
            break;
        case 'multi-button':
            // This case is now handled by the modal and processButtonTemplate function
            break;
        case 'single-image':
            content = `<div style="text-align: center; margin: 20px 0;">
                <div style="padding: 40px; background-color: #f8f9fa; border: 1px dashed #ddd; border-radius: 4px;">
                    <i class="fa fa-image fa-5x" style="color: #ddd;"></i>
                    <p style="margin-top: 10px; color: #6c757d;">Click to replace with your image</p>
                </div>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'image-gallery':
            content = `<div style="display: flex; justify-content: space-between; margin: 20px 0;">
                <div style="flex: 1; padding: 0 5px; text-align: center;">
                    <div style="padding: 20px; background-color: #f8f9fa; border: 1px dashed #ddd;">
                        <i class="fa fa-image fa-3x" style="color: #ddd;"></i>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 5px; text-align: center;">
                    <div style="padding: 20px; background-color: #f8f9fa; border: 1px dashed #ddd;">
                        <i class="fa fa-image fa-3x" style="color: #ddd;"></i>
                    </div>
                </div>
                <div style="flex: 1; padding: 0 5px; text-align: center;">
                    <div style="padding: 20px; background-color: #f8f9fa; border: 1px dashed #ddd;">
                        <i class="fa fa-image fa-3x" style="color: #ddd;"></i>
                    </div>
                </div>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'banner':
            // Store the template type in the hidden field
            document.getElementById('button_template_type').value = 'banner';

            // Configure the modal for banner
            document.getElementById('secondaryButtonSection').style.display = 'none';
            document.getElementById('primary_button_text').value = 'Learn More';
            document.getElementById('primary_button_url').value = 'https://';
            document.getElementById('buttonLinkModalLabel').textContent = 'Banner Button Settings';

            // Show the modal
            const bannerModal = new bootstrap.Modal(document.getElementById('buttonLinkModal'));
            bannerModal.show();

            // Return early - the actual insertion will happen when the modal is confirmed
            return;
            break;
        case 'simple-footer':
            content = `<div style="padding: 20px; background-color: #f8f9fa; text-align: center; margin-top: 20px; font-size: 12px; color: #6c757d;">
                <p>© 2025 Your Company. All rights reserved.</p>
                <p>You are receiving this email because you signed up on our website.</p>
                <p><a href="#">Unsubscribe</a></p>
            </div>
            <p>&nbsp;</p>`;
            break;
        case 'social-footer':
            // Store the template type in the hidden field
            document.getElementById('footer_template_type').value = 'social-footer';

            // Configure the modal for social footer
            document.getElementById('footerLinksModalLabel').textContent = 'Social Media Footer Settings';
            document.getElementById('socialMediaSection').style.display = 'block';
            document.getElementById('footerLinksSection').style.display = 'none';

            // Reset form fields to default values
            document.getElementById('facebook_url').value = 'https://facebook.com/';
            document.getElementById('twitter_url').value = 'https://twitter.com/';
            document.getElementById('instagram_url').value = 'https://instagram.com/';
            document.getElementById('linkedin_url').value = 'https://linkedin.com/';
            document.getElementById('unsubscribe_url').value = '#';

            // Show the modal
            const socialFooterModal = new bootstrap.Modal(document.getElementById('footerLinksModal'));
            socialFooterModal.show();

            // Return early - the actual insertion will happen when the modal is confirmed
            return;
            break;
        case 'complete-footer':
            // Store the template type in the hidden field
            document.getElementById('footer_template_type').value = 'complete-footer';

            // Configure the modal for complete footer
            document.getElementById('footerLinksModalLabel').textContent = 'Complete Footer Settings';
            document.getElementById('socialMediaSection').style.display = 'block';
            document.getElementById('footerLinksSection').style.display = 'block';

            // Reset form fields to default values
            document.getElementById('facebook_url').value = 'https://facebook.com/';
            document.getElementById('twitter_url').value = 'https://twitter.com/';
            document.getElementById('instagram_url').value = 'https://instagram.com/';
            document.getElementById('linkedin_url').value = 'https://linkedin.com/';
            document.getElementById('unsubscribe_url').value = '#';
            document.getElementById('view_in_browser_url').value = '#';
            document.getElementById('privacy_policy_url').value = '#';

            // Show the modal
            const completeFooterModal = new bootstrap.Modal(document.getElementById('footerLinksModal'));
            completeFooterModal.show();

            // Return early - the actual insertion will happen when the modal is confirmed
            return;
            break;
    }

    // Paragraph breaks are now included directly in the button templates

    // Insert the template content into TinyMCE
    tinymce.activeEditor.execCommand('mceInsertContent', false, content);

    // Ensure the editor is focused
    tinymce.activeEditor.focus();

    // Place cursor at the end of the inserted content
    const editor = tinymce.activeEditor;

    // Force a small delay to ensure content is fully inserted before setting cursor
    setTimeout(function() {
        // Move to the end of content
        editor.selection.select(editor.getBody(), true);
        editor.selection.collapse(false);

        // Scroll to the cursor position
        editor.getWin().scrollTo(0, editor.getWin().scrollY + 100);
    }, 100);
}

function showPreview() {
    const subject = document.getElementById('subject').value;
    const content = tinymce.get('emailContent').getContent();

    document.getElementById('previewSubject').textContent = subject || '(No subject)';
    document.getElementById('previewContent').innerHTML = content || '(No content)';

    // Show the preview modal
    const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
    previewModal.show();
}

function saveAsTemplate() {
    const templateName = document.getElementById('template_save_name').value;
    const category = document.getElementById('template_category').value;
    const description = document.getElementById('template_description').value;
    const content = tinymce.get('emailContent').getContent();
    const subject = document.getElementById('subject').value;
    const recipientGroup = document.getElementById('recipient_group').value;

    if (!templateName) {

        createSlider("Confirm Action", "Please enter a template name",{ title: "Warning" });
        return;
    }

    // Get the CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show loading state if the button exists
    const saveBtn = document.getElementById('confirmSaveTemplate');
    let originalText = '';

    if (saveBtn) {
        originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving...';
        saveBtn.disabled = true;
    }

    // Send the template data to the server
    fetch('{{ route("admin.save-template") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            name: templateName,
            category: category,
            description: description,
            content: content,
            subject: subject,
            recipient_group: recipientGroup
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {

            bootstrap.Modal.getInstance(document.getElementById('saveTemplateModal')).hide();


            createSlider("Success", "Template saved successfully",{ title: "Success" });


            document.getElementById('saveTemplateForm').reset();
        } else {
            createSlider("Error", "Failed to save template",{ title: "Error" });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        createSlider("Error", "An error occurred while saving the template",{ title: "Error" });
    })
    .finally(() => {
        // Restore button state if it exists
        if (saveBtn) {
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    });
}

function submitForm() {
    // Get the HTML content from TinyMCE
    const htmlContent = tinymce.get('emailContent').getContent();

    // Set the hidden input value
    document.getElementById('html_content').value = htmlContent;

    // Validate required fields
    const templateName = document.getElementById('template_name').value;
    const subject = document.getElementById('subject').value;
    const recipientGroup = document.getElementById('recipient_group').value;

    if (!templateName) {
        createSlider("Confirm Action", "Please enter a template name",{ title: "Warning" });
        return;
    }

    if (!subject) {
        createSlider("Confirm Action", "Please enter an email subject",{ title: "Warning" });
        return;
    }

    if (!recipientGroup) {
        createSlider("Confirm Action", "Please select a recipient group",{ title: "Warning" });
        return;
    }

    if (!htmlContent || htmlContent.trim() === '') {
        createSlider("Confirm Action", "Please add some content to your email",{ title: "Warning" });
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Sending...';
    submitBtn.disabled = true;

    // Submit the form
    document.getElementById('emailForm').submit();
}

// Helper function to create a notification
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Add to document
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Function to load a saved template
function processButtonTemplate() {
    // Get values from the modal
    const templateType = document.getElementById('button_template_type').value;
    const primaryButtonText = document.getElementById('primary_button_text').value || 'Click Here';
    const primaryButtonUrl = document.getElementById('primary_button_url').value || 'https://';

    let content = '';

    // Create the appropriate button based on template type
    if (templateType === 'button-primary') {
        content = `<div style="text-align: center; margin: 20px 0;">
            <a href="${primaryButtonUrl}" class="btn btn-primary" style="padding: 10px 20px; background-color: #F05522; color: white; text-decoration: none; border-radius: 4px; display: inline-block;">${primaryButtonText}</a>
        </div>
        <p>&nbsp;</p>`;
    } else if (templateType === 'button-secondary') {
        content = `<div style="text-align: center; margin: 20px 0;">
            <a href="${primaryButtonUrl}" class="btn btn-secondary" style="padding: 10px 20px; background-color: #19345E; color: white; text-decoration: none; border-radius: 4px; display: inline-block;">${primaryButtonText}</a>
        </div>
        <p>&nbsp;</p>`;
    } else if (templateType === 'multi-button') {
        const secondaryButtonText = document.getElementById('secondary_button_text').value || 'Learn More';
        const secondaryButtonUrl = document.getElementById('secondary_button_url').value || 'https://';

        content = `<div style="text-align: center; margin: 20px 0;">
            <a href="${primaryButtonUrl}" class="btn btn-primary" style="padding: 10px 20px; background-color: #F05522; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin-right: 10px;">${primaryButtonText}</a>
            <a href="${secondaryButtonUrl}" class="btn btn-secondary" style="padding: 10px 20px; background-color: #19345E; color: white; text-decoration: none; border-radius: 4px; display: inline-block;">${secondaryButtonText}</a>
        </div>
        <p>&nbsp;</p>`;
    } else if (templateType === 'banner') {
        content = `<div style="margin: 20px 0; position: relative;">
            <div style="padding: 60px 20px; background-color: #f8f9fa; border: 1px dashed #ddd; text-align: center;">
                <i class="fa fa-panorama fa-4x" style="color: #ddd;"></i>
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; width: 80%;">
                    <h2 style="margin-top: 20px;">Banner with Text Overlay</h2>
                    <a href="${primaryButtonUrl}" class="btn btn-primary" style="padding: 10px 20px; background-color: #F05522; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin-top: 10px;">${primaryButtonText}</a>
                </div>
            </div>
        </div>
        <p>&nbsp;</p>`;
    }

    // Insert the content into TinyMCE
    tinymce.activeEditor.execCommand('mceInsertContent', false, content);

    // Close the modal
    bootstrap.Modal.getInstance(document.getElementById('buttonLinkModal')).hide();

    // Ensure the editor is focused
    tinymce.activeEditor.focus();

    // Place cursor at the end of the inserted content
    const editor = tinymce.activeEditor;

    // Force a small delay to ensure content is fully inserted before setting cursor
    setTimeout(function() {
        // Move to the end of content
        editor.selection.select(editor.getBody(), true);
        editor.selection.collapse(false);

        // Scroll to the cursor position
        editor.getWin().scrollTo(0, editor.getWin().scrollY + 100);
    }, 100);
}

function processFooterTemplate() {
    // Get values from the modal
    const templateType = document.getElementById('footer_template_type').value;
    let content = '';

    if (templateType === 'social-footer') {
        // Get social media URLs
        const facebookUrl = document.getElementById('facebook_url').value;
        const twitterUrl = document.getElementById('twitter_url').value;
        const instagramUrl = document.getElementById('instagram_url').value;
        const linkedinUrl = document.getElementById('linkedin_url').value;
        const unsubscribeUrl = document.getElementById('unsubscribe_url').value || '#';

        // Build social icons HTML
        let socialIcons = '';

        if (facebookUrl && facebookUrl.trim() !== '') {
            socialIcons += `<a href="${facebookUrl}" style="display: inline-block; margin: 0 10px;"><i class="fab fa-facebook fa-lg" style="color: #3b5998;"></i></a>`;
        }

        if (twitterUrl && twitterUrl.trim() !== '') {
            socialIcons += `<a href="${twitterUrl}" style="display: inline-block; margin: 0 10px;"><i class="fab fa-twitter fa-lg" style="color: #1da1f2;"></i></a>`;
        }

        if (instagramUrl && instagramUrl.trim() !== '') {
            socialIcons += `<a href="${instagramUrl}" style="display: inline-block; margin: 0 10px;"><i class="fab fa-instagram fa-lg" style="color: #e1306c;"></i></a>`;
        }

        if (linkedinUrl && linkedinUrl.trim() !== '') {
            socialIcons += `<a href="${linkedinUrl}" style="display: inline-block; margin: 0 10px;"><i class="fab fa-linkedin fa-lg" style="color: #0077b5;"></i></a>`;
        }

        // If no social icons were added, use defaults
        if (socialIcons === '') {
            socialIcons = `
                <a href="#" style="display: inline-block; margin: 0 10px;"><i class="fab fa-facebook fa-lg" style="color: #3b5998;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 10px;"><i class="fab fa-twitter fa-lg" style="color: #1da1f2;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 10px;"><i class="fab fa-instagram fa-lg" style="color: #e1306c;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 10px;"><i class="fab fa-linkedin fa-lg" style="color: #0077b5;"></i></a>
            `;
        }

        content = `<div style="padding: 20px; background-color: #f8f9fa; text-align: center; margin-top: 20px; font-size: 12px; color: #6c757d;">
            <div style="margin-bottom: 10px;">
                ${socialIcons}
            </div>
            <p>© 2025 Your Company. All rights reserved.</p>
            <p>You are receiving this email because you signed up on our website.</p>
            <p><a href="${unsubscribeUrl}">Unsubscribe</a></p>
        </div>
        <p>&nbsp;</p>`;
    } else if (templateType === 'complete-footer') {
        // Get footer links
        const unsubscribeLink = document.getElementById('unsubscribe_url').value || '#';
        const viewInBrowserLink = document.getElementById('view_in_browser_url').value || '#';
        const privacyPolicyLink = document.getElementById('privacy_policy_url').value || '#';

        // Get social media URLs
        const footerFacebookUrl = document.getElementById('facebook_url').value;
        const footerTwitterUrl = document.getElementById('twitter_url').value;
        const footerInstagramUrl = document.getElementById('instagram_url').value;
        const footerLinkedinUrl = document.getElementById('linkedin_url').value;

        // Build social icons HTML
        let footerSocialIcons = '';

        if (footerFacebookUrl && footerFacebookUrl.trim() !== '') {
            footerSocialIcons += `<a href="${footerFacebookUrl}" style="display: inline-block; margin: 0 5px;"><i class="fab fa-facebook" style="color: #3b5998;"></i></a>`;
        }

        if (footerTwitterUrl && footerTwitterUrl.trim() !== '') {
            footerSocialIcons += `<a href="${footerTwitterUrl}" style="display: inline-block; margin: 0 5px;"><i class="fab fa-twitter" style="color: #1da1f2;"></i></a>`;
        }

        if (footerInstagramUrl && footerInstagramUrl.trim() !== '') {
            footerSocialIcons += `<a href="${footerInstagramUrl}" style="display: inline-block; margin: 0 5px;"><i class="fab fa-instagram" style="color: #e1306c;"></i></a>`;
        }

        if (footerLinkedinUrl && footerLinkedinUrl.trim() !== '') {
            footerSocialIcons += `<a href="${footerLinkedinUrl}" style="display: inline-block; margin: 0 5px;"><i class="fab fa-linkedin" style="color: #0077b5;"></i></a>`;
        }

        // If no social icons were added, use defaults
        if (footerSocialIcons === '') {
            footerSocialIcons = `
                <a href="#" style="display: inline-block; margin: 0 5px;"><i class="fab fa-facebook" style="color: #3b5998;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 5px;"><i class="fab fa-twitter" style="color: #1da1f2;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 5px;"><i class="fab fa-instagram" style="color: #e1306c;"></i></a>
                <a href="#" style="display: inline-block; margin: 0 5px;"><i class="fab fa-linkedin" style="color: #0077b5;"></i></a>
            `;
        }

        content = `<div style="padding: 20px; background-color: #f8f9fa; margin-top: 20px; font-size: 12px; color: #6c757d;">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 33%; vertical-align: top; padding: 10px;">
                        <h4>About Us</h4>
                        <p>Your Company is dedicated to providing the best service to our customers.</p>
                    </td>
                    <td style="width: 33%; vertical-align: top; padding: 10px;">
                        <h4>Links</h4>
                        <p>
                            <a href="${viewInBrowserLink}">View in Browser</a><br>
                            <a href="${privacyPolicyLink}">Privacy Policy</a>
                        </p>
                    </td>
                    <td style="width: 33%; vertical-align: top; padding: 10px;">
                        <h4>Contact Us</h4>
                        <p>123 Main Street<br>City, State 12345<br><EMAIL><br>(123) 456-7890</p>
                    </td>
                    <td style="width: 33%; vertical-align: top; padding: 10px;">
                        <h4>Follow Us</h4>
                        <div>
                            ${footerSocialIcons}
                        </div>
                    </td>
                </tr>
            </table>
            <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd;">
                <p>© 2025 Your Company. All rights reserved.</p>
                <p>You are receiving this email because you signed up on our website.</p>
                <p><a href="${unsubscribeLink}">Unsubscribe</a> | <a href="${viewInBrowserLink}">View in Browser</a> | <a href="${privacyPolicyLink}">Privacy Policy</a></p>
            </div>
        </div>
        <p>&nbsp;</p>`;
    }

    // Insert the content into TinyMCE
    tinymce.activeEditor.execCommand('mceInsertContent', false, content);

    // Close the modal
    bootstrap.Modal.getInstance(document.getElementById('footerLinksModal')).hide();

    // Ensure the editor is focused
    tinymce.activeEditor.focus();

    // Place cursor at the end of the inserted content
    const editor = tinymce.activeEditor;

    // Force a small delay to ensure content is fully inserted before setting cursor
    setTimeout(function() {
        // Move to the end of content
        editor.selection.select(editor.getBody(), true);
        editor.selection.collapse(false);

        // Scroll to the cursor position
        editor.getWin().scrollTo(0, editor.getWin().scrollY + 100);
    }, 100);
}

function loadSavedTemplate(templateId) {
    // Get the CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show loading indicator
    const savedTemplateDropdown = document.getElementById('saved_template');
    const originalText = savedTemplateDropdown.options[savedTemplateDropdown.selectedIndex].text;
    savedTemplateDropdown.options[savedTemplateDropdown.selectedIndex].text = 'Loading...';

    // Fetch the template data
    fetch(`{{ url('admin/get-template') }}/${templateId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(template => {
        // Populate the form with template data
        document.getElementById('template_name').value = template.name;
        document.getElementById('subject').value = template.subject || '';

        // Set recipient group if available
        const recipientGroupSelect = document.getElementById('recipient_group');
        if (template.recipient_group && recipientGroupSelect) {
            for (let i = 0; i < recipientGroupSelect.options.length; i++) {
                if (recipientGroupSelect.options[i].value === template.recipient_group) {
                    recipientGroupSelect.selectedIndex = i;
                    break;
                }
            }
        }


        tinymce.get('emailContent').setContent(template.content);


        savedTemplateDropdown.options[savedTemplateDropdown.selectedIndex].text = originalText;


        createSlider("success", "Template loaded successfully",{ title: "Success" });
    })
    .catch(error => {
        console.error('Error loading template:', error);
        createSlider("Error", "Error loading template. Please try again.",{ title: "Error" });


        savedTemplateDropdown.options[savedTemplateDropdown.selectedIndex].text = originalText;
    });
}


document.addEventListener('keydown', function(event) {

    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        const saveTemplateBtn = document.getElementById('saveTemplateBtn');
        if (saveTemplateBtn) {
            saveTemplateBtn.click();
        }
    }


    if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
        event.preventDefault();
        const previewBtn = document.getElementById('previewBtn');
        if (previewBtn) {
            previewBtn.click();
        }
    }
});
</script>
@endpush


