/* Form Styling */
.form-floating > label {
    padding-left: 1rem;
}

.form-floating > .form-control {
    height: 3.5rem;
    padding: 1rem 0.75rem;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25);
    border-color: #6a11cb;
}

.form-check-input:checked {
    background-color: #6a11cb;
    border-color: #6a11cb;
}

.form-check-input:focus {
    border-color: #6a11cb;
    box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25);
}

/* Button Styling */
.btn-primary {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.btn-primary:focus {
    box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
    .col-md-5 {
        padding: 3rem 2rem;
    }

    .impact-stats {
        margin-bottom: 0;
    }
}

/* Hover Effects */
a:hover {
    color: #2575fc !important;
    transition: color 0.3s ease;
}

/* Error States */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Success States */
.is-valid {
    border-color: #198754 !important;
}

/* Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading:after {
    content: "";
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid #fff;
    border-radius: 50%;
    border-top-color: transparent;
    left: calc(50% - 0.5rem);
    top: calc(50% - 0.5rem);
    animation: button-loading-spinner 1s linear infinite;
}

@keyframes button-loading-spinner {
    from {
        transform: rotate(0turn);
    }
    to {
        transform: rotate(1turn);
    }
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    top: 70%;
    right: 1.8rem;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 22px;
    color: #19345e !important;
    z-index: 10;
    padding: 5px;
    line-height: 1;
    border-radius: 50%;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.password-toggle:hover {
    background-color: rgba(106, 17, 203, 0.1);
    color: #19345e !important;
}

.password-toggle:focus {
    outline: none;
    box-shadow: 0 0 6px rgba(106, 17, 203, 0.3);
}

.password-toggle svg {
    width: 24px;
    height: 24px;
    fill: #19345e;
    transition: fill 0.2s ease;
}

.password-toggle:hover svg {
    fill: #19345e;
}
