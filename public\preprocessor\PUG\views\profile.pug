- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet';
- let heroName = 'Profile';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName} ${heroName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/radioOptionPackage.pug
include ../layouts/secHead.pug
include ../partials/footer.pug

html(lang="en")
    +head(pageTitle)

    body
        +header()

        section.profile-hero
            .container
                .row.align-items-center.gx-xl-5
                    .col-md-4.mb-5.mb-md-0
                        .img-player
                            img.w-100(src="images/profile-pic.jpg", alt="")
                    .col-md-8
                        .name
                            h1.text-uppercase Support#[br] #[strong.text-orange <PERSON>’s]#[br] Fundraiser

        section.achieve-goal
            .container-fluid.gx-0
                .bg-blue
                    .container
                        .meta-timeline.d-flex.justify-content-between
                            .meta.text-center
                                h2 Raised:#[br] #[strong $2,000]
                            .meta.text-center
                                h2 Goal:#[br] #[strong $5,000]
                        .time-line.mt-3
                            .bar
                                span.fill.align-items-center.d-flex.justify-content-end(style="width:40%")

        section.sec.donate-wrap.select-level
            .container
                +secHead('Choose your impact level', 'Every donation makes a difference in the fight against cancer!', 'mb-5')

                .select-package.row
                    .col-6.col-md-4.col-lg-3.col-xl.mb-4
                        +radioOptionPackage('$25 <span>Supporter</span> <span class="tag text-nowrap">Basic Donation</span>', 'Supporter')
                    .col-6.col-md-4.col-lg-3.col-xl.mb-4
                        +radioOptionPackage('$200 <span>Champion</span> <span class="tag text-nowrap">Recommended</span>', 'Champion')
                    .col-6.col-md-4.col-lg-3.col-xl.mb-4
                        +radioOptionPackage('$250 <span>Advocate</span> <span class="tag text-nowrap">Supporter</span>', 'Advocate')
                    .col-6.col-md-4.col-lg-3.col-xl.mb-4
                        +radioOptionPackage('$500 <span>Hero</span> <span class="tag text-nowrap">Generous</span>', 'Hero')
                    .col-6.col-md-4.col-lg-3.col-xl.mb-4
                        +radioOptionPackage('$1,000 <span>Guardian</span> <span class="tag text-nowrap">Major Contribution</span>', 'Guardian')

                .text-center.mb-5
                    h2.mb-3 Or Enter Your Own Amount
                    .input-price.mx-auto.mb-4
                        label.visually-hidden(for="amount") Amount
                        .input-group
                            .input-group-text $
                            input.form-control(type="number", id="amount", inputmode="numeric")

                    .text-center.mb-5
                        button.cta.orange.border-0 Continue

        section.sec.your-donation
            .container
                +secHead('Your Donation Makes a Difference', 'Here’s how your contribution helps in the fight against cancer!', 'mb-5')
                .row
                    mixin differenceBox(h2, p)
                        .col-md-4.mt-5
                            .box.text-center
                                .icon.mb-4
                                    img(src="images/"+h2.toLowerCase()+"-icon.svg", alt="", width="116", height="116")
                                .text
                                    h2=h2
                                    p=p
                    +differenceBox('Research', 'Your donation helps fund vital cancer research to develop new treatments and therapies.')
                    +differenceBox('Support', 'Provides essential support services for patients and families throughout their cancer journey.')
                    +differenceBox('Prevention', 'Supports education and awareness programs to help prevent cancer through early detection.')

        section.sec.recent-activities
            .container
                +secHead('Join our Community of Donors', 'These generous people have already made a difference', 'mb-5 mb-md-0')

                .search-athlete.py-5.mx-auto(style="max-width:330px")
                    h2.text-center Search by Name
                    form.search-wrap.mt-2.mb-0(action="")
                        .search
                            input(type="text")
                            .icon.d-flex.align-items-center.justify-content-center #[i.bi.bi-search]


                .row.activity-head.text-uppercase.d-none.d-md-flex.mt-5
                    .col-md-3.donor.ps-5 Donor
                    .col-md-3.athlete Athlete
                    .col-md-1.amount Amount
                    .col-md.comment.pe-md-5 Comment

                mixin activityRow(donor, athlete, amount, comment)
                    .row.activity-row.d-flex.align-items-center.mt-2
                        .col-md-3.label.py-2.py-md-3.donor.ps-md-5(title='Donor')=donor
                        .col-md-3.label.py-2.py-md-3.athlete(title='Athlete')=athlete
                        .col-md-1.label.py-2.py-md-3.amount(title='Amount') $#{amount}
                        .col-md.label.py-2.py-md-3.comment.pe-md-5(title='Comment')=comment
                +activityRow('Joan Smith', 'Michael Bryant', '50', 'Way to go Michael!')
                +activityRow('Ted Jones', 'Robert James', '25', 'Let’s go Bobby!')
                +activityRow('Lorna Haller', 'Ali McCarthy', '50', 'We’re so proud of you. We can’t wait to see what you do next. Keep up the good work. Good luck!')
                +activityRow('David Munn', 'Mike Gluck', '50', 'We’re so proud of you.')
                +activityRow('Oliver Bates', 'Megan Lailer', '500', 'She shoots, she scores!')
                +activityRow('Ray Winslow', 'Andy Sadler', '500', 'Way to go Andy. Very proud of all you are accomplishing, keep going!')

                .cta-row.mt-5.text-center
                    a.cta.orange(href="#") #[img(src="images/arrow-cta-left.svg", alt="") ]
                    a.cta.orange(href="#") #[img(src="images/arrow-cta-right.svg", alt="") ]

        +footer()
