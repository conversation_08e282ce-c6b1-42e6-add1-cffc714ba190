@extends('layouts.app')

@section('title', '2025 ASFL Gauntlet | Register')
@section('meta_description', 'Sign up for the ASFL Gauntlet—join a 24-hour basketball event to support cancer patients. Open to all ages and skill levels. Register today!')
@section('content')

<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Register</h1>
        </div>
    </div>
</section>
<section class="sec content-row">
    <div class="container">
        <div class="copy text-center">
            <p>
                Join the A Shot for Life Gauntlet: Fundraise, Inspire, and<br class="d-none d-md-block" />
                Make a Difference in the Fight Against Cancer!
            </p>
        </div>
    </div>
</section>
<section class="form">
    <div class="container">
        <form class="row register-form gx-lg-5" id="registerForm" action="" method="POST" enctype="multipart/form-data">
            @csrf
            <div class="col-md-6 mb-4">
                <label class="form-label" for="name">Name</label>
                <input class="form-control" name="name" required id="name" />
            </div>
            <div class="col-md-6 mb-4">
                <label class="form-label" for="email">Email</label>
                <input class="form-control" name="email" type="email" id="email" />
            </div>
            <div class="col-md-4 mb-4">
                <label class="form-label" for="address">Address</label>
                <input class="form-control" name="address" id="address" />
            </div>
            <div class="col-md-4 mb-4">
                <label class="form-label" for="city">City</label>
                <input class="form-control" name="city" id="city" />
            </div>
            <div class="col-md-4 mb-4">
                <label class="form-label" for="state">State</label>
                <input class="form-control" name="state" id="state" />
            </div>


            <div class="col-md-12 mb-4 password-container">
                <label class="form-label" for="password">Password</label>
                <input type="password" class="form-control" id="password" name="password"
                    required placeholder="Password">
                <button type="button" class="password-toggle"
                    aria-label="Toggle password visibility">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                        fill="currentColor" class="bi bi-eye-slash" viewBox="0 0 16 16">
                        <path
                            d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7 7 0 0 0-2.79.588l.77.771A6 6 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755q-.247.248-.517.486z" />
                        <path
                            d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829" />
                        <path
                            d="M3.35 5.47q-.27.24-.518.487A13 13 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7 7 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12z" />
                    </svg>
                </button>
            </div>
            <div class="col-md-12 mb-4">
                <label class="form-label" for="fileUpload">Profile Photo</label>
                <div class="file-upload-container">
                    <div id="dropZone" class="drop-zone">
                        <input type="file" id="fileUpload" name="file" class="file-input" accept="image/*" />
                        <div class="drop-zone-content" id="dropZoneTrigger">
                            <i class="upload-icon" style="font-size: 24px; margin-bottom: 10px;">📁</i>
                            <p class="drop-zone-text">Drag & Drop File(s) Here to Upload</p>
                            <p class="drop-zone-text" style="font-size: 12px; margin-top: 5px;">(or click to browse)</p>
                        </div>
                        <div id="imagePreviewContainer" class="image-preview-container">
                            <img id="imagePreview" src="" alt="Preview">
                            <button id="removeImage" type="button" class="remove-image-btn">
                                <i class="remove-icon">✖</i>
                            </button>
                        </div>
                    </div>

                    <!-- Buttons Container -->
                    <div class="upload-buttons-container">
                        <button type="button" id="manualUploadBtn" class="upload-btn">
                            <i class="upload-icon">📤</i> Upload New
                        </button>
                        <button type="button" id="chooseExistingBtn" class="upload-btn">
                            <i class="upload-icon">📁</i> Choose Existing
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-12 mb-4">
                <label class="form-label" for="fund_raise_message">Message to Donors<i class="fw-normal">(Optional)</i> </label>
                <textarea name="fund_raise_message" id="message" name="fund_raise_message" class="form-control"></textarea>
            </div>
            <div class="col-md-6 mb-4">
                <label class="form-label" for="amount">Goal Amount <i class="fw-normal">(Minimum $1000)</i></label>
                <input class="form-control" min="1000" type="text" id="amount" name="fund_raise_goal" />
            </div>
            <div class="col-md-12 mb-4">
                <div class="form-check">
                    <input class="form-check-input" value="on" type="checkbox" value="" id="commitFundraising" name= "commit_fundraising" />
                    <label class="form-check-label" for="commit_fundraising">By submitting this form, I agree and understand that I am committing to raise a minimum of $1,000 for the ASFL Gauntlet Fundraiser.</label>
                </div>
            </div>
            <div class="col-md-12 mb-4">
                <div class="form-check">
                    <input class="form-check-input" value="on" type="checkbox" value="" id="use_photo" name="use_photo_videos" />
                    <label class="form-check-label" for="use_photo_videos">I grant ASFL permission to use photos and/or videos of me from the event for their marketing purposes; the release form can be <a class="fw-bold" href="#">downloaded here</a>.</label>
                </div>
            </div>
            <div class="col-md-12 mb-4">
                <div class="form-check">
                    <input class="form-check-input" value="on" type="checkbox" value="" id="another_terms"  name="liability_waiver" />
                    <label class="form-check-label" for="liability_waiver">I affirm that I have read and understand the liability waiver, which can be <a class="fw-bold" href="#">downloaded here</a>.</label>
                </div>
            </div>
            <div class="col-md-12 my-5 text-center">
                <button class="cta orange border-0 hoverbutton" id="register" type="submit"><span>Register Now</span></button>
            </div>
        </form>
    </div>
</section>

    @endsection

    @push('styles')
      @vite('resources/css/pages/register.css')
    @endpush

    @push('scripts')

        <script defer src="{{ asset('tinymce/tinymce.min.js') }}"></script>
       @vite('resources/js/pages/register.js')
    @endpush


