@extends('layouts.app')

@section('title', '2025 ASFL Gauntlet | Donate')

@section('meta_description', 'Help fuel the fight against cancer—donate to the ASFL Gauntlet and make a lasting impact through this powerful basketball fundraiser.')

<meta name="stripe-key" content="{{ config('services.stripe.key') }}">

@section('content')


<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Donate</h1>
        </div>
    </div>
</section>
<section class="search-athlete sec leaderboard pt-3">
    <div class="container">
        <h2 class="text-center">Find an Athlete</h2>
        <div class="search mt-2 mb-0">
            <input type="text" id="search" placeholder="Search Athlete By Name or Town" />
            <div class="icon d-flex align-items-center justify-content-center"><i class="bi bi-search"></i></div>
        </div>
        <div class="text-center mt-4">
            <button id="searchButton" class="cta orange hoverbutton" style="border:none;">
                Search
            </button>
        </div>


    <div id="searchResults" class="row">
    </div>
    </div>
</section>
<section class="donate-wrap">
    <div class="container">
        <div class="donate-form">
            <div class="select-frequency text-center mb-5">
                <h2 class="mb-0">Choose Your Donation Frequency</h2>
                <div class="select-row select-cta row justify-content-center mt-4">
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="oneTime" id="onetime" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="onetime">One Time</label>
                        </div>
                    </div>


                    {{-- <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="daily" id="daily" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="daily">T Daily</label>
                        </div>
                    </div> --}}
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="monthly" id="monthly" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="monthly">Monthly</label>
                        </div>
                    </div>



                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="quarterly" id="quarterly" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="quarterly">Quarterly</label>
                        </div>
                    </div>
                    <div class="col-md-3 col-lg-2 mb-4">
                        <div class="check">
                            <input type="radio" class="frequency-card" name="frequency" value="annually" id="annually" />
                            <label class="d-flex align-items-center justify-content-center text-white text-uppercase" for="annually">Annually</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="select-package select-cta text-center mb-5">
                <div class="select-package row">
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="25" id="bronze" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="bronze">$25 <span>Bronze</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="50" id="silver" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="silver">$50 <span>Silver</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="100" id="gold" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="gold">$100 <span>Gold</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="250" id="platinum" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="platinum">$250 <span>Platinum</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="500" id="diamond" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="diamond">$500 <span>Diamond</span></label>
                        </div>
                    </div>
                    <div class="col-6 col-md-4 mb-4">
                        <div class="check">
                            <input type="radio" name="package" value="1000" id="philanthropist" />
                            <label class="d-flex align-items-center justify-content-center flex-column p-3" for="philanthropist">$1000 <span>Philanthropist</span></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mb-5">
                <h2 class="mb-3">Or Enter Your Own Amount</h2>
                <div class="input-price mx-auto">
                    <label class="visually-hidden" for="amount">Amount</label>
                    <div class="input-group">
                        <div class="input-group-text">$</div>
                        <input class="form-control" type="number" id="custom-amount" inputmode="numeric" />
                    </div>
                </div>
            </div>
            <div class="text-center mb-5">
                <button class="cta orange border-0 hoverbutton custom-donate-btn">Continue</button>
            </div>
        </form>
    </div>
</section>


    <section>
        <div id="payment-container" class="donation-wrapper d-none">
            <div id="payment-section" class="donation-container d-none">
                <div class="donation-header">
                    <h2 class="donation-title">Make a Difference</h2>
                    <p class="donation-subtitle">Your generosity can change lives</p>
                </div>

                <form class="donation-form" id= "payment-form" method='POST'>
                    @csrf
                    <div class="form-grid">
                        <div class="form-section personal-info">
                            <div class="section-header">
                                <i class="fas fa-user-circle"></i>
                                <h3>Personal Details</h3>
                            </div>

                            <div class="name-fields">
                                <div class="form-group">
                                    <label for="firstName">First Name <span class="required">*</span></label>
                                    <input type="text" id="firstName" name="firstName" required class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name <span class="required">*</label>
                                    <input type="text" id="lastName" name="lastName"  required class="form-control">
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="email">Email Address <span class="required">*</span></label>
                                <input type="email" id="email" name="email" required class="form-control">
                            </div>

                            <div class="name-fields">
                                <div class="form-group">
                                    <label for="city">City <span class="required">*</span></label>
                                    <input type="text" id="city" name="city" required class="form-control">
                                </div>
                                <div class="form-group">
                                    <label for="state">State <span class="required">*</span></label>
                                    <input type="text" id="state" name="state" class="form-control">
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="billing-address"> Billing Address <span class="required">*</span></label>
                                <input type="text" id="billing-address" name="address" class="form-control">
                            </div>
                        </div>

                        <div class="form-section payment-details">
                            <div class="section-header">
                                <i class="fas fa-credit-card"></i>
                                <h3>Payment Information</h3>
                            </div>

                            <div class="stripe-card-wrapper">
                                <div id="card-element" class="stripe-card-element"></div>
                            </div>

                            <div id="card-errors" class="error-message"></div>

                            <div class="donation-amount">
                                <span class="amount-label">Donation Amount</span>
                                <div class="amount-display">$<span id="final-amount">0</span></div>
                            </div>
                        </div>

                        <div class="form-section message-section">
                            <div class="section-header">
                                <i class="fas fa-comment-dots"></i>
                                <h3>Your Message</h3>
                            </div>

                            <div class="form-group">
                                <label for="donationMessage">Share Your Inspiration <span
                                        class="optional">(Optional)</span></label>
                                <textarea id="donationMessage" name="message" class="form-control" rows="4"
                                    placeholder="Write a message of hope..."></textarea>
                            </div>
                        </div>

                        <div class="form-section privacy-section">
                            <div class="section-header">
                                <i class="fas fa-shield-alt"></i>
                                <h3>Privacy Options</h3>
                            </div>

                            <div class="privacy-options">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="anonymousToPublic" name="anonymousToPublic"
                                        class="custom-checkbox">
                                    <label for="anonymousToPublic">Visible to recipient, hidden from public</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="checkbox" id="completeAnonymity" name="completeAnonymity"
                                        class="custom-checkbox">
                                    <label for="completeAnonymity">Complete anonymity</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="donation-submit">
                        <button type="submit" class="btn-donate" id="submit-button">
                            <span class="btn-text">Donate $<span id="btn-final-amount">0</span></span>
                            <span class="btn-icon">
                                <i class="fas fa-heart"></i>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        </div>
    </section>
@endsection
@push('styles')
    @vite('resources/css/pages/card-details.css')
@endpush

@push('scripts')
    <script src="https://js.stripe.com/v3/"></script>

      @vite('resources/js/pages/donor-details.js')


    {{-- <script>
      document.addEventListener('DOMContentLoaded', function () {
        const customDonateBtn = document.querySelector('.custom-donate-btn');
        const customAmountInput = document.getElementById('custom-amount');
        const paymentSection = document.getElementById('payment-section');
        const paymentContainer = document.getElementById('payment-container');
        const finalAmountDisplay = document.getElementById('final-amount');
        const btnFinalAmountDisplay = document.getElementById('btn-final-amount');
        let paymentFrequency;

        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        const style = {
            base: {
                fontSize: '16px',
                color: '#32325d',
                fontFamily: '"Anaheim",-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                '::placeholder': { color: '#aab7c4' },
                iconColor: '#6a11cb',
                // Add these properties for better mobile display
                padding: '10px',
                lineHeight: '1.5',
                '::selection': {
                    backgroundColor: 'rgba(106, 17, 203, 0.1)'
                }
            },
            invalid: {
                color: '#dc3545',
                iconColor: '#dc3545'
            }
        };

        // Create card with mobile-friendly options
        const cardElement = elements.create('card', {
            style,
            hidePostalCode: false,
            // Add these options for better mobile experience
            classes: {
                base: 'stripe-card-base',
                focus: 'stripe-card-focus',
                invalid: 'stripe-card-error'
            }
        });

        const mountStripeElement = () => {
            if (!paymentSection.classList.contains('d-none')) {
                setTimeout(() => {
                    cardElement.mount('#card-element');
                }, 100);
            }
        };

        customDonateBtn.addEventListener("click", () => {
            // Get selected frequency
            const frequencyInput = document.querySelector('input[name="frequency"]:checked');
            if (!frequencyInput) {
                createSlider("Confirm Action", 'Please Select The Payment Frequency', { title: "Select Frequency" });
                return;
            }
             paymentFrequency = frequencyInput.value;

            // Get selected package or custom amount
            const packageInput = document.querySelector('input[name="package"]:checked');
                let amount = null;

                let customAmount = customAmountInput.value.trim();

                if (customAmount && !isNaN(customAmount) && parseFloat(customAmount) > 0) {

                    if (packageInput) {
                        packageInput.checked = false;
                    }
                    amount = parseFloat(customAmount);
                } else if (packageInput) {
                    amount = parseFloat(packageInput.value);
                } else {
                    createSlider("Confirm Action", 'Please enter a valid donation amount', { title: "Enter Amount" });
                    return;
                }

                console.log("Final donation amount:", amount);


            // Show payment section and amount
            finalAmountDisplay.textContent = amount.toFixed(2);
            btnFinalAmountDisplay.textContent = amount.toFixed(2);
            paymentContainer.classList.remove('d-none');
            paymentSection.classList.remove('d-none');


            mountStripeElement();


            setTimeout(() => {
                paymentSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);


            console.log("Frequency:", paymentFrequency);
            console.log("Amount:", amount);
        });


        customAmountInput.addEventListener('input', function () {
            this.classList.remove('is-invalid');
        });


            const paymentForm = document.getElementById('payment-form');
            if (paymentForm) {

                let cardComplete = false;
                cardElement.on('change', function(event) {
                    cardComplete = event.complete;
                });


                paymentForm.addEventListener('submit', async function(event) {

                    event.preventDefault();


                    const formData = new FormData(paymentForm);

                    const submitButton = document.getElementById('submit-button');
                    const formObject = Object.fromEntries(formData.entries());
                    finalAmountDisplayText = finalAmountDisplay.textContent;
                    formObject.amount = parseFloat(finalAmountDisplayText.replace(/,/g, ''));

                    if (paymentFrequency) {
                        formObject.paymentFrequency = paymentFrequency
                    } else {
                        createSlider("dark", "Please Select Payment Frequency.", {
                            title: "Confirm Action"
                        });
                        scrollToElement('.frequency-card');
                        return;
                    }

                    if (!this.checkValidity()) {
                        e.stopPropagation();
                        this.classList.add('was-validated');
                        return;
                    }

                    if (!cardComplete) {
                        createSlider("error", "Please enter complete card details", {
                            title: "Card Incomplete"
                        });
                        scrollToElement('#card-element');
                        return;
                    }



                    showLoader("Processing Payment");
                    submitButton.disabled = true;
                    if (formObject.paymentFrequency === 'oneTime') {
                        await handleOneTimePayment(formObject);
                    } else {
                        await handleRecurringSubscription(formObject);
                    }



                    //handle one time payments
                    async function handleOneTimePayment(formObject) {

                        const intentRoute = route('createPaymentIntentforOrgOneTime');
                        try {
                            const response = await fetch(intentRoute, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify(formObject)
                            });

                            const {
                                clientSecret
                            } = await response.json();

                            const {
                                paymentIntent,
                                error
                            } = await stripe.confirmCardPayment(clientSecret, {
                                payment_method: {
                                    card: cardElement,
                                    billing_details: {
                                        name: `${formObject.firstName} ${formObject.lastName}`,
                                        email: formObject.email,
                                    }
                                }
                            });

                            if (error) {
                                // Record the payment failure
                                await recordPaymentFailure({
                                    paymentIntentId: paymentIntent?.id || 'unknown',
                                    errorCode: error.code || 'unknown',
                                    errorMessage: error.message,
                                    errorType: error.type || 'card_error',
                                    amount: formObject.amount,
                                    customerEmail: formObject.email,
                                    customerName: `${formObject.firstName} ${formObject.lastName}`,
                                    donationType: 'organization_one_time',
                                    donationMessage: formObject.message,
                                    paymentMethodType: 'card',
                                    failureStage: 'client_side_payment_confirmation'
                                });

                                hideLoader();
                                createSlider("error", error.message);
                                submitButton.disabled = false;
                                return;
                            }

                            formObject.paymentIntentId = paymentIntent.id;

                            const storeTransactionRoute = route('storeTransactionForOrgOneTime');

                            const storeResponse = await fetch(storeTransactionRoute, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify(formObject)
                            });

                            const result = await storeResponse.json();
                            if (result.success) {
                                showSuccessAnimation(() => {
                                    window.location.href = result.redirect_url;
                                });
                            } else {
                                hideLoader();
                                submitButton.disabled = false;
                                createSlider("error", result.message);
                            }
                        } catch (err) {
                            hideLoader();
                            submitButton.disabled = false;
                            console.error(err);
                        }
                    }

                    // Helper function to record payment failures
                    async function recordPaymentFailure(failureData) {
                        try {
                            const failureRoute = route('recordPaymentFailure');
                            await fetch(failureRoute, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify(failureData)
                            });
                        } catch (err) {
                            console.error('Failed to record payment failure:', err);
                        }
                    }

                    async function handleRecurringSubscription(formObject) {
                        const subscriptionRoute = route('createSubscriptionForOrg');
                        try {
                            // Step 1: Create the customer and initial payment intent
                            const response = await fetch(subscriptionRoute, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify(formObject)
                            });

                            const {
                                clientSecret,
                                customerId,
                                priceId,
                                paymentIntentId,
                                error
                            } = await response.json();

                            if (error) {
                                hideLoader();
                                createSlider("error", error);
                                submitButton.disabled = false;
                                return;
                            }

                            // Step 2: Confirm the payment using the PaymentIntent
                            const {
                                error: paymentError,
                                paymentIntent
                            } = await stripe.confirmCardPayment(clientSecret, {
                                payment_method: {
                                    card: cardElement,
                                    billing_details: {
                                        name: `${formObject.firstName} ${formObject.lastName}`,
                                        email: formObject.email,
                                    }
                                },
                            });

                            // Handle error during payment confirmation
                            if (paymentError) {
                                // Record the payment failure
                                await recordPaymentFailure({
                                    paymentIntentId: paymentIntentId,
                                    errorCode: paymentError.code || 'unknown',
                                    errorMessage: paymentError.message,
                                    errorType: paymentError.type || 'card_error',
                                    amount: formObject.amount,
                                    customerEmail: formObject.email,
                                    customerName: `${formObject.firstName} ${formObject.lastName}`,
                                    donationType: 'organization_subscription',
                                    donationMessage: formObject.message,
                                    paymentMethodType: 'card',
                                    failureStage: 'client_side_payment_confirmation'
                                });

                                hideLoader();
                                createSlider("error", paymentError.message);
                                submitButton.disabled = false;
                                return;
                            }

                            // Step 3: Create the subscription using the confirmed payment method
                            const confirmSubscriptionRoute = route('confirmSubscription');
                            const confirmResponse = await fetch(confirmSubscriptionRoute, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector(
                                        'meta[name="csrf-token"]').getAttribute(
                                        'content')
                                },
                                body: JSON.stringify({
                                    paymentMethodId: paymentIntent.payment_method,
                                    customerId: customerId,
                                    priceId: priceId,
                                    paymentIntentId: paymentIntentId,
                                    amount: formObject.amount,
                                    paymentFrequency: formObject.paymentFrequency
                                })
                            });

                            const confirmResult = await confirmResponse.json();
                            if (confirmResult.success) {
                                // Step 4: Store the transaction information
                                formObject.paymentIntentId = paymentIntentId;
                                formObject.customerId = customerId;
                                formObject.subscriptionId = confirmResult.subscription;

                                const storeTransactionRoute = route(
                                    'storeTransactionForOrgDonation');
                                const storeResponse = await fetch(storeTransactionRoute, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector(
                                            'meta[name="csrf-token"]').getAttribute(
                                            'content')
                                    },
                                    body: JSON.stringify(formObject)
                                });

                                const result = await storeResponse.json();
                                if (result.success) {
                                    showSuccessAnimation(() => {
                                        window.location.href = result.redirect_url;
                                    });
                                } else {
                                    hideLoader();
                                    createSlider("error", result.message);
                                }
                            } else {
                                hideLoader();
                                createSlider("error", confirmResult.message);
                            }
                        } catch (err) {
                            hideLoader();
                            submitButton.disabled = false;
                            console.error(err);
                        }
                    }









                });
            }
            // if (paymentForm) {



            //     paymentForm.addEventListener('submit', async function(e) {

            //         e.preventDefault();

            //         const formData = new FormData(paymentForm);
            //         const formObject = Object.fromEntries(formData.entries());
            //         finalAmountDisplayText = finalAmountDisplay.textContent;
            //         formObject.amount = parseFloat(finalAmountDisplayText.replace(/,/g, ''));

            //         if (paymentFrequency) {
            //             formObject.paymentFrequency = paymentFrequency
            //         } else {

            //             createSlider("dark", "Please Select Payment Frequency.", {
            //                 title: "Confirm Action"
            //             });
            //             const frequencySection = document.querySelector('.frequency-card');
            //             if (frequencySection) {
            //                 frequencySection.scrollIntoView({
            //                     behavior: 'smooth',
            //                     block: 'center'
            //                 });
            //             }
            //             return;
            //         }

            //         if (!this.checkValidity()) {
            //             e.stopPropagation();
            //             this.classList.add('was-validated');
            //             return;
            //         }
            //         if (!isCardComplete) {
            //             createSlider("error", "Please enter complete card details.", {
            //                 title: "Card Incomplete"
            //             });
            //             cardElement._iframe?.scrollIntoView({
            //                 behavior: "smooth",
            //                 block: "center"
            //             });

            //             return;
            //         }

            //         showLoader("Processing Payment...");

            //         const submitButton = document.getElementById('submit-button');
            //         const amountText = document.getElementById('final-amount').textContent;
            //         const amount = parseFloat(amountText.replace(/,/g, ''));



            //         submitButton.disabled = true;
            //         try {

            //             const response = await fetch(paymentForm.action, {

            //                 method: 'POST',
            //                 headers: {
            //                     'Content-Type': 'application/json',
            //                     'X-CSRF-TOKEN': document.querySelector(
            //                         'meta[name="csrf-token"]').getAttribute('content')
            //                 },
            //                 body: JSON.stringify(formObject)
            //             });

            //             const result = await response.json();
            //             const {
            //                 clientSecret,
            //                 type,
            //                 customerId,
            //                 subscriptionId
            //             } = result;




            //             if (!clientSecret) throw new Error("Failed to create Payment Intent");


            //             //2 Confirm card payment
            //             const cardholderName = document.getElementById('firstName').value + ' ' +
            //                 document.getElementById('lastName').value;
            //             const {
            //                 paymentIntent,
            //                 error
            //             } = await stripe.confirmCardPayment(clientSecret, {

            //                 payment_method: {

            //                     card: cardElement,
            //                     billing_details: {

            //                         name: cardholderName,
            //                         address: {
            //                             line1: document.getElementById('billing-address').value,
            //                             city: document.getElementById('city').value,
            //                             state: document.getElementById('state').value,

            //                         }

            //                     },
            //                 },
            //             });


            //             if (error) {

            //                 hideLoader();
            //                 console.error('Payment Error:', error.message);
            //                 createSlider('error', error.message, {
            //                     title: "Error"
            //                 });
            //             } else if (paymentIntent.status == 'succeeded') {
            //                 console.log('Payment successful');
            //             }

            //             //3. Send final Confirmation to backend

            //             formObject.paymentIntentId = paymentIntent.id;

            //             if (type !== 'oneTime') {
            //                 formObject.customerId = customerId;
            //                 formObject.subscriptionId = subscriptionId;
            //             }


            //             fetch('/store-transaction-org-donation', {

            //                     method: "POST",
            //                     headers: {

            //                         'Content-Type': 'application/json',
            //                         'X-CSRF-TOKEN': document.querySelector(
            //                             'meta[name="csrf-token"]').getAttribute('content')
            //                     },
            //                     body: JSON.stringify(formObject)
            //                 })

            //                 .then(res => res.json())
            //                 .then(data => {
            //                     if (data.success) {
            //                         submitButton.disabled = false;

            //                         showSuccessAnimation(() => {
            //                             window.location.href = data.redirect_url;
            //                         });
            //                     } else {
            //                         hideLoader();
            //                         submitButton.disabled = false;
            //                         createSlider('error', data.message);
            //                     }
            //                 })

            //                 .catch(err => {
            //                     hideLoader();
            //                     createSlider('error', err);
            //                 });
            //         } catch (err) {
            //             hideLoader();
            //             submitButton.disabled = false;
            //             console.error('Unexpected Error:', err);
            //         }
            //     });
            // }


            // cardElement.addEventListener('change', function(event) {
            //     const displayError = document.getElementById('card-errors');
            //     if (event.error) {
            //         displayError.textContent = event.error.message;
            //     } else {
            //         displayError.textContent = '';
            //     }
            // });

            // let isCardComplete = false;

            // cardElement.on('change', function(event) {
            //     isCardComplete = event.complete;

            //     const cardError = document.getElementById('card-errors');
            //     if (event.error) {
            //         cardError.textContent = event.error.message;
            //     } else {
            //         cardError.textContent = '';
            //     }
            // });













            //searching functionality
            let searchButton = document.getElementById("searchButton");
            let searchResults = document.getElementById("searchResults");
            let input = document.getElementById("search");

            searchButton.addEventListener("click", function() {
                let inputValue = input.value.trim();

                if (inputValue === "") {
                    return;
                }

                let url = route("search-athelete-for-donation", {
                    search: inputValue
                });
                fetchUsers(url);
            });

            function fetchUsers(url) {
                // Create a container with fixed height to prevent layout shifts
                searchResults.innerHTML = `
                    <div class="results-container" style="min-height: 600px; position: relative;">
                        <div class="d-flex justify-content-center align-items-center" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="spinner-border" role="status" style="color: #19345E; width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                `;

                fetch(url)
                    .then((response) => response.json())
                    .then((data) => {
                        // Create a container with the same min-height
                        let resultsContainer = `<div class="results-container" style="min-height: 600px;">`;
                        let users = data.data;

                        if (data.next_page_url || data.prev_page_url) {
                            resultsContainer += `
                            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 pagination-container p-3 rounded shadow-sm"
                                 style="background: linear-gradient(145deg, #ffffff, #f8f8ff);">
                                <div class="mb-3 mb-md-0">
                                    <span class="text-muted">Showing results ${data.from || 0} - ${data.to || 0} of ${data.total || 0}</span>
                                </div>
                                <div class="pagination-buttons d-flex gap-2 w-100 justify-content-center justify-content-md-end">`;

                            if (data.prev_page_url) {
                                resultsContainer += `
                                <button class="btn prev-page" data-url="${data.prev_page_url}"
                                        style="background: rgba(25,52,94,0.1); color: #19345E; border: none; border-radius: 20px; padding: 8px 16px; flex: 1 1 auto; max-width: 120px;">
                                    <i class="fas fa-chevron-left me-1"></i> Previous
                                </button>`;
                            }

                            if (data.next_page_url) {
                                resultsContainer += `
                                <button class="btn next-page" data-url="${data.next_page_url}"
                                        style="background: rgba(25, 52, 94, 0.1); color: #19345E; border: none; border-radius: 20px; padding: 8px 16px; flex: 1 1 auto; max-width: 120px;">
                                    Next <i class="fas fa-chevron-right ms-1"></i>
                                </button>`;
                            }

                            resultsContainer += `
                                </div>
                            </div>`;
                        }

                        if (users.length === 0) {
                            resultsContainer += `
                            <div class="alert mt-2 alert-warning p-4 text-center rounded-lg shadow-sm" role="alert">
                                <i class="fas fa-exclamation-circle fa-2x mb-3" style="color: #f39c12;"></i>
                                <h4 class="alert-heading">No Results Found</h4>
                                <p class="mb-0">We couldn't find any athletes matching your search criteria. Please try different keywords.</p>
                            </div>`;
                        } else {
                            resultsContainer += `<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 mb-4">`;

                            users.forEach((user) => {
                                resultsContainer += `
                            <div class="col-sm-6 col-lg-4 col-xl-3 mt-5">
                                <a href="${user.donationPageUrl}" class="text-decoration-none w-100 h-100">
                                    <div class="leader_box d-flex flex-column text-center align-items-center h-100">
                                        <div class="img mb-3">
                                            <img src="${user.profile_photo}" alt="${user.name}" />
                                        </div>
                                        <div class="text">
                                            <h2 class="mb-0">${user.name}</h2>
                                            <h3 class="price">
                                                $${(user.total_collected || 0).toLocaleString()} out of
                                                $${(user.fundraising_goal || 0).toLocaleString()}
                                            </h3>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            `;
                            });

                            resultsContainer += `</div>`;
                        }

                        resultsContainer += `</div>`;
                        searchResults.innerHTML = resultsContainer;

                        // Add event listeners to pagination buttons
                        document.querySelectorAll(".prev-page, .next-page").forEach(button => {
                            button.addEventListener("click", function() {
                                let newUrl = this.getAttribute("data-url");
                                fetchUsers(newUrl);
                            });
                        });

                        // Add hover effects to athlete cards
                        document.querySelectorAll('.athlete-card').forEach(card => {
                            card.addEventListener('mouseenter', function() {
                                this.style.transform = 'translateY(-5px)';
                                this.style.boxShadow = '0 10px 20px rgba(106,17,203,0.15)';
                            });

                            card.addEventListener('mouseleave', function() {
                                this.style.transform = 'translateY(0)';
                                this.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
                            });
                        });
                    })
                    .catch((error) => {
                        searchResults.innerHTML = `
                        <div class="alert alert-danger p-4 text-center rounded-lg shadow-sm" role="alert">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <h4 class="alert-heading">Error</h4>
                            <p class="mb-0">Sorry, something went wrong: ${error.message}</p>
                        </div>`;
                    });
            }

            function isElementInViewport(el) {
                const rect = el.getBoundingClientRect();
                return (
                    rect.top >= 0 &&
                    rect.left >= 0 &&
                    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                );
            }

            function scrollToElement(selector, block = 'center') {
                const element = document.querySelector(selector);
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block
                    });
                }
            }

            function showLoader(message) {
                if (!document.getElementById('full-screen-loader')) {
                    const loader = document.createElement('div');
                    loader.id = 'full-screen-loader';
                    loader.innerHTML = `
            <div class="loader-icon">
                <div class="spinner-border" role="status"></div>
            </div>
            <p>${message}</p>
        `;
                    document.body.appendChild(loader);
                }
            }
            // Hide Loader
            function hideLoader() {
                const loader = document.getElementById('full-screen-loader');
                if (loader) loader.remove();
            }

            function showSuccessAnimation(callback) {
                const loader = document.getElementById('full-screen-loader');
                if (loader) {
                    loader.innerHTML = `
            <div class="success-animation">
                   🎉
            </div>
            <p>Thank You for Your Donation!</p>
        `;
                    setTimeout(() => {
                        callback();
                    }, 2000);
                }
            }
        });
    </script> --}}
@endpush







{{-- <div class="donate-wrapper py-5">
    <div class="container">
        <div class="text-center mb-4">
            <h1 class="display-4 fw-bold" style="color: #6a11cb; text-shadow: 1px 1px 3px rgba(0,0,0,0.1);">
                Support a Fundraiser
            </h1>
            <p class="lead" style="color: #2575fc;">
                Your generosity can make a difference. Find a cause that speaks to your heart.
            </p>
        </div>

        <div class="row justify-content-center mb-5">
            <div class="col-12">
                <div class="search-container p-4 rounded-lg shadow"
                    style="background: linear-gradient(145deg, #ffffff, #f8f8ff);">
                    <div class="row g-3">
                        <div class="col-12 mb-3">
                            <h3 class="h4 mb-3" style="color: #6a11cb;">Find an Athlete</h3>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text border-0" style="background: rgba(106,17,203,0.1);">
                                    <i class="fas fa-search" style="color: #6a11cb;"></i>
                                </span>
                                <input type="text" id="search" class="form-control border-0"
                                    placeholder="Search by Name or Town"
                                    style="box-shadow: none; background: rgba(106,17,203,0.05);">
                                <button id="searchButton" class="btn px-4 search-btn"
                                    style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); color: white;">
                                    Search
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="searchResults">

        </div>

        <div class="row justify-content-center mt-5 pt-4 border-top">
            <div class="col-lg-10 text-center">
                <h2 class="h3 mb-4" style="color: #6a11cb;">Choose Your Support Level</h2>
                <p class="text-muted mb-5">Select one of our premium donation tiers or customize your contribution.</p>

                <div>Choose Your Dontion Frequency</div>
                <div class="donation-frequency-cards">

                    <div data-id="oneTime" class="frequency-card">One time</div>
                    <div data-id="daily" class="frequency-card">Daily for testing</div>
                    <div data-id="monthly" class="frequency-card">monthly</div>
                    <div data-id="quarterly"class="frequency-card">quarterly</div>
                    <div data-id="annually" class="frequency-card">annualy</div>
                </div>

                <div class="donation-tiers">

                    <div class="row justify-content-center gx-4 gy-4 mb-5">

                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card bonus">
                                <div class="card-badge">
                                    <span>BONUS</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <h3 class="tier-title">Bonus</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$25</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Exclusive Supporter Badge</li>
                                        <li><i class="fas fa-check-circle"></i> Thank You Certificate</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card silver">
                                <div class="card-badge">
                                    <span>SILVER</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-medal"></i>
                                    </div>
                                    <h3 class="tier-title">Silver</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$50</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Silver Supporter Badge</li>
                                        <li><i class="fas fa-check-circle"></i> Thank You Certificate</li>
                                        <li><i class="fas fa-check-circle"></i> Monthly Newsletter</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card gold">
                                <div class="card-badge">
                                    <span>GOLD</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <h3 class="tier-title">Gold</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$100</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Gold Supporter Badge</li>
                                        <li><i class="fas fa-check-circle"></i> Personalized Thank You</li>
                                        <li><i class="fas fa-check-circle"></i> Quarterly Report</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card platinum">
                                <div class="card-badge">
                                    <span>PLATINUM</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-award"></i>
                                    </div>
                                    <h3 class="tier-title">Platinum</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$250</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Platinum Supporter Badge</li>
                                        <li><i class="fas fa-check-circle"></i> Premium Recognition</li>
                                        <li><i class="fas fa-check-circle"></i> Exclusive Updates</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card diamond">
                                <div class="card-badge">
                                    <span>DIAMOND</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-gem"></i>
                                    </div>
                                    <h3 class="tier-title">Diamond</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$500</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Diamond Supporter Badge</li>
                                        <li><i class="fas fa-check-circle"></i> VIP Recognition</li>
                                        <li><i class="fas fa-check-circle"></i> Impact Report</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>


                        <div class="col-md-6 col-lg-4">
                            <div class="donation-card philanthropist">
                                <div class="card-badge special">
                                    <span>PHILANTHROPIST</span>
                                </div>
                                <div class="card-header">
                                    <div class="tier-icon">
                                        <i class="fas fa-crown"></i>
                                    </div>
                                    <h3 class="tier-title">Philanthropist</h3>
                                </div>
                                <div class="card-body">
                                    <div class="amount">$1,000</div>
                                    <ul class="benefits">
                                        <li><i class="fas fa-check-circle"></i> Elite Supporter Status</li>
                                        <li><i class="fas fa-check-circle"></i> Major Donor Recognition</li>
                                        <li><i class="fas fa-check-circle"></i> Personal Impact Report</li>
                                    </ul>
                                    <button class="select-tier-btn">Select</button>
                                </div>
                            </div>
                        </div>



                    </div>


                    <div class="custom-donation mt-5 mb-5">
                        <div class="custom-donation-container">
                            <div class="custom-donation-header">
                                <div class="tier-icon custom-icon">
                                    <i class="fas fa-hand-holding-heart"></i>
                                </div>
                                <h3>Custom Donation</h3>
                                <p>Enter your preferred amount below</p>
                            </div>
                            <div class="custom-donation-input">
                                <div class="input-wrapper">
                                    <div class="currency-symbol">$</div>
                                    <input type="number" id="custom-amount" placeholder="0" min="1">
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <button class="custom-donate-btn">
                                Donate Custom Amount
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> --}}



{{-- <style>
    .donation-frequency-cards {
        display: flex;
        flex-direction: row;
        height: 7%;
        justify-content: space-around;
        align-items: center;

    }

    .frequency-card {
        background-color: #19345E;
        color: white;
        border-radius: 10px;
        padding-left: 2rem;
        padding-right: 2rem;
        padding-top: .3rem;
        padding-bottom: .3rem;
        cursor: pointer;
    }

    .frequency-card.active {
        background-color: #f05522;
    }
</style> --}}





