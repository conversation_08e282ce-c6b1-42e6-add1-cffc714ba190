<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class FailedTransaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'payment_intent_id',
        'customer_id',
        'subscription_id',
        'amount',
        'amount_charged',
        'currency',
        'failure_stage',
        'failure_code',
        'failure_message',
        'failure_reason',
        'failure_description',
        'payment_method_id',
        'payment_method_type',
        'payment_method_brand',
        'payment_method_last4',
        'customer_email',
        'customer_name',
        'customer_phone',
        'donated_to_slug',
        'donation_type',
        'donation_message',
        'error_details',
        'processing_log',
        'request_data',
        'status',
        'retry_count',
        'failed_at',
        'last_retry_at',
        'next_retry_at',
        'resolved_at',
        'resolved_by',
        'resolution_notes',
        'resolution_action',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'amount_charged' => 'decimal:2',
        'error_details' => 'array',
        'processing_log' => 'array',
        'request_data' => 'array',
        'metadata' => 'array',
        'failed_at' => 'datetime',
        'last_retry_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'failed_at',
        'last_retry_at',
        'next_retry_at',
        'resolved_at',
    ];

    /**
     * Scope for failed transactions that can be retried
     */
    public function scopeRetryable($query)
    {
        return $query->whereIn('status', ['failed', 'retry_pending'])
                    ->where('retry_count', '<', 3)
                    ->where(function ($q) {
                        $q->whereNull('next_retry_at')
                          ->orWhere('next_retry_at', '<=', now());
                    });
    }

    /**
     * Scope for unresolved failed transactions
     */
    public function scopeUnresolved($query)
    {
        return $query->whereNotIn('status', ['resolved', 'abandoned']);
    }

    /**
     * Scope for failed transactions by stage
     */
    public function scopeByStage($query, $stage)
    {
        return $query->where('failure_stage', $stage);
    }

    /**
     * Scope for failed transactions by failure code
     */
    public function scopeByFailureCode($query, $code)
    {
        return $query->where('failure_code', $code);
    }

    /**
     * Get the user this failed transaction was for
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'donated_to_slug', 'slug');
    }

    /**
     * Check if this failed transaction can be retried
     */
    public function canBeRetried(): bool
    {
        return in_array($this->status, ['failed', 'retry_pending'])
            && $this->retry_count < 3
            && ($this->next_retry_at === null || $this->next_retry_at <= now());
    }

    /**
     * Mark as retry pending
     */
    public function markAsRetryPending(): void
    {
        $this->update([
            'status' => 'retry_pending',
            'retry_count' => $this->retry_count + 1,
            'last_retry_at' => now(),
            'next_retry_at' => $this->calculateNextRetryTime(),
        ]);
    }

    /**
     * Mark as retry processing
     */
    public function markAsRetryProcessing(): void
    {
        $this->update([
            'status' => 'retry_processing',
        ]);
    }

    /**
     * Mark as resolved
     */
    public function markAsResolved(string $resolvedBy, string $action, ?string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy,
            'resolution_action' => $action,
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Mark as abandoned
     */
    public function markAsAbandoned(string $resolvedBy, ?string $notes = null): void
    {
        $this->update([
            'status' => 'abandoned',
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy,
            'resolution_action' => 'abandoned',
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Add a log entry to processing_log
     */
    public function addLogEntry(string $stage, string $message, array $data = []): void
    {
        $log = $this->processing_log ?? [];
        $log[] = [
            'timestamp' => now()->toISOString(),
            'stage' => $stage,
            'message' => $message,
            'data' => $data,
        ];

        $this->update(['processing_log' => $log]);
    }

    /**
     * Calculate next retry time based on retry count
     */
    private function calculateNextRetryTime(): Carbon
    {
        $delays = [
            1 => 5,    // 5 minutes
            2 => 30,   // 30 minutes
            3 => 120,  // 2 hours
        ];

        $delay = $delays[$this->retry_count + 1] ?? 120;
        return now()->addMinutes($delay);
    }

    /**
     * Get human readable failure reason
     */
    public function getFailureReasonText(): string
    {
        $reasons = [
            'card_declined' => 'Card was declined by the bank',
            'insufficient_funds' => 'Insufficient funds in the account',
            'expired_card' => 'Card has expired',
            'incorrect_cvc' => 'Incorrect CVC code',
            'processing_error' => 'Payment processing error',
            'invalid_request' => 'Invalid payment request',
            'authentication_required' => '3D Secure authentication required',
            'rate_limit' => 'Too many requests',
            'invalid_payment_method' => 'Invalid payment method',
            'card_not_supported' => 'Card type not supported',
            'currency_not_supported' => 'Currency not supported',
            'duplicate_transaction' => 'Duplicate transaction detected',
            'fraudulent' => 'Transaction flagged as potentially fraudulent',
            'network_error' => 'Network connectivity issue',
            'timeout' => 'Request timed out',
            'server_error' => 'Server error occurred',
            'unknown' => 'Unknown error occurred',
        ];

        return $reasons[$this->failure_reason] ?? 'Unknown error';
    }

    /**
     * Check if amount was charged despite failure
     */
    public function wasAmountCharged(): bool
    {
        return $this->amount_charged > 0;
    }

    /**
     * Get the difference between requested and charged amount
     */
    public function getAmountDifference(): float
    {
        return ($this->amount ?? 0) - ($this->amount_charged ?? 0);
    }
}
