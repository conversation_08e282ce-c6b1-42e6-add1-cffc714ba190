<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class redirectStageUsersToLive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        if (app()->environment(['staging'])) {
            logger('Inside staging environment');

            $cookie = $request->cookie('staging_access');
            $token  = config('app.staging_cookie_token');

            logger('token: ' . $token);

            logger(' staging_access cookie: ' . var_export($cookie, true));

            if ($cookie !== $token) {
                logger(' No valid cookie set');

                if ($request->query('access') === $token) {
                    logger(' Access token matched. Setting cookie.');

                    return response('Access granted')->with<PERSON><PERSON>ie(
                        cookie('staging_access', $token, 60 * 24 * 30)
                    );
                }

                logger(' Redirecting to live site');
                return redirect('https://gauntlet.ashotforlife.org');
            } else {
                logger(' Valid cookie present. Access granted');
            }
        }

        return $next($request);
    }
}
