
[2025-07-14 12:53:54] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:53:54] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:53:54] local.DEBUG: ✅ Valid cookie present. Access granted  
[2025-07-14 12:55:55] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:56:00] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:56:00] local.DEBUG: token:   
[2025-07-14 12:56:00] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:56:00] local.DEBUG: ✅ Valid cookie present. Access granted  
[2025-07-14 12:57:16] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:57:30] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:57:30] local.DEBUG: token: gauntlet  
[2025-07-14 12:57:30] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:57:30] local.DEBUG: ❌ No valid cookie set  
[2025-07-14 12:57:30] local.DEBUG: 🚫 Redirecting to live site  
[2025-07-14 12:58:33] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 12:58:47] local.DEBUG: ✅ Inside staging/local environment  
[2025-07-14 12:58:47] local.DEBUG: token: gauntlet  
[2025-07-14 12:58:47] local.DEBUG: 🍪 staging_access cookie: NULL  
[2025-07-14 12:58:47] local.DEBUG: ❌ No valid cookie set  
[2025-07-14 12:58:47] local.DEBUG: 🚫 Redirecting to live site  
[2025-07-14 12:59:08] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-14 13:07:20] staging.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 06:24:57] staging.DEBUG: Inside staging environment  
[2025-07-15 06:24:57] staging.DEBUG: token: gauntlet  
[2025-07-15 06:24:57] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:24:57] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:24:57] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:05] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:05] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:05] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:05] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:05] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:06] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:06] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:06] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:06] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:06] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:15] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:15] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:15] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:15] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:15] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:19] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:19] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:19] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:19] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:19] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:44] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:44] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:44] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:44] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:44] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:46] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:46] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:46] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:46] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:46] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:52] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:52] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:52] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:52] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:52] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:25:52] staging.DEBUG: Inside staging environment  
[2025-07-15 06:25:52] staging.DEBUG: token: gauntlet  
[2025-07-15 06:25:52] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:25:52] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:25:52] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:18] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:18] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:18] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:18] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:18] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:19] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:19] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:19] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:19] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:19] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:45] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:45] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:45] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:45] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:45] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:48] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:48] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:48] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:48] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:48] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:26:59] staging.DEBUG: Inside staging environment  
[2025-07-15 06:26:59] staging.DEBUG: token: gauntlet  
[2025-07-15 06:26:59] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:26:59] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:26:59] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:04] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:04] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:04] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:04] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:04] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:10] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:10] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:10] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:10] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:10] staging.DEBUG:  Redirecting to live site  
[2025-07-15 06:27:27] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:27] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:27] staging.DEBUG:  staging_access cookie: NULL  
[2025-07-15 06:27:27] staging.DEBUG:  No valid cookie set  
[2025-07-15 06:27:27] staging.DEBUG:  Access token matched. Setting cookie.  
[2025-07-15 06:27:29] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:29] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:29] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:29] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:35] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:35] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:35] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:35] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:37] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:37] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:37] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:37] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:27:46] staging.DEBUG: Inside staging environment  
[2025-07-15 06:27:46] staging.DEBUG: token: gauntlet  
[2025-07-15 06:27:46] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:27:46] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:29:16] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2MZKQoAc42Xce0lpMYfce","idempotency_key":"payment_intent_c0e0690c633e28421d63abd8e90acdc9","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:29:16] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2MZKQoAc42Xce0lpMYfce","error_code":"incomplete_number","error_message":"Your card number is incomplete.","donation_type":"user_donation"} 
[2025-07-15 06:35:00] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2S8KQoAc42Xce0fN1xv3I","idempotency_key":"payment_intent_005d66a26b5732d43fffeb546ed713fd","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:35:07] staging.DEBUG: Inside staging environment  
[2025-07-15 06:35:07] staging.DEBUG: token: gauntlet  
[2025-07-15 06:35:07] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:35:07] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:35:36] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2ShKQoAc42Xce1VHBeXrj","idempotency_key":"payment_intent_cab25a310e8c950d65480d831026af57","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:37:05] staging.DEBUG: Inside staging environment  
[2025-07-15 06:37:05] staging.DEBUG: token: gauntlet  
[2025-07-15 06:37:05] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:37:05] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:37:38] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2UgKQoAc42Xce1Od6NoLP","idempotency_key":"payment_intent_10d3b8a2aa9e7b723c364d91138fbe34","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:37:45] staging.DEBUG: Inside staging environment  
[2025-07-15 06:37:45] staging.DEBUG: token: gauntlet  
[2025-07-15 06:37:45] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:37:45] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:38:17] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:17] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:26] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:27] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_351c169faf24a254a91e6a6332390dc0","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:27] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 06:38:27] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2VJKQoAc42Xce0zwHbPYr","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 06:38:56] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2VwKQoAc42Xce1oh4CD0S","idempotency_key":"payment_intent_a0d9155c9f054817e789f2005ff77c11","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:38:57] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_a0d9155c9f054817e789f2005ff77c11","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:40:52] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2XoKQoAc42Xce10wZT54y","idempotency_key":"payment_intent_59ef59a432e8d77948e306e77efbcf27","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:40:52] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_59ef59a432e8d77948e306e77efbcf27","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:44:11] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2b0KQoAc42Xce1Kd0Mkfi","idempotency_key":"payment_intent_0a4bbb415feb2454d8b2726dc25dff8c","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:44:11] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_0a4bbb415feb2454d8b2726dc25dff8c","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:44:50] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2beKQoAc42Xce13Ue4eTZ","idempotency_key":"payment_intent_028276ed09fd93005582d689be47acea","email":"<EMAIL>","amount":"25"} 
[2025-07-15 06:44:59] staging.DEBUG: Inside staging environment  
[2025-07-15 06:44:59] staging.DEBUG: token: gauntlet  
[2025-07-15 06:44:59] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:44:59] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:45:44] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2cVKQoAc42Xce099eHp6x","idempotency_key":"payment_intent_2a93c77c81a59167d76df299d4032c9c","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:45:44] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_2a93c77c81a59167d76df299d4032c9c","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:45:46] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_2a93c77c81a59167d76df299d4032c9c","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:45:47] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_2a93c77c81a59167d76df299d4032c9c","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:45:47] staging.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl2cVKQoAc42Xce099eHp6x","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 06:46:24] staging.DEBUG: Inside staging environment  
[2025-07-15 06:46:24] staging.DEBUG: token: gauntlet  
[2025-07-15 06:46:24] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:46:24] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:47:18] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2e2KQoAc42Xce0I3MpS1r","idempotency_key":"payment_intent_b0392ad8688d6e9494b925d30ded43a2","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:47:19] staging.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_b0392ad8688d6e9494b925d30ded43a2","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:51:23] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2hzKQoAc42Xce095DQfTx","idempotency_key":"payment_intent_d2046f391142e0b12cd6b47d391ebfdb","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:51:31] staging.DEBUG: Inside staging environment  
[2025-07-15 06:51:31] staging.DEBUG: token: gauntlet  
[2025-07-15 06:51:31] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:51:31] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:52:22] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2iwKQoAc42Xce0CQYWJiJ","idempotency_key":"payment_intent_de0294de3a61cafd63709b27e846249b","email":"<EMAIL>","amount":"250"} 
[2025-07-15 06:53:13] staging.DEBUG: Inside staging environment  
[2025-07-15 06:53:13] staging.DEBUG: token: gauntlet  
[2025-07-15 06:53:13] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 06:53:13] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 06:55:35] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2m3KQoAc42Xce148L86il","idempotency_key":"payment_intent_23365d4021ec10b4c56ddd1811e2d6ca","email":"<EMAIL>","amount":"25"} 
[2025-07-15 07:03:06] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2tKKQoAc42Xce0xsCIEyV","idempotency_key":"payment_intent_541a783e63f603121dc9f33537dfa6a4","email":"<EMAIL>","amount":"25"} 
[2025-07-15 07:03:25] staging.DEBUG: Inside staging environment  
[2025-07-15 07:03:25] staging.DEBUG: token: gauntlet  
[2025-07-15 07:03:25] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 07:03:25] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 07:04:08] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2uKKQoAc42Xce1ORozxi4","idempotency_key":"payment_intent_ab20c44f693549106351470da6f3f390","email":"<EMAIL>","amount":"250"} 
[2025-07-15 07:04:21] staging.DEBUG: Inside staging environment  
[2025-07-15 07:04:21] staging.DEBUG: token: gauntlet  
[2025-07-15 07:04:21] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 07:04:21] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 07:05:42] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2vqKQoAc42Xce0S3FinG0","idempotency_key":"payment_intent_c5fcaff4ffb75b4293a3fdd24033e19d","email":"<EMAIL>","amount":"25"} 
[2025-07-15 07:06:04] staging.DEBUG: Inside staging environment  
[2025-07-15 07:06:04] staging.DEBUG: token: gauntlet  
[2025-07-15 07:06:04] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 07:06:04] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 07:08:39] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl2yhKQoAc42Xce1DHyIZqQ","idempotency_key":"payment_intent_0db232c32c63b128c5fa8788d50331b0","email":"<EMAIL>","amount":"250"} 
[2025-07-15 07:09:44] staging.DEBUG: Inside staging environment  
[2025-07-15 07:09:44] staging.DEBUG: token: gauntlet  
[2025-07-15 07:09:44] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 07:09:44] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 07:10:14] staging.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl30EKQoAc42Xce00dYkZiN","idempotency_key":"payment_intent_41a58ebf489b0825a8c4ffed11d1d6b9","email":"<EMAIL>","amount":"500"} 
[2025-07-15 07:10:22] staging.DEBUG: Inside staging environment  
[2025-07-15 07:10:22] staging.DEBUG: token: gauntlet  
[2025-07-15 07:10:22] staging.DEBUG:  staging_access cookie: 'gauntlet'  
[2025-07-15 07:10:22] staging.DEBUG:  Valid cookie present. Access granted  
[2025-07-15 08:19:18] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:19:18] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:mode...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:21:52] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:24:26] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:24:44] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:25:00] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:25:10] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:26:48] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:27:47] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:28:16] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:29:40] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 08:33:18] local.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl4IcKQoAc42Xce0qncBZuE","idempotency_key":"payment_intent_44bc19c2e3cbed945b9d92b53e6ab5fd","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:33:56] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_44bc19c2e3cbed945b9d92b53e6ab5fd","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:33:57] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl4IcKQoAc42Xce0qncBZuE","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 08:34:33] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_44bc19c2e3cbed945b9d92b53e6ab5fd","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:34:34] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl4IcKQoAc42Xce0qncBZuE","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 08:34:46] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_44bc19c2e3cbed945b9d92b53e6ab5fd","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:34:48] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl4IcKQoAc42Xce0qncBZuE","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 08:35:55] local.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl4L9KQoAc42Xce163UKmrm","idempotency_key":"payment_intent_14df0f023e5e6ccc492c01e1d8b7030f","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:36:22] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_14df0f023e5e6ccc492c01e1d8b7030f","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:36:23] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl4L9KQoAc42Xce163UKmrm","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 08:36:24] local.INFO: Returning existing payment intent for duplicate request {"idempotency_key":"payment_intent_14df0f023e5e6ccc492c01e1d8b7030f","email":"<EMAIL>","amount":"1000.00"} 
[2025-07-15 08:36:25] local.INFO: Client-side payment failure recorded {"payment_intent_id":"pi_3Rl4L9KQoAc42Xce163UKmrm","error_code":"payment_intent_unexpected_state","error_message":"A processing error occurred.","donation_type":"user_donation"} 
[2025-07-15 09:37:20] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 09:37:36] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 09:40:58] local.ERROR: There are no commands defined in the "ide-helper" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"ide-helper\" namespace. at C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('ide-helper')
#1 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('ide-helper:gene...')
#2 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\ASFL-Gauntlet\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\ASFL-Gauntlet\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-07-15 09:41:24] local.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl5MXKQoAc42Xce1EU5XMX2","idempotency_key":"payment_intent_65ba003d1f2bcf85247948f50a7abcc0","email":"<EMAIL>","amount":"200.00"} 
[2025-07-15 09:42:45] local.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl5NqKQoAc42Xce0bdLa3K4","idempotency_key":"payment_intent_dd98de1b47fe79f9f6b6b4924327455f","email":"<EMAIL>","amount":"25.00"} 
[2025-07-15 09:44:22] local.INFO: Created new payment intent {"payment_intent_id":"pi_3Rl5POKQoAc42Xce1u68vZG1","idempotency_key":"payment_intent_75813ccb381bceafa8a011aeb1941346","email":"<EMAIL>","amount":"250.00"} 
