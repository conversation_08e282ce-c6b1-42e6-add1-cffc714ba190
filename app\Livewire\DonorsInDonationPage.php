<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User; // Import User model

class DonorsInDonationPage extends Component
{
    use WithPagination;

    public $search;
    public $slug;
    public $user;

    protected $queryString = ['search'];
    protected $paginationTheme = 'bootstrap';

    public function mount($slug)
    {
        $this->user = User::where('slug', $slug)->firstOrFail();
    }

    public function updatingSearch()
    {
        $this->search = trim($this->search);
        $this->resetPage();
    }

    public function render()
    {

        if (!$this->user) {
            return view('livewire.donors-in-donation-page', ['recentDonors' => collect()]);
        }

        $donorsQuery = $this->user->donors()
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    if (strtolower($this->search) === 'anonymous') {
                        $query->where('anonymous_for_public', true);
                    } else {
                        $query->where('anonymous_for_public', false)
                            ->where(function ($subQuery) {
                                $subQuery->where('name', 'like', '%' . $this->search . '%')
                                    ->orWhere('email', 'like', '%' . $this->search . '%');
                            });
                    }
                });
            })
            ->orderByDesc('user_donors.created_at')
            ->paginate(5);

        $donors = $donorsQuery->through(function ($donor) {
            return [
                'name' => $donor->anonymous_for_public ? 'Anonymous' : $donor->name,
                'amount' => $donor->pivot->amount,
                'message_for_fundraiser' => $donor->message_for_fundraiser,
                'created_at' => $donor->pivot->created_at,
            ];
        });

        return view('livewire.donors-in-donation-page', [
            'recentDonors' => $donors,
        ]);
    }
}
