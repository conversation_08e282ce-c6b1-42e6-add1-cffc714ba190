<?php

namespace App\Listeners;

use App\Events\PaymentRecieved;
use App\Notifications\ThanksReceipt;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendThankYouEmailToDonor
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentRecieved $event): void
    {

        $donor = $event->donor;
        $amount = $event->amount;

        $donor->notify(new ThanksReceipt($amount, $donor));
    }
}
