import { createSlider } from '../appTwo.js';

// Handle validation errors function
function handleValidationErrors(errors) {
    document.querySelectorAll(".is-invalid").forEach((el) => el.classList.remove("is-invalid"));
    document.querySelectorAll(".invalid-feedback").forEach((el) => el.remove());

    let firstErrorField = null;

    Object.entries(errors).forEach(([field, messages]) => {
        const input = document.querySelector(`[name="${field}"]`);

        if (input) {
            input.classList.add("is-invalid");
            if (!firstErrorField) {
                firstErrorField = input;
            }

            const errorDiv = document.createElement("div");
            errorDiv.className = "invalid-feedback";
            errorDiv.textContent = messages[0];
            input.parentElement.appendChild(errorDiv);
        }
    });

    if (firstErrorField) {
        firstErrorField.scrollIntoView({
            behavior: "smooth",
            block: "center",
        });
    }
}

// Program data
const programData = {
    '24-hour': {
        name: '24 Hour Challenge',
        description: 'Join the ultimate endurance challenge with maximum fundraising potential and exclusive participant benefits.',
        icon: 'fas fa-clock',
        badge: 'Popular',
        features: [
            'Epic 24-hour basketball marathon',
            'Unlimited fundraising potential',
            'Premium participant gear & swag',
            'VIP event access & recognition',
            'Exclusive achievement badges'
        ],
        requirements: [
            'Minimum $1,000 fundraising goal',
            'Medical clearance required',
            'Must attend pre-event briefing'
        ]
    },
    '4-hour': {
        name: '4 Hour Session',
        description: 'Perfect for busy schedules with flexible time slots and standard participant benefits.',
        icon: 'fas fa-stopwatch',
        badge: 'Flexible',
        features: [
            'High-intensity 4-hour session',
            'Flexible time slot selection',
            'Professional participant kit',
            'Full event access & networking',
            'Achievement recognition'
        ],
        requirements: [
            'Minimum $1,000 fundraising goal',
            'Basic fitness level',
            'Attend orientation session'
        ]
    },
    'virtual': {
        name: 'Virtual Player',
        description: 'Participate remotely from anywhere in the world while still making a meaningful impact.',
        icon: 'fas fa-globe',
        badge: 'Remote',
        features: [
            'Global remote participation',
            'Live virtual event streaming',
            'Digital participant toolkit',
            'Online community platform',
            'Virtual achievement showcase'
        ],
        requirements: [
            'Minimum $1,000 fundraising goal',
            'Internet connection required',
            'Virtual orientation session'
        ]
    }
};

// Function to render program cards
function renderProgramCards(programs, userPrograms) {
    const programCardsContainer = document.getElementById('program-cards');

    const cardsHTML = programs.map(program => {
        const data = programData[program.name];
        const isSelected = userPrograms.includes(program.name);
        const badgeClass = isSelected ? 'program-badge current-selection' : 'program-badge';
        const badgeText = isSelected ? 'Current Selection' : data.badge;

        return '<div class="col-md-4">' +
            '<div class="program-card ' + (isSelected ? 'selected' : '') + '" data-program="' + program.name + '">' +
                '<div class="program-card__header">' +
                    '<div class="program-icon">' +
                        '<i class="' + data.icon + '"></i>' +
                    '</div>' +
                    '<h3 class="program-title">' + data.name + '</h3>' +
                    '<div class="' + badgeClass + '">' + badgeText + '</div>' +
                '</div>' +
                '<div class="program-card__body">' +
                    '<div class="program-description">' +
                        '<p>' + data.description + '</p>' +
                    '</div>' +
                    '<div class="program-features">' +
                        '<ul>' +
                            data.features.map(feature => '<li><i class="fas fa-check"></i> ' + feature + '</li>').join('') +
                        '</ul>' +
                    '</div>' +
                    '<div class="program-requirements">' +
                        '<h4>Requirements:</h4>' +
                        '<ul>' +
                            data.requirements.map(req => '<li>' + req + '</li>').join('') +
                        '</ul>' +
                    '</div>' +
                '</div>' +
                '<div class="program-card__footer">' +
                    '<button class="cta orange border-0 hoverbutton program-select-btn" data-program="' + program.name + '">' +
                        (isSelected ? 'Selected' : 'Select') +
                    '</button>' +
                '</div>' +
            '</div>' +
        '</div>';
    }).join('');

    programCardsContainer.innerHTML = cardsHTML;
}

// Function to render the selection form
function renderSelectionForm(user) {
    const formContainer = document.getElementById('program-selection-form');
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    formContainer.innerHTML = '<div class="program-selection-form mt-5" id="programSelectionForm" style="display: none;">' +
        '<div class="card shadow-sm">' +
            '<div class="card-body p-4">' +
                '<h3 class="text-center mb-4" id="formTitle">Confirm Your Program Selection</h3>' +
                '<form id="confirmProgramForm" method="POST" action="/selected-program">' +
                    '<input type="hidden" name="_token" value="' + csrfToken + '">' +
                    '<input type="hidden" name="selected_program" id="selectedProgramInput">' +
                    '<input type="hidden" name="user_slug" value="' + user.slug + '">' +
                    '<div class="row">' +
                        '<div class="col-md-8 mx-auto">' +
                            '<div class="selected-program-summary mb-4">' +
                                '<div class="alert">' +
                                    '<h5 class="mb-2">Selected Program: <span id="selectedProgramName"></span></h5>' +
                                    '<p class="mb-0" id="selectedProgramDescription"></p>' +
                                '</div>' +
                            '</div>' +
                            '<div class="form-check mb-4">' +
                                '<input class="form-check-input" type="checkbox" id="confirmSelection" name="confirm_selection" required>' +
                                '<label class="form-check-label" for="confirmSelection" id="confirmLabel">' +
                                    'I confirm that I have selected the program that best suits my schedule and commitment level. I understand the requirements and am ready to proceed with my fundraising journey.' +
                                '</label>' +
                            '</div>' +
                            '<div class="text-center">' +
                                '<button type="submit" class="cta orange border-0 hoverbutton" id="confirmProgramBtn" disabled>Continue</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</form>' +
            '</div>' +
        '</div>' +
    '</div>';
}

// Function to render the complete page
function renderPage(data) {
    const { user, programs, userPrograms } = data;
    const app = document.getElementById('program-selection-app');

    app.innerHTML = '<section class="hero--title">' +
        '<div class="container-fluid gx-0">' +
            '<div class="bg-blue">' +
                '<div class="container">' +
                    '<div class="row justify-content-center">' +
                        '<div class="col-lg-8 text-center">' +
                            '<h1 class="text-uppercase mb-4">Choose Your <strong>Program</strong></h1>' +
                            '<p class="lead text-white mb-0">Select the program that best fits your schedule and commitment level for the A Shot for Life Gauntlet.</p>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</section>' +
    '<section class="sec program-selection">' +
        '<div class="container">' +
            '<div class="row justify-content-center">' +
                '<div class="col-lg-10">' +
                    '<div class="program-cards">' +
                        '<div class="row g-4" id="program-cards"></div>' +
                    '</div>' +
                    '<div id="program-selection-form"></div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</section>';

    // Render program cards and form
    renderProgramCards(programs, userPrograms);
    renderSelectionForm(user);

    // Initialize event listeners
    initializeEventListeners(userPrograms);
}

// Function to initialize event listeners
function initializeEventListeners(userPrograms) {
    const programCards = document.querySelectorAll('.program-card');
    const programSelectionForm = document.getElementById('programSelectionForm');
    const selectedProgramInput = document.getElementById('selectedProgramInput');
    const selectedProgramName = document.getElementById('selectedProgramName');
    const selectedProgramDescription = document.getElementById('selectedProgramDescription');
    const confirmSelection = document.getElementById('confirmSelection');
    const confirmProgramBtn = document.getElementById('confirmProgramBtn');

    // Function to select a program
    function selectProgram(programType, card) {
        programCards.forEach(c => c.classList.remove('selected'));
        card.classList.add('selected');

        selectedProgramInput.value = programType;
        selectedProgramName.textContent = programData[programType].name;
        selectedProgramDescription.textContent = programData[programType].description;

        programSelectionForm.style.display = 'block';
        programSelectionForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Check if user has already selected a program and set it as default
    if (userPrograms.length > 0) {
        const selectedProgram = userPrograms[0]; // Get the first selected program
        const defaultCard = document.querySelector('[data-program="' + selectedProgram + '"]');

        if (defaultCard) {
            // Select the program automatically
            selectProgram(selectedProgram, defaultCard);

            // Update form title and label for existing selection
            const formTitle = document.getElementById('formTitle');
            const confirmLabel = document.getElementById('confirmLabel');
            const confirmBtn = document.getElementById('confirmProgramBtn');

            if (formTitle) {
                formTitle.textContent = 'Your Current Program Selection';
            }

            if (confirmLabel) {
                confirmLabel.textContent = 'I confirm that I want to keep my current program selection and am ready to proceed with my fundraising journey.';
            }

            if (confirmBtn) {
                confirmBtn.textContent = 'Keep Selection';
            }

            // Check the confirmation checkbox since user already has a program
            if (confirmSelection) {
                confirmSelection.checked = true;
                confirmProgramBtn.disabled = false;
            }
        }
    }

    // Add click event listeners to program cards
    programCards.forEach(card => {
        const selectBtn = card.querySelector('.program-select-btn');
        const programType = card.dataset.program;

        selectBtn.addEventListener('click', function() {
            selectProgram(programType, card);
        });
    });

    // Handle confirmation checkbox
    if (confirmSelection) {
        confirmSelection.addEventListener('change', function() {
            confirmProgramBtn.disabled = !this.checked;
        });
    }

    // Handle form submission
    const form = document.getElementById('confirmProgramForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('confirmProgramBtn');
            const formData = new FormData(form);

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';

            fetch('/selected-program', {
                method: "POST",
                body: formData,
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').getAttribute("content"),
                },
            })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    form.reset();
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<span class="d-inline-flex align-items-center"><span class="fas fa-check-circle text-success me-1" role="status" aria-hidden="true" style="font-size: 1em; line-height: 1;"></span>Selected</span>';

                    createSlider("success", data.message);
                    setTimeout(() => {
                        window.location.href = data.redirectUrl;
                    }, 2000);
                } else {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = "Continue";
                    submitBtn.style.backgroundColor = "#f05522";

                    if (data.errors) {
                        handleValidationErrors(data.errors);
                    }

                    createSlider("error", data.message, { title: "Error" });
                }
            })
            .catch((error) => {
                submitBtn.style.backgroundColor = "#f05522";
                submitBtn.disabled = false;
                submitBtn.innerHTML = "Continue";
                console.error("Error:", error);
                createSlider("error", "An error occurred. Please try again later.", { title: "Error" });
            });
        });
    }
}

// Main initialization
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Get slug from URL
        const slug = window.location.pathname.split('/').pop();

        // Fetch data from API
        const response = await fetch('/api/program-selection/' + slug);
        const result = await response.json();

        if (!result.success) {
            document.getElementById('program-selection-app').innerHTML = '<div class="container py-5"><div class="alert alert-danger text-center"><h4>Error</h4><p>' + result.message + '</p></div></div>';
            return;
        }

        // Render the page with the fetched data
        renderPage(result.data);

    } catch (error) {
        console.error('Error loading program selection:', error);
        document.getElementById('program-selection-app').innerHTML = '<div class="container py-5"><div class="alert alert-danger text-center"><h4>Error</h4><p>Failed to load program selection. Please try again later.</p></div></div>';
    }
});
