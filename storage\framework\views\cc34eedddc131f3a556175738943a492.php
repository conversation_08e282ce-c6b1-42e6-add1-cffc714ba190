<?php $__env->startSection('title', 'Login'); ?>
<?php $__env->startSection('content'); ?>

<section class="hero hero--title">
    <div class="container-fluid gx-0">
        <div class="bg-blue text-center text-uppercase">
            <h1 class="mb-0">Login</h1>
        </div>
    </div>
</section>
<section class="sec content-row">
    <div class="container">
        <div class="copy text-center">
            <p>
                Welcome back to the A Shot for Life Gauntlet!<br class="d-none d-md-block" />
                Continue your journey in the fight against cancer.
            </p>
        </div>
    </div>
</section>
<section class="form">
    <div class="container">
        <form class="row login-form gx-lg-5" id="loginForm"  action="<?php echo e(route('login.user')); ?>"  method="POST">
            <?php echo csrf_field(); ?>
            <div class="col-md-12 mb-4">
                <label class="form-label" for="email">Email</label>
                <input class="form-control" name="email" type="email" id="email" required />
                <?php $__errorArgs = ['email', 'default'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            <div class="col-md-12 mb-4 password-container">
                <label class="form-label" for="password">Password</label>
                <input type="password" class="form-control" id="password" name="password"
                    required placeholder="Password">
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <button type="button" class="password-toggle"
                    aria-label="Toggle password visibility">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                        fill="currentColor" class="bi bi-eye-slash" viewBox="0 0 16 16">
                        <path
                            d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7 7 0 0 0-2.79.588l.77.771A6 6 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755q-.247.248-.517.486z" />
                        <path
                            d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829" />
                        <path
                            d="M3.35 5.47q-.27.24-.518.487A13 13 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7 7 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12z" />
                    </svg>
                </button>
            </div>
            <div class="col-md-12 mb-4 d-flex justify-content-between align-items-center">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Remember me</label>
                </div>
                <a href="#" class="text-decoration-none">Forgot Password?</a>
            </div>
            <div class="col-md-12 my-4 text-center">
                <button class="cta orange border-0 hoverbutton" id="login" type="submit"><span>Login</span></button>
            </div>
            <div class="col-md-12 text-center">
                <p>Don't have an account? <a href="<?php echo e(route('register')); ?>" class="text-decoration-none fw-bold">Register Now</a></p>
            </div>
        </form>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<?php echo app('Illuminate\Foundation\Vite')('resources/css/pages/login.css'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
  <?php echo app('Illuminate\Foundation\Vite')('resources/js/pages/login.js'); ?>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/Auth/login.blade.php ENDPATH**/ ?>