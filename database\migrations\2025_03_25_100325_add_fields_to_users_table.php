<?php


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('name')->index()->change();
            $table->string('address')->after('email');
            $table->string('city')->index()->after('address');
            $table->string('state')->after('city');
            $table->string('fund_raise_message')->after('state');
            $table->string('profile_photo')->nullable()->after('fund_raise_message');

            $table->boolean('commit_fundraising')->default(false)->after('profile_photo');
            $table->boolean('use_photo_videos')->default(false)->after('commit_fundraising');
            $table->boolean('liability_waiver')->default(false)->after('use_photo_videos');
            $table->decimal('fundraising_goal', 10, 2)->default(0)->after('liability_waiver');

            $table->string('slug')->unique()->after('name');
            $table->string('phone_number', 10)->nullable()->after('email');


            $table->timestamp('last_login_at')->nullable()->after('phone_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'address',
                'city',
                'state',
                'fund_raise_message',
                'profile_photo',
                'commit_fundraising',
                'use_photo_videos',
                'liability_waiver',
                'fundraising_goal',
                'slug',
                'phone_number',
                'last_login_at',
            ]);
        });
    }
};
