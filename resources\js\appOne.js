import $ from 'jquery';

$(function () {
	domManipulate();
	navCall();
});

$(window).on('resize', function () {
	if ($(window).innerWidth() > 991) {
		$('#navCall').removeClass('is-active');
		$('#siteNav').addClass('d-none');
		$('#siteNav .nav').removeClass('flex-column text-center');
	}
});

function domManipulate() {
	if ($('.cta').length > 0) {
		$('.cta').addClass('d-inline-flex text-center text-uppercase justify-content-center text-decoration-none py-2');
	}
}

function navCall() {
	$('#navCall').on('click', function () {
		$(this).toggleClass('is-active');
		$('#siteNav').toggleClass('d-none');
		$('#siteNav .nav').toggleClass('flex-column text-center');
	});
}
