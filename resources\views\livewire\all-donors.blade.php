<section class="sec recent-activities">
    <div class="container">
        <div class="head text-center mb-5 mb-md-0">
            <h2 class="text-uppercase">Recent Activity</h2>
        </div>

        @if($recentDonors->count() > 0)
            <div class="row activity-head text-uppercase d-none d-md-flex mt-5">
                <div class="col-md-3 donor ps-5">Donor</div>
                <div class="col-md-3 athlete">Athlete</div>
                <div class="col-md-2 amount">Amount</div>
                <div class="col-md-4 comment pe-md-5">Comment</div>
            </div>

            <div wire:loading.class="opacity-50" class="activities-container">
                @foreach($recentDonors as $donor)
                    <div class="row activity-row d-flex align-items-center mt-2 donor-row">
                        <div class="col-md-3 label py-2 py-md-3 donor ps-md-5" title="Donor">{{ $donor['name'] }}</div>
                        <div class="col-md-3 label py-2 py-md-3 athlete" title="Athlete">{{ $donor['donated_to'] }}</div>
                        <div class="col-md-2 label py-2 py-md-3 amount" title="Amount">${{ number_format($donor['amount'], 2) }}</div>
                        <div class="col-md-4 label py-2 py-md-3 comment pe-md-5" title="Comment">{{ $donor['message'] ?? '' }}</div>
                    </div>
                @endforeach
            </div>

            <div class="cta-row mt-5 text-center">
                @if($recentDonors->previousPageUrl())
                    <button class="cta orange" wire:click.prevent="previousPage" wire:loading.attr="disabled">
                        <img src="images/arrow-cta-left.svg" alt="Previous Page" />
                    </button>
                @else
                    <button class="cta orange disabled">
                        <img src="images/arrow-cta-left.svg" alt="No Previous Page" />
                    </button>
                @endif

                @if($recentDonors->nextPageUrl())
                    <button class="cta orange" wire:click.prevent="nextPage" wire:loading.attr="disabled">
                        <img src="images/arrow-cta-right.svg" alt="Next Page" />
                    </button>
                @else
                    <button class="cta orange disabled">
                        <img src="images/arrow-cta-right.svg" alt="No Next Page" />
                    </button>
                @endif
            </div>
        @else
            <div class="row justify-content-center">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-hand-holding-heart fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">No donation activity found.</p>
                </div>
            </div>
        @endif
    </div>
</section>





{{-- <section class="recent-donors py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-4 fw-bold" style="color: #6a11cb; position: relative;">
            Recent Donors
            <span class="position-absolute w-25 h-1 bg-primary rounded"
                style="bottom: -10px; left: 50%; transform: translateX(-50%); height: 3px; background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%) !important;"></span>
        </h2>

        <div class="row mb-4">
            <div class="col-md-6 col-lg-4 mx-auto">

                <div class="search-wrapper position-relative">
                    <i class="fas fa-search position-absolute" style="left: 15px; top: 50%; transform: translateY(-50%); color: #6a11cb;"></i>
                    <input type="text" wire:model.live="search" placeholder="Search by name"
                        class="form-control py-2 ps-5 rounded-pill border-0"
                        style="box-shadow: 0 2px 10px rgba(106, 17, 203, 0.1);">
                </div>

            </div>
        </div>

        <div class="card border-0 rounded-lg shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);">
                            <tr>
                                <th class="py-3 text-white fw-normal border-0">Name</th>
                                <th class="py-3 text-white fw-normal border-0">Amount</th>
                                <th class="py-3 text-white fw-normal border-0">Date</th>
                            </tr>
                        </thead>
                        <tbody>

                            @forelse ($recentDonors as $donor)
                                <tr class="align-middle" style="transition: all 0.2s ease;">
                                    <td class="border-0 py-3">
                                        <div class="d-flex align-items-center">
                                            <span class="fw-medium text-dark">{{ $donor['name'] }}</span>
                                        </div>
                                    </td>
                                    <td class="border-0 py-3 fw-bold text-success">
                                        ${{ number_format($donor['amount'], 2) }}</td>
                                    <td class="border-0 py-3 text-muted">
                                        {{ \Carbon\Carbon::parse($donor['created_at'])->format('F d, Y') }}</td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-4 text-muted border-0">
                                        <div class="py-3">
                                            <i class="fas fa-search fa-2x mb-3 text-light"></i>
                                            <p class="mb-0">No donors found.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <div class="mt-4 d-flex justify-content-center">
            {{ $recentDonors->links('pagination::bootstrap-5') }}
        </div>

    </div>
</section> --}}
