<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class HandleStripePaymentFailed implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public $invoice;
    public function __construct($invoice)
    {
        $this->invoice = $invoice;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::table('subscriptions')
            ->where('subscription_id', $this->invoice->subscription)
            ->update(['status' => 'past_due']);
    }
}
