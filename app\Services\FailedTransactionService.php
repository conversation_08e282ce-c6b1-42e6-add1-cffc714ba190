<?php

namespace App\Services;

use App\Models\FailedTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\Exception\AuthenticationException;
use Stripe\Exception\ApiConnectionException;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\RateLimitException;
use Exception;

class FailedTransactionService
{
    /**
     * Record a failed payment intent creation
     */
    public function recordIntentCreationFailure(
        Request $request,
        Exception $exception,
        array $additionalData = []
    ): FailedTransaction {
        $failureData = $this->extractFailureData($exception);

        return FailedTransaction::create([
            'payment_intent_id' => null, // Intent was never created
            'amount' => $request->amount ?? null,
            'currency' => 'usd',
            'failure_stage' => 'intent_creation',
            'failure_code' => $failureData['code'],
            'failure_message' => $failureData['message'],
            'failure_reason' => $failureData['reason'],
            'failure_description' => $failureData['description'],
            'customer_email' => $request->email ?? null,
            'customer_name' => $this->getCustomerName($request),
            'donated_to_slug' => $request->donatedTo ?? null,
            'donation_type' => $request->paymentFrequency ?? 'one-time',
            'donation_message' => $request->message ?? null,
            'error_details' => $this->getErrorDetails($exception),
            'request_data' => $this->sanitizeRequestData($request->all()),
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Record a failed payment processing
     */
    public function recordPaymentProcessingFailure(
        string $paymentIntentId,
        Request $request,
        Exception $exception,
        array $additionalData = []
    ): FailedTransaction {
        $failureData = $this->extractFailureData($exception);

        return FailedTransaction::create([
            'payment_intent_id' => $paymentIntentId,
            'amount' => $request->amount ?? null,
            'currency' => 'usd',
            'failure_stage' => 'payment_processing',
            'failure_code' => $failureData['code'],
            'failure_message' => $failureData['message'],
            'failure_reason' => $failureData['reason'],
            'failure_description' => $failureData['description'],
            'customer_email' => $request->email ?? null,
            'customer_name' => $this->getCustomerName($request),
            'donated_to_slug' => $request->donatedTo ?? null,
            'donation_type' => $request->paymentFrequency ?? 'one-time',
            'donation_message' => $request->message ?? null,
            'error_details' => $this->getErrorDetails($exception),
            'request_data' => $this->sanitizeRequestData($request->all()),
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Record a failed subscription creation
     */
    public function recordSubscriptionCreationFailure(
        Request $request,
        Exception $exception,
        array $additionalData = []
    ): FailedTransaction {
        $failureData = $this->extractFailureData($exception);

        return FailedTransaction::create([
            'payment_intent_id' => $additionalData['payment_intent_id'] ?? null,
            'customer_id' => $additionalData['customer_id'] ?? null,
            'amount' => $request->amount ?? null,
            'currency' => 'usd',
            'failure_stage' => 'subscription_creation',
            'failure_code' => $failureData['code'],
            'failure_message' => $failureData['message'],
            'failure_reason' => $failureData['reason'],
            'failure_description' => $failureData['description'],
            'customer_email' => $request->email ?? null,
            'customer_name' => $this->getCustomerName($request),
            'donation_type' => $request->paymentFrequency ?? 'subscription',
            'error_details' => $this->getErrorDetails($exception),
            'request_data' => $this->sanitizeRequestData($request->all()),
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Record a failed database storage
     */
    public function recordDatabaseStorageFailure(
        string $paymentIntentId,
        Request $request,
        Exception $exception,
        array $additionalData = []
    ): FailedTransaction {
        $failureData = $this->extractFailureData($exception);

        return FailedTransaction::create([
            'payment_intent_id' => $paymentIntentId,
            'amount' => $request->amount ?? null,
            'currency' => 'usd',
            'failure_stage' => 'database_storage',
            'failure_code' => $failureData['code'],
            'failure_message' => $failureData['message'],
            'failure_reason' => $failureData['reason'],
            'failure_description' => $failureData['description'],
            'customer_email' => $request->email ?? null,
            'customer_name' => $this->getCustomerName($request),
            'donated_to_slug' => $request->donatedTo ?? null,
            'donation_type' => $request->paymentFrequency ?? 'one-time',
            'donation_message' => $request->message ?? null,
            'error_details' => $this->getErrorDetails($exception),
            'request_data' => $this->sanitizeRequestData($request->all()),
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Record a webhook processing failure
     */
    public function recordWebhookProcessingFailure(
        array $webhookData,
        Exception $exception,
        array $additionalData = []
    ): FailedTransaction {
        $failureData = $this->extractFailureData($exception);

        return FailedTransaction::create([
            'payment_intent_id' => $webhookData['payment_intent_id'] ?? null,
            'customer_id' => $webhookData['customer_id'] ?? null,
            'subscription_id' => $webhookData['subscription_id'] ?? null,
            'amount' => $webhookData['amount'] ?? null,
            'amount_charged' => $webhookData['amount_charged'] ?? null,
            'currency' => 'usd',
            'failure_stage' => 'webhook_processing',
            'failure_code' => $failureData['code'],
            'failure_message' => $failureData['message'],
            'failure_reason' => $failureData['reason'],
            'failure_description' => $failureData['description'],
            'error_details' => $this->getErrorDetails($exception),
            'request_data' => $webhookData,
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Record a Stripe webhook payment failure
     */
    public function recordStripePaymentFailure(
        array $stripeEvent,
        array $additionalData = []
    ): FailedTransaction {
        $paymentIntent = $stripeEvent['data']['object'];

        return FailedTransaction::create([
            'payment_intent_id' => $paymentIntent['id'] ?? null,
            'customer_id' => $paymentIntent['customer'] ?? null,
            'amount' => $this->convertStripeAmount($paymentIntent['amount'] ?? 0),
            'amount_charged' => $this->convertStripeAmount($paymentIntent['amount_received'] ?? 0),
            'currency' => $paymentIntent['currency'] ?? 'usd',
            'failure_stage' => 'payment_processing',
            'failure_code' => $paymentIntent['last_payment_error']['code'] ?? null,
            'failure_message' => $paymentIntent['last_payment_error']['message'] ?? null,
            'failure_reason' => $this->categorizeStripeFailure($paymentIntent['last_payment_error']['code'] ?? null),
            'failure_description' => $paymentIntent['last_payment_error']['decline_code'] ?? null,
            'payment_method_id' => $paymentIntent['payment_method'] ?? null,
            'payment_method_type' => $paymentIntent['payment_method_types'][0] ?? null,
            'customer_email' => $paymentIntent['receipt_email'] ?? null,
            'error_details' => $paymentIntent,
            'request_data' => $stripeEvent,
            'status' => 'failed',
            'failed_at' => now(),
            'metadata' => $additionalData,
        ]);
    }

    /**
     * Extract failure data from exception
     */
    private function extractFailureData(Exception $exception): array
    {
        $code = 'unknown';
        $message = $exception->getMessage();
        $reason = 'unknown';
        $description = '';

        if ($exception instanceof CardException) {
            $code = $exception->getStripeCode();
            $reason = $this->categorizeStripeFailure($code);
            $description = $exception->getDeclineCode();
        } elseif ($exception instanceof InvalidRequestException) {
            $code = 'invalid_request';
            $reason = 'invalid_request';
            $description = 'Invalid request parameters';
        } elseif ($exception instanceof AuthenticationException) {
            $code = 'authentication_failed';
            $reason = 'authentication_failed';
            $description = 'Stripe authentication failed';
        } elseif ($exception instanceof ApiConnectionException) {
            $code = 'api_connection_error';
            $reason = 'network_error';
            $description = 'Network connectivity issue with Stripe';
        } elseif ($exception instanceof RateLimitException) {
            $code = 'rate_limit_exceeded';
            $reason = 'rate_limit';
            $description = 'Too many requests to Stripe API';
        } elseif ($exception instanceof ApiErrorException) {
            $code = $exception->getStripeCode() ?? 'api_error';
            $reason = 'processing_error';
            $description = 'Stripe API error';
        }

        return [
            'code' => $code,
            'message' => $message,
            'reason' => $reason,
            'description' => $description,
        ];
    }

    /**
     * Categorize Stripe failure codes
     */
    private function categorizeStripeFailure(?string $code): string
    {
        if (!$code) return 'unknown';

        $categories = [
            'card_declined' => 'card_declined',
            'insufficient_funds' => 'insufficient_funds',
            'expired_card' => 'expired_card',
            'incorrect_cvc' => 'incorrect_cvc',
            'processing_error' => 'processing_error',
            'invalid_request' => 'invalid_request',
            'authentication_required' => 'authentication_required',
            'rate_limit_exceeded' => 'rate_limit',
            'invalid_payment_method' => 'invalid_payment_method',
            'card_not_supported' => 'card_not_supported',
            'currency_not_supported' => 'currency_not_supported',
            'duplicate_transaction' => 'duplicate_transaction',
            'fraudulent' => 'fraudulent',
            'network_error' => 'network_error',
            'timeout' => 'timeout',
            'server_error' => 'server_error',
        ];

        return $categories[$code] ?? 'unknown';
    }

    /**
     * Get detailed error information
     */
    private function getErrorDetails(Exception $exception): array
    {
        return [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'stripe_code' => method_exists($exception, 'getStripeCode') ? $exception->getStripeCode() : null,
            'decline_code' => method_exists($exception, 'getDeclineCode') ? $exception->getDeclineCode() : null,
        ];
    }

    /**
     * Sanitize request data for storage
     */
    private function sanitizeRequestData(array $data): array
    {
        // Remove sensitive information
        $sensitiveKeys = ['password', 'password_confirmation', 'token', 'stripeToken', 'stripeTokenType'];

        foreach ($sensitiveKeys as $key) {
            unset($data[$key]);
        }

        return $data;
    }

    /**
     * Get customer name from request
     */
    private function getCustomerName(Request $request): ?string
    {
        $firstName = $request->firstName ?? '';
        $lastName = $request->lastName ?? '';

        return trim($firstName . ' ' . $lastName) ?: null;
    }

    /**
     * Convert Stripe amount from cents to dollars
     */
    private function convertStripeAmount(int $amount): float
    {
        return $amount / 100;
    }

    /**
     * Get failed transactions that need attention
     */
    public function getFailedTransactionsNeedingAttention(): \Illuminate\Database\Eloquent\Collection
    {
        return FailedTransaction::unresolved()
            ->where('failed_at', '<=', now()->subHours(1))
            ->orderBy('failed_at', 'desc')
            ->get();
    }

    /**
     * Get failed transactions by stage
     */
    public function getFailedTransactionsByStage(string $stage): \Illuminate\Database\Eloquent\Collection
    {
        return FailedTransaction::byStage($stage)
            ->unresolved()
            ->orderBy('failed_at', 'desc')
            ->get();
    }

    /**
     * Get failed transactions with charged amounts
     */
    public function getFailedTransactionsWithCharges(): \Illuminate\Database\Eloquent\Collection
    {
        return FailedTransaction::where('amount_charged', '>', 0)
            ->unresolved()
            ->orderBy('failed_at', 'desc')
            ->get();
    }
}
