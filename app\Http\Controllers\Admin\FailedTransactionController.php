<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FailedTransaction;
use App\Services\FailedTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class FailedTransactionController extends Controller
{
    protected $failedTransactionService;

    public function __construct(FailedTransactionService $failedTransactionService)
    {
        $this->failedTransactionService = $failedTransactionService;
    }

    /**
     * Display failed transactions dashboard
     */
    public function index(Request $request)
    {
        $query = FailedTransaction::query();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('failure_stage')) {
            $query->where('failure_stage', $request->failure_stage);
        }

        if ($request->filled('failure_code')) {
            $query->where('failure_code', $request->failure_code);
        }

        if ($request->filled('customer_email')) {
            $query->where('customer_email', 'like', '%' . $request->customer_email . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('failed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('failed_at', '<=', $request->date_to . ' 23:59:59');
        }

        if ($request->filled('has_charges')) {
            if ($request->has_charges === 'yes') {
                $query->where('amount_charged', '>', 0);
            } else {
                $query->where('amount_charged', '=', 0);
            }
        }

        // Get statistics
        $stats = [
            'total_failed' => FailedTransaction::count(),
            'unresolved' => FailedTransaction::unresolved()->count(),
            'with_charges' => FailedTransaction::where('amount_charged', '>', 0)->count(),
            'retryable' => FailedTransaction::retryable()->count(),
            'by_stage' => FailedTransaction::selectRaw('failure_stage, count(*) as count')
                ->groupBy('failure_stage')
                ->pluck('count', 'failure_stage'),
            'by_status' => FailedTransaction::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
        ];

        $failedTransactions = $query->with('user')
            ->orderBy('failed_at', 'desc')
            ->paginate(20);

        return view('admin.failed-transactions.index', compact('failedTransactions', 'stats'));
    }

    /**
     * Show failed transaction details
     */
    public function show($id)
    {
        $failedTransaction = FailedTransaction::with('user')->findOrFail($id);

        return view('admin.failed-transactions.show', compact('failedTransaction'));
    }

    /**
     * Mark failed transaction as resolved
     */
    public function resolve(Request $request, $id)
    {
        $request->validate([
            'resolution_action' => 'required|string|in:refund,manual_credit,abandoned,retry_successful',
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        $failedTransaction = FailedTransaction::findOrFail($id);

        try {
            $failedTransaction->markAsResolved(
                Auth::user()->name,
                $request->resolution_action,
                $request->resolution_notes
            );

            Log::info('Failed transaction resolved', [
                'failed_transaction_id' => $id,
                'resolved_by' => Auth::user()->name,
                'action' => $request->resolution_action,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Failed transaction marked as resolved.',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to resolve transaction: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to resolve transaction.',
            ], 500);
        }
    }

    /**
     * Mark failed transaction as abandoned
     */
    public function abandon(Request $request, $id)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        $failedTransaction = FailedTransaction::findOrFail($id);

        try {
            $failedTransaction->markAsAbandoned(
                Auth::user()->name,
                $request->resolution_notes
            );

            Log::info('Failed transaction abandoned', [
                'failed_transaction_id' => $id,
                'abandoned_by' => Auth::user()->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Failed transaction marked as abandoned.',
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to abandon transaction: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to abandon transaction.',
            ], 500);
        }
    }

    /**
     * Retry failed transaction
     */
    public function retry($id)
    {
        $failedTransaction = FailedTransaction::findOrFail($id);

        if (!$failedTransaction->canBeRetried()) {
            return response()->json([
                'success' => false,
                'message' => 'This transaction cannot be retried.',
            ], 400);
        }

        try {
            $failedTransaction->markAsRetryPending();

            // Add to processing log
            $failedTransaction->addLogEntry(
                'admin_retry',
                'Admin initiated retry',
                ['admin_user' => Auth::user()->name]
            );

            Log::info('Failed transaction retry initiated', [
                'failed_transaction_id' => $id,
                'initiated_by' => Auth::user()->name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Retry initiated successfully.',
                'next_retry_at' => $failedTransaction->next_retry_at,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to initiate retry: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate retry.',
            ], 500);
        }
    }

    /**
     * Get failed transactions for API
     */
    public function getFailedTransactions(Request $request)
    {
        $query = FailedTransaction::query();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('failure_stage')) {
            $query->where('failure_stage', $request->failure_stage);
        }

        if ($request->filled('customer_email')) {
            $query->where('customer_email', 'like', '%' . $request->customer_email . '%');
        }

        $failedTransactions = $query->with('user')
            ->orderBy('failed_at', 'desc')
            ->paginate($request->get('per_page', 20));

        return response()->json($failedTransactions);
    }

    /**
     * Get failed transaction statistics
     */
    public function getStatistics()
    {
        $stats = [
            'total_failed' => FailedTransaction::count(),
            'unresolved' => FailedTransaction::unresolved()->count(),
            'with_charges' => FailedTransaction::where('amount_charged', '>', 0)->count(),
            'retryable' => FailedTransaction::retryable()->count(),
            'by_stage' => FailedTransaction::selectRaw('failure_stage, count(*) as count')
                ->groupBy('failure_stage')
                ->pluck('count', 'failure_stage'),
            'by_status' => FailedTransaction::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'recent_failures' => FailedTransaction::where('failed_at', '>=', now()->subDays(7))
                ->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Export failed transactions
     */
    public function export(Request $request)
    {
        $query = FailedTransaction::query();

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('failure_stage')) {
            $query->where('failure_stage', $request->failure_stage);
        }

        if ($request->filled('date_from')) {
            $query->where('failed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('failed_at', '<=', $request->date_to . ' 23:59:59');
        }

        $failedTransactions = $query->with('user')->get();

        $filename = 'failed_transactions_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($failedTransactions) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Payment Intent ID',
                'Customer Email',
                'Customer Name',
                'Amount',
                'Amount Charged',
                'Failure Stage',
                'Failure Code',
                'Failure Message',
                'Status',
                'Failed At',
                'Retry Count',
                'Donation Type',
                'Donated To',
            ]);

            foreach ($failedTransactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->payment_intent_id,
                    $transaction->customer_email,
                    $transaction->customer_name,
                    $transaction->amount,
                    $transaction->amount_charged,
                    $transaction->failure_stage,
                    $transaction->failure_code,
                    $transaction->failure_message,
                    $transaction->status,
                    $transaction->failed_at,
                    $transaction->retry_count,
                    $transaction->donation_type,
                    $transaction->donated_to_slug,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
