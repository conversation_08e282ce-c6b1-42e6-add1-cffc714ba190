<?php

namespace App\Services;

use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Role;
use App\Models\SubscriberForLatestEvents;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;


class UserService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }


    public function storeUser(Request $request): User
    {

        try {
            $data = $request->validate(
                [
                    'name' => 'required|string|max:255',
                    'email' => 'required|email|unique:users,email',
                    'password' => 'required|min:8|',
                    'fund_raise_message' => 'nullable|string',
                    'file' => 'required|image|mimes:jpeg,png,jpg,webp|max:5000',
                    'address' => 'required|string|max:255',
                    'city' => 'required|string|max:255',
                    'state' => 'required|string|max:255',
                    'fund_raise_goal' => 'required|numeric|min:1000',
                    'commit_fundraising' => 'required|in:on',
                    'use_photo_videos' => 'required|in:on',
                    'liability_waiver' => 'required|in:on',
                ],
                [
                    'fund_raise_goal.min' => 'The fundraise goal must be at least $1000.',
                ]
            );
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        if ($request->hasFile('file')) {
            $data['file_path'] = $request->file('file')->store('uploads', 'public');
        } else {
            $data['file_path'] = 'uploads/default.png';
        }

        $data['commit_fundraising'] = $request->has('commit_fundraising') ? 1 : 0;
        $data['use_photo_videos'] = $request->has('use_photo_videos') ? 1 : 0;
        $data['liability_waiver'] = $request->has('liability_waiver') ? 1 : 0;

        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'fund_raise_message' => $data['fund_raise_message'] ?? null,
            'address' => $data['address'],
            'city' => $data['city'],
            'state' => $data['state'],
            'fundraising_goal' => $data['fund_raise_goal'],
            'commit_fundraising' => $data['commit_fundraising'],
            'use_photo_videos' => $data['use_photo_videos'],
            'liability_waiver' => $data['liability_waiver'],
            'profile_photo' => $data['file_path'] ?? null,

        ]);
        $subscriber = SubscriberForLatestEvents::where('email', $user->email)->first();

        if ($subscriber) {
            if (!$subscriber->active) {
                $subscriber->update(['active' => true]);
            }
        } else {
            SubscriberForLatestEvents::create([
                'email' => $user->email,
                'active' => true,
            ]);
        }



        $user->roles()->attach(Role::where('name', 'user')->first());



        return $user;
    }



    public function editUserProfile(Request $request): User
    {



        $user = auth()->user();

        try {
            $data = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'fund_raise_message' => 'nullable|string',
                'file' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5000',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'fundraising_goal' => 'required|numeric|min:1000',
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        if ($request->hasFile('file')) {
            if ($user->profile_photo && $user->profile_photo !== 'uploads/default.png') {
                Storage::disk('public')->delete($user->profile_photo);
            }

            $data['profile_photo'] = $request->file('file')->store('uploads', 'public');
        }

        $user->update($data);

        return $user;
    }




    public function updateUserPassword(Request $request): User
    {
        $user = auth()->user();
        try {
            $validated = $request->validate([
                'current_password' => ['required', 'current_password'],
                'new_password' => ['required', 'string', 'min:8', 'confirmed'],
            ]);
        } catch (ValidationException $e) {
            throw new ValidationException($e->validator, response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422));
        }

        $user->update([
            'password' => Hash::make($validated['new_password']),
        ]);

        return $user;
    }



    public function authenticateUser(Request $request)
    {

        $data = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $data['email'])->first();

        if (!$user) {
            return redirect()->back()->withErrors(['email' => 'The provided email does not match our records.'])->withInput();
        }

        if (!Hash::check($data['password'], $user->password)) {
            return redirect()->back()->withErrors(['password' => 'The provided password is incorrect.']);
        }


        $roles = $user->roles()->pluck('name')->toArray();

        Auth::login($user);
        Auth::user()->update(['last_login_at' => now()]);


        return redirect()->route(
            $user->hasRole('admin') ? 'admin.dashboard' : 'user.dashboard'
        )->with('success', 'Login Successful!');
    }

    public function getDashboardData(Request $request): array
    {
        $user = $request->user();

        if (!$user) {
            throw new \Exception('Unable to find user.');
        }

        $donationPageUrl = route('donationPage', ['slug' => $user->slug]);

        $topDonors = $user->donors()
            ->orderByDesc('amount')
            ->take(5)
            ->get()
            ->map(fn($donor) => [
                'name' => $donor->anonymous_for_all ? 'Anonymous' : $donor->name,
                'amount' => $donor->pivot->amount,
            ]);



        $totalDonors = $user->donors()->count();

        $totalAmount = $user->donors()->sum('amount');
        $averageAmount = ($totalAmount > 0) ? round($user->donors()->avg('amount'), 2) : 0;




        return [
            'donationPageUrl' => $donationPageUrl,
            'user' => $user,
            'topDonors' => $topDonors,
            'totalDonors' => $totalDonors,
            'totalAmount' => $totalAmount,
            'averageAmount' => $averageAmount,
        ];
    }


    public function getDonationPage(String $slug)
    {
        return User::findBySlug($slug)->first();
    }

    public function getAllPrograms()
    {

        return Program::all();
    }
}
