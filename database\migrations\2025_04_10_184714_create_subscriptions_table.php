<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
             $table->string('subscription_id')->unique();
            $table->string('customer_id');
            $table->string('payment_frequency');
            $table->decimal('amount', 10, 2);
            $table->string('currency')->default('usd');
            $table->timestamp('start_date')->nullable();
            $table->timestamp('next_payment_at')->nullable();
            $table->string('status')->default('active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
