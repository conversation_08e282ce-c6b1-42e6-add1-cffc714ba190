<?php

namespace App\Listeners;

use App\Events\SendReplyToMessage;
use App\Mail\ReplyToUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendReplyMail implements ShouldQueue
{

    use InteractsWithQueue;
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(SendReplyToMessage $event): void
    {
        Mail::to($event->email)->send(new ReplyToUser($event->reply));
    }
}
