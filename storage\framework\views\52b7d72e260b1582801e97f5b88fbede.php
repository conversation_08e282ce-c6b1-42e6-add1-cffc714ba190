
<section class="sec recent-activities">
    <div class="container">
        <div class="head text-center mb-5 mb-md-0">
            <h2 class="text-uppercase">Join our Community of Donors</h2>
            <h3>These generous people have already made a difference</h3>
        </div>
        <div class="search-athlete py-5 mx-auto" style="max-width: 330px">
            <h2 class="text-center">Search by Name</h2>
            <form class="search-wrap mt-2 mb-0" action="">
                <div class="search">
                    <input type="text" wire:model.live="search" />
                    <div class="icon d-flex align-items-center justify-content-center"><i class="bi bi-search"></i></div>
                </div>
            </form>
        </div>
        <div class="row activity-head text-uppercase d-none d-md-flex mt-5">
            <div class="col-md-3 donor ps-5">Donor</div>
            <div class="col-md-3 athlete">Date</div>
            <div class="col-md-2 amount">Amount</div>
            <div class="col-md comment pe-md-5">Comment</div>
        </div>

        <div wire:loading.class="opacity-50" class="activities-container">
            <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $recentDonors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="row activity-row d-flex align-items-center mt-2 donor-row">
                    <div class="col-md-3 label py-2 py-md-3 donor ps-md-5" title="Donor"><?php echo e($donor['name']); ?></div>
                    <div class="col-md-3 label py-2 py-md-3 athlete" title="Athlete"><?php echo e(\Carbon\Carbon::parse($donor['created_at'])->format('F j, Y')); ?></div>
                    <div class="col-md-2 label py-2 py-md-3 amount" title="Amount">$<?php echo e(number_format($donor['amount'], 2)); ?></div>
                    <div class="col-md-4 label py-2 py-md-3 comment pe-md-5" title="Comment"><?php echo e($donor['message_for_fundraiser'] ?? ''); ?></div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="row mt-4">
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-hand-holding-heart fa-3x mb-3 text-muted"></i>
                        <p class="mb-0">No donation activity found.</p>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <div class="cta-row mt-5 text-center">
            <!--[if BLOCK]><![endif]--><?php if($recentDonors->previousPageUrl()): ?>
                <button class="cta orange"  wire:click.prevent="previousPage" wire:loading.attr="disabled">
                    <img src="<?php echo e(asset('images/arrow-cta-left.svg')); ?>" alt="Previous Page" />
                </button>
            <?php else: ?>
                <button class="cta orange disabled">
                    <img src="<?php echo e(asset('images/arrow-cta-left.svg')); ?>" alt="No Previous Page" />
                </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!--[if BLOCK]><![endif]--><?php if($recentDonors->nextPageUrl()): ?>
                <button class="cta orange"  wire:click.prevent="nextPage" wire:loading.attr="disabled">
                    <img src="<?php echo e(asset('images/arrow-cta-right.svg')); ?>" alt="Next Page" />
                </button>
            <?php else: ?>
                <button class="cta orange disabled" >
                    <img src="<?php echo e(asset('images/arrow-cta-right.svg')); ?>" alt="No Next Page" />
                </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
</section>






<?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/livewire/donors-in-donation-page.blade.php ENDPATH**/ ?>