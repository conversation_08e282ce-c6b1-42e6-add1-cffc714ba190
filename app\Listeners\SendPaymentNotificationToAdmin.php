<?php

namespace App\Listeners;

use App\Events\PaymentRecieved;
use App\Models\User;
use App\Notifications\NewPaymentRecieved;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendPaymentNotificationToAdmin
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentRecieved $event): void
    {
        $admins = User::admins()->get();
        $donor = $event->donor;
        $amount = $event->amount;
        foreach ($admins as $admin) {
            $admin->notify(new NewPaymentRecieved($amount, $donor));
        }
    }
}
