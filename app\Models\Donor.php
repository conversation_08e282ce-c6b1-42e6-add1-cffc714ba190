<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Donor extends Model
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'address',
        'city',
        'state',
        'message_for_fundraiser',
        'amount_donate',
        'anonymous_for_public',
        'anonymous_for_all',
        'payment_frequency',
        'customer_id',
        'subscription_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount_donate' => 'decimal:2',
        'anonymous_for_public' => 'boolean',
        'anonymous_for_all' => 'boolean',
    ];

    /**
     * Get the users that received donations from this donor.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_donors')
            ->withPivot('amount')
            ->withTimestamps();
    }

    /**
     * Get the subscription associated with the donor.
     */
    public function subscription()
    {
        return $this->hasOne(\App\Models\Subscription::class, 'customer_id', 'customer_id');
    }
}
