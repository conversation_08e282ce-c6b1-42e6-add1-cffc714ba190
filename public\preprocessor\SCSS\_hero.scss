.hero {
	@media (min-width: 768px) {
		position: relative;
	}

	&__text {
		padding: 1.25rem 0;

		@media (min-width: 768px) {
			left: 0;
			padding: 2.5rem 0 0;
			position: absolute;
			top: 0;
			width: 100%;
		}
	}

	h1 {
		color: #000000;
		font: 46px/0.9 $anaheim;

		@media (min-width: 768px) {
			font-size: 46px;
		}

		@media (min-width: 1240px) {
			font-size: 70px;
		}

		strong {
			color: #f05522;
			font-size: 110%;
		}
	}

	p {
		color: #000;
		font-size: 18px;
		line-height: 1.5;

		@media (min-width: 768px) {
			font-size: 14px;
			text-wrap: balance;
		}

		@media (min-width: 1240px) {
			font-size: 20px;
			text-wrap: balance;
		}
	}

	.cta-row {
		margin-top: 1.25rem;

		@media (min-width: 1240px) {
			margin-top: 2.25rem;
		}
	}

	&--title {
		.bg-blue {
			background: #19345e;
			padding-block: 1.5rem;

			@media (min-width: 768px) {
				padding-block: 2.25rem;
			}
		}

		h1 {
			color: #fff;
			font: 500 42px/1 $anaheim;

			@media (min-width: 768px) {
				font-size: 52px;
			}
		}
	}
}

.hording {
	@media (min-width: 768px) {
		position: relative;
	}

	&__text {
		@media (min-width: 768px) {
			left: 0;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			width: 100%;
		}

		h2 {
			color: #19345e;

			strong {
				color: #f05522;
				font-size: 115%;
			}
		}

		p {
			color: #363636;
		}

		.p-size {
			max-width: 475px;
		}
	}
}
