<?php

use App\Http\Controllers\StripeWebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Jobs\HandleStripePaymentSucceeded;
use App\Jobs\HandleStripePaymentFailed;
use App\Jobs\HandleStripeSubscriptionCancelled;
use App\Jobs\HandleStripeSubscriptionUpdated;
use Illuminate\Support\Facades\Log;

Route::post('/webhook/stripe', [StripeWebhookController::class, 'handle']);
