<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DonationToken extends Model
{
    protected $fillable = [
        'token',
        'amount',
        'user_slug',
        'expires_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'amount' => 'decimal:2'
    ];

    /**
     * Generate a new donation token
     */
    public static function generateToken($amount, $userSlug, $expirationMinutes = 30)
    {
        $token = Str::random(32);

        return self::create([
            'token' => $token,
            'amount' => $amount,
            'user_slug' => $userSlug,
            'expires_at' => Carbon::now()->addMinutes($expirationMinutes)
        ]);
    }

    /**
     * Find a valid token
     */
    public static function findValidToken($token)
    {
        return self::where('token', $token)
            ->where('expires_at', '>', Carbon::now())
            ->first();
    }

    /**
     * Clean up expired tokens
     */
    public static function cleanupExpired()
    {
        return self::where('expires_at', '<', Carbon::now())->delete();
    }

    /**
     * Check if token is expired
     */
    public function isExpired()
    {
        return $this->expires_at < Carbon::now();
    }

    /**
     * Get the user associated with this token
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_slug', 'slug');
    }
}
