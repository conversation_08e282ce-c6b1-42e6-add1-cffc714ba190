- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet';
- let heroName = 'Leaderboard';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName} ${heroName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/heroText.pug
include ../layouts/leaderBox.pug
include ../partials/footer.pug

html(lang="en")
    +head(pageTitle)

    body
        +header()

        +heroText(heroName)

        section.sec.leaderboard.pt-3
            .container
                .row
                    - for (let i = 0; i < 2 ; i++)
                        +leaderBox('lbp1', '<PERSON>', 4750, 1000)
                        +leaderBox('lbp2', '<PERSON>', 4625, 2000)
                        +leaderBox('lbp3', '<PERSON>', 4125, 1500)
                        +leaderBox('lbp4', '<PERSON>', 3750, 3000)
                        +leaderBox('lbp5', '<PERSON><PERSON>', 3650, 1500)
                        +leaderBox('lbp6', '<PERSON>', 4625, 2000)
                        +leaderBox('lbp7', '<PERSON>', 4125, 1500)
                        +leaderBox('lbp8', '<PERSON>', 3750, 3000)

                    .cta-row.mt-5.text-center
                        a.cta.orange(href="#") PREVIOUS PAGE
                        a.cta.orange(href="#") NEXT PAGE

                    .total-page.text-center 1 of 37

        +footer()
