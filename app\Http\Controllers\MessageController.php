<?php

namespace App\Http\Controllers;

use App\Events\SendReplyToMessage;
use App\Http\Controllers\Controller;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class MessageController extends Controller
{
    public function index()
    {
        $messages = Message::orderBy('created_at', 'desc')->paginate(5);



        return view('Admin.messages', compact('messages'));
    }

    public function filter(Request $request)
    {
        $query = Message::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'read':
                    $query->where('is_read', 1);
                    break;
                case 'unread':
                    $query->where('is_read', 0);
                    break;
                case 'replied':
                    $query->where('is_replied', 1);
                    break;
                case 'non-replied':
                    $query->where('is_replied', 0);
                    break;
            }
        }


        $messages = $query->orderBy('created_at', 'desc')->paginate(5);

        return response()->json([
            'messages' => $messages->items(),
            'hasMorePages' => $messages->hasMorePages()
        ]);
    }

    public function markAsRead(Message $message)
    {
        if (!$message) {
            return response()->json(['success' => false, 'message' => 'Message not found'], 404);
        }
        $message->update(['is_read' => 1]);

        return response()->json(['success' => true]);
    }


    public function reply(Request $request, Message $message)
    {

        $request->validate([
            'email' => 'required|email',
            'reply' => 'required|string',
        ]);

        SendReplyToMessage::dispatch($request->email, $request->reply);


        $message->update(['is_replied' => 1]);

        return response()->json(['success' => true]);
    }

    public function markAllAsRead()
    {
        Message::where('is_read', 0)->update(['is_read' => 1]);

        return response()->json(['success' => true]);
    }
}
