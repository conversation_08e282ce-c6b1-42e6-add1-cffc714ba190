<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('donors', function (Blueprint $table) {
            $table->string('city')->nullable()->after('address');
            $table->string('state')->nullable()->after('city');
            $table->string('customer_id')->nullable()->after('state');
            $table->string('subscription_id')->nullable()->after('customer_id');
            $table->string('payment_frequency')->nullable()->after('subscription_id');
            $table->timestamp('next_payment_at')->nullable()->after('payment_frequency');
            $table->string('cancellation_token')->nullable()->after('next_payment_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('donors', function (Blueprint $table) {
            $table->dropColumn([
                'city',
                'state',
                'customer_id',
                'subscription_id',
                'payment_frequency',
                'next_payment_at',
                'cancellation_token',
            ]);
        });
    }
};
