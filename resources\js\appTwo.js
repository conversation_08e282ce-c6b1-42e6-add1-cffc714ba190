window.addEventListener("load", function () {
    setTimeout(() => {
        document.body.classList.remove("loading");

        const loader = document.getElementById("loader");
        if (loader) {
            loader.style.transition = "opacity 0.3s ease";
            loader.style.opacity = "0";

            setTimeout(() => {
                loader.remove();
            }, 200);
        }
    }, 200);
});

document.addEventListener("DOMContentLoaded", function () {
    initializeFooterNewsletter();
});

 export function createSlider(type, message, options = {}) {
    if (options.unique) {
        const existingToasts = document.querySelectorAll(".ultra-toast");
        existingToasts.forEach((toast) => dismissToast(toast));
    }

    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.id = "toast-container";
        toastContainer.className = "position-fixed bottom-0 end-0 p-3";
        toastContainer.style.zIndex = "1080";
        document.body.appendChild(toastContainer);
    }

    const toast = document.createElement("div");
    toast.className = "ultra-toast";
    toast.style.margin = "0.75rem 0";
    toast.style.transformOrigin = "right bottom";
    toast.style.borderRadius = "12px";
    toast.style.overflow = "hidden";
    toast.style.boxShadow =
        "0 15px 30px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1)";
    toast.style.width = "350px";
    toast.style.maxWidth = "95vw";
    toast.style.opacity = "0";
    toast.style.transform = "translateX(100%)";
    toast.style.transition = "all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)";
    toast.style.backdropFilter = "blur(10px)";
    toast.style.WebkitBackdropFilter = "blur(10px)";
    toast.style.borderLeft = "6px solid #fff";

    const typeConfig = {
        success: {
            gradient: "linear-gradient(145deg, #19345E, #1D4A82)",
            borderColor: "#F05522",
            icon: "fas fa-circle-check",
            iconColor: "#25ce64",
        },
        error: {
            gradient:
                "linear-gradient(145deg, rgba(220, 53, 69, 0.85), rgba(176, 42, 55, 0.95))",
            borderColor: "#ff5b75",
            icon: "fas fa-circle-xmark",
            iconColor: "#ff5b75",
        },
        warning: {
            gradient:
                "linear-gradient(145deg, rgba(255, 193, 7, 0.85), rgba(204, 154, 6, 0.95))",
            borderColor: "#ffd24d",
            icon: "fas fa-triangle-exclamation",
            iconColor: "#ffd24d",
        },
        info: {
            gradient:
                "linear-gradient(145deg, rgba(13, 110, 253, 0.85), rgba(10, 88, 202, 0.95))",
            borderColor: "#5e9eff",
            icon: "fas fa-circle-info",
            iconColor: "#5e9eff",
        },
        dark: {
            gradient:
                "linear-gradient(145deg, rgba(33, 37, 41, 0.85), rgba(18, 20, 22, 0.95))",
            borderColor: "#6c757d",
            icon: "fas fa-bell",
            iconColor: "#adb5bd",
        },
    };

    const config = typeConfig[type] || typeConfig.dark;
    toast.style.background = config.gradient;
    toast.style.borderLeftColor = config.borderColor;

    const inner = document.createElement("div");
    inner.className = "d-flex position-relative";
    inner.style.padding = "16px";

    const contentWrapper = document.createElement("div");
    contentWrapper.className = "toast-content d-flex";

    const iconContainer = document.createElement("div");
    iconContainer.className =
        "me-3 d-flex align-items-center justify-content-center";
    iconContainer.style.width = "32px";
    iconContainer.style.height = "32px";
    iconContainer.style.borderRadius = "50%";
    iconContainer.style.backgroundColor = "rgba(255, 255, 255, 0.15)";
    iconContainer.style.flexShrink = "0";

    const icon = document.createElement("i");
    icon.className = `fas fa-light ${config.icon}`;
    icon.style.color = config.iconColor;
    icon.style.fontSize = "1.2rem";
    iconContainer.appendChild(icon);

    const textContainer = document.createElement("div");
    textContainer.className = "d-flex flex-column justify-content-center";

    if (options.title) {
        const title = document.createElement("h6");
        title.textContent = options.title;
        title.className = "mb-1";
        title.style.color = "#ffffff";
        title.style.fontWeight = "600";
        title.style.fontSize = "1rem";
        title.style.marginTop = "0";
        title.style.marginBottom = "4px";
        textContainer.appendChild(title);
    }

    const messageEl = document.createElement("div");
    messageEl.textContent = message;
    messageEl.style.color = "rgba(255, 255, 255, 0.9)";
    messageEl.style.fontSize = "0.875rem";
    messageEl.style.lineHeight = "1.4";
    textContainer.appendChild(messageEl);

    const closeBtn = document.createElement("button");
    closeBtn.className = "position-absolute top-0 end-0 mt-2 me-2";
    closeBtn.style.backgroundColor = "transparent";
    closeBtn.style.border = "none";
    closeBtn.style.padding = "8px";
    closeBtn.style.display = "flex";
    closeBtn.style.alignItems = "center";
    closeBtn.style.justifyContent = "center";
    closeBtn.style.cursor = "pointer";
    closeBtn.style.color = "rgba(255, 255, 255, 0.7)";
    closeBtn.style.transition = "all 0.2s";
    closeBtn.innerHTML = '<i class="bi bi-x" style="font-size: 1.25rem;"></i>';
    closeBtn.ariaLabel = "Close";

    closeBtn.addEventListener("mouseover", () => {
        closeBtn.style.color = "rgba(255, 255, 255, 1)";
        closeBtn.style.transform = "scale(1.1)";
    });

    closeBtn.addEventListener("mouseout", () => {
        closeBtn.style.color = "rgba(255, 255, 255, 0.7)";
        closeBtn.style.transform = "scale(1)";
    });

    const progressContainer = document.createElement("div");
    progressContainer.className = "position-absolute bottom-0 start-0";
    progressContainer.style.height = "4px";
    progressContainer.style.width = "100%";
    progressContainer.style.backgroundColor = "rgba(255, 255, 255, 0.1)";

    const progressBar = document.createElement("div");
    progressBar.className = "progress-bar";
    progressBar.style.height = "100%";
    progressBar.style.width = "100%";
    progressBar.style.backgroundColor = config.borderColor;
    progressBar.style.transition = `width ${options.duration || 5000}ms linear`;
    progressContainer.appendChild(progressBar);

    contentWrapper.appendChild(iconContainer);
    contentWrapper.appendChild(textContainer);
    inner.appendChild(contentWrapper);
    inner.appendChild(closeBtn);
    toast.appendChild(inner);
    toast.appendChild(progressContainer);

    toastContainer.appendChild(toast);

    const animateIn = () => {
        requestAnimationFrame(() => {
            toast.style.transform = "translateX(0)";
            toast.style.opacity = "1";
        });
    };

    function dismissToast(toastElement) {
        toastElement.style.transform = "translateX(100%)";
        toastElement.style.opacity = "0";

        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }

            if (toastContainer.children.length === 0) {
                document.body.removeChild(toastContainer);
            }
        }, 500);
    }

    closeBtn.addEventListener("click", () => {
        clearTimeout(toast.autoCloseTimeout);
        dismissToast(toast);
    });

    toast.addEventListener("mouseenter", () => {
        progressBar.style.transition = "none";
        clearTimeout(toast.autoCloseTimeout);
    });

    toast.addEventListener("mouseleave", () => {
        const width = parseFloat(getComputedStyle(progressBar).width);
        const totalWidth = parseFloat(
            getComputedStyle(progressContainer).width
        );
        const remainingTime = (width / totalWidth) * (options.duration || 5000);

        progressBar.style.transition = `width ${remainingTime}ms linear`;
        progressBar.style.width = "0";

        toast.autoCloseTimeout = setTimeout(() => {
            dismissToast(toast);
        }, remainingTime);
    });

    toast.addEventListener("click", (e) => {
        if (e.target !== closeBtn && !closeBtn.contains(e.target)) {
            toast.style.transform = "scale(0.98)";
            setTimeout(() => {
                toast.style.transform = "scale(1)";
            }, 100);
        }
    });

    setTimeout(animateIn, 10);

    setTimeout(() => {
        progressBar.style.width = "0";
    }, 100);

    toast.autoCloseTimeout = setTimeout(() => {
        dismissToast(toast);
    }, options.duration || 5000);

    toast.addEventListener("mousedown", (e) => {
        if (e.target !== closeBtn && !closeBtn.contains(e.target)) {
            const ripple = document.createElement("div");
            ripple.className = "ripple";
            ripple.style.position = "absolute";
            ripple.style.width = "10px";
            ripple.style.height = "10px";
            ripple.style.backgroundColor = "rgba(255, 255, 255, 0.4)";
            ripple.style.borderRadius = "50%";
            ripple.style.transform = "scale(0)";
            ripple.style.animation = "ripple-effect 0.6s linear";
            ripple.style.pointerEvents = "none";

            const rect = toast.getBoundingClientRect();
            ripple.style.left = `${e.clientX - rect.left}px`;
            ripple.style.top = `${e.clientY - rect.top}px`;

            toast.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }
    });

    if (!document.getElementById("toast-styles")) {
        const style = document.createElement("style");
        style.id = "toast-styles";
        style.innerHTML = `
            @keyframes ripple-effect {
                0% {
                    transform: scale(0);
                    opacity: 1;
                }
                100% {
                    transform: scale(40);
                    opacity: 0;
                }
            }

            .ultra-toast {
                position: relative;
                overflow: hidden;
            }

            .ultra-toast:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 100%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.05) 100%);
                pointer-events: none;
            }
        `;
        document.head.appendChild(style);
    }

    return toast;
}

// Example usage:
// createToast("success", "Your changes have been saved successfully!", { title: "Success", duration: 5000 });
// createToast("error", "Unable to connect to the server. Please try again later.", { title: "Error" });
// createToast("warning", "Your session will expire in 5 minutes.", { title: "Warning" });
// createToast("info", "New features are available in this update.", { title: "Information" });
// createToast("dark", "This action cannot be undone.", { title: "Confirm Action" });
function initializeFooterNewsletter() {
    const newsletterFormInFooter = document.getElementById(
        "newsletterFormInFooter"
    );

    if (newsletterFormInFooter) {
        newsletterFormInFooter.addEventListener("submit", function (e) {
            e.preventDefault();

            const emailInput = this.querySelector('input[type="email"]');
            if (!emailInput) return;

            const email = emailInput.value.trim();
            if (!email) {
                return;
            }

            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                return;
            }

            let subscribeButtonInFooter = document.getElementById(
                "subscribeForNewsLetterBtnInFooter"
            );

            const subscribeUrl = route("subscribeForLatestEvents");

            subscribeButtonInFooter.disabled = true;
            subscribeButtonInFooter.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Subscribing...`;

            fetch(subscribeUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken.getAttribute("content"),
                },
                body: JSON.stringify({ email: email }),
            })
                .then((response) => {
                    if (!response.ok) {
                        subscribeButtonInFooter.disabled = false;
                        subscribeButtonInFooter.innerHTML = "Subscribe";
                    }
                    return response.json();
                })
                .then((data) => {
                    if (data.success) {
                        subscribeButtonInFooter.disabled = true;
                        subscribeButtonInFooter.innerHTML = `<i class="fas fa-check-circle text-success"></i> Subscribed!`;
                        emailInput.value = "";
                        setTimeout(() => {
                            subscribeButtonInFooter.disabled = false;
                            subscribeButtonInFooter.innerHTML = "Subscribe";
                        }, 3000);
                    } else {
                        subscribeButtonInFooter.disabled = true;
                        subscribeButtonInFooter.innerHTML = `<i class="fas fa-exclamation-circle text-warning"></i> Error!`;

                        setTimeout(() => {
                            subscribeButtonInFooter.disabled = false;
                            subscribeButtonInFooter.innerHTML = "Subscribe";
                        }, 3000);
                    }
                })
                .catch((error) => {
                    subscribeButtonInFooter.disabled = true;
                    subscribeButtonInFooter.innerHTML = `<i class="fas fa-exclamation-circle text-warning"></i> Error!`;

                    setTimeout(() => {
                        subscribeButtonInFooter.disabled = false;
                        subscribeButtonInFooter.innerHTML = "Subscribe";
                    }, 3000);
                });
        });
    }
}
