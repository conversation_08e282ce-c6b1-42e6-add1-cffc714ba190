mixin radioOptionPackage(label, idFor, name = 'package')
    - const id = idFor.toLowerCase().replace(/\s+/g, '')
    .check(class=(idFor === 'Champion' ? 'star' : ''))
        input(type="radio", name=name, value=id, id=id)
        label.d-flex.align-items-center.justify-content-center.flex-column.p-3(for=id) !{label}
        if idFor === 'Champion'
            .most-selected.d-flex.align-items-center.flex-column
                .icon
                    i.bi.bi-star-fill
                h2.text-uppercase Popular Choice
