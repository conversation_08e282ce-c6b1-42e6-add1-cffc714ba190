<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\File;


class UserFactory extends Factory
{
    protected static ?string $password = null;

    public function definition(): array
{
        $imageUrl = fake()->imageUrl(200, 200, 'people', true);

        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'fund_raise_message' => Str::random(15),
            'address' => fake()->address(),
            'state' => fake()->state(),
            'city' => fake()->city(),
            'commit_fundraising' => 1,
            'use_photo_videos' => 1,
             'profile_photo' => 'uploads/' . $imageUrl,
            'fundraising_goal' => fake()->numberBetween(1000, 1000000),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (User $user) {
            $role = Role::where('name', 'user')->first();
            if ($role instanceof Role) {
                $user->roles()->attach($role->id);
            }
        });
    }
}
