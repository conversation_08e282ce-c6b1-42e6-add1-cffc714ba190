.recent-activities {
	.activity-head {
		color: #19345e;
		font: 700 13px $inter;
	}

	.activity-row {
		background: #e4eaf4;
		border-radius: 30px;

		@media (min-width: 768px) {
			border-radius: 100px;
			min-height: 6rem;
		}

		.label {
			color: #154da3;
			font: 700 18px/1.25 $anaheim;

			@media (max-width: 767px) {
				padding-left: 30%;
				position: relative;

				&:after {
					color: #19345e;
					content: attr(title);
					font: 700 13px $inter;
					left: 0;
					padding-left: 1.25rem;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 30%;
				}
			}

			@media (min-width: 768px) {
				font-size: 22px;
			}
		}
	}

	.cta-row {
		.cta {
			min-width: 46px;
			width: 46px;
		}
	}
}
