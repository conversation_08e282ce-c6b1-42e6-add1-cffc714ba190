    #payment-section {
    transition: all 0.5s ease;
    }

    #payment-section.d-none {
    opacity: 0;
    height: 0;
    overflow: hidden;
    }

    #payment-section:not(.d-none) {
    opacity: 1;
    height: auto;
    }

    .submit-payment-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(37, 117, 252, 0.3);
    }

    /* Make form inputs prettier on focus */
    .form-control:focus {
    border-color: #19335E !important;
    box-shadow: 0 0 0 0.25rem rgba(106, 17, 203, 0.25) !important;
    }

    /* Custom validity styling */
    .form-control.is-invalid {
    border-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.05) !important;
    }

    /* Animated payment process */
    @keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
    }

    .fa-check-circle {
    animation: pulse 2s ease-in-out infinite;
    }

    /* Stripe Element Styles */
    .StripeElement {
    background-color: rgba(106, 17, 203, 0.05);
    border-radius: 4px;
    transition: box-shadow 0.3s, border 0.3s;
    }

    .StripeElement--focus {
    box-shadow: 0 0 0 1px rgba(106, 17, 203, 0.5), 0 1px 3px 0 rgba(106, 17, 203, 0.15);
    }

    .StripeElement--invalid {
    border-color: #dc3545;
    }

    .StripeElement--webkit-autofill {
    background-color: rgba(106, 17, 203, 0.02) !important;
    }
    .donate-wrapper {
    background-color: #f4f4f8;
    min-height: 100vh;
    }

    /* Donation Cards */
    .donation-tiers {
    perspective: 1000px;
    }

    .donation-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    transform-style: preserve-3d;
    background: white;
    height: 100%;
    border: none;
    }

    .donation-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .card-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #6a11cb;
    color: white;
    padding: 6px 12px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom-left-radius: 8px;
    z-index: 2;
    }

    .card-badge.special {
    background: linear-gradient(135deg, #ff7b02, #ff0080);
    animation: pulse 2s infinite;
    }

    @keyframes pulse {
    0% {
    box-shadow: 0 0 0 0 rgba(255, 123, 2, 0.4);
    }
    70% {
    box-shadow: 0 0 0 10px rgba(255, 123, 2, 0);
    }
    100% {
    box-shadow: 0 0 0 0 rgba(255, 123, 2, 0);
    }
    }

    .card-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(106,17,203,0.02) 0%, rgba(37,117,252,0.05) 100%);
    border-bottom: 1px solid rgba(106,17,203,0.1);
    }

    .tier-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 1.5rem;
    background: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .tier-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0;
    }

    .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: calc(100% - 140px);
    }

    .amount {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    color: #333;
    }

    .benefits {
    list-style: none;
    padding: 0;
    margin-bottom: 25px;
    flex-grow: 1;
    }

    .benefits li {
    margin-bottom: 10px;
    font-size: 0.95rem;
    display: flex;
    align-items: flex-start;
    line-height: 1.4;
    }

    .benefits li i {
    margin-right: 8px;
    margin-top: 3px;
    flex-shrink: 0;
    }

    .select-tier-btn {
    border: none;
    border-radius: 30px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 1rem;
    cursor: pointer;
    }

    /* Card specific styling */
    .bonus {
    border-top: 4px solid #6a11cb;
    }

    .bonus .tier-icon {
    color: #6a11cb;
    }

    .bonus .benefits i {
    color: #6a11cb;
    }

    .bonus .select-tier-btn {
    background: linear-gradient(135deg, #6a11cb 0%, #8c31fa 100%);
    color: white;
    }

    .silver {
    border-top: 4px solid #8e9aaf;
    }

    .silver .tier-icon {
    color: #8e9aaf;
    }

    .silver .benefits i {
    color: #8e9aaf;
    }

    .silver .select-tier-btn {
    background: linear-gradient(135deg, #8e9aaf 0%, #cbc0d3 100%);
    color: white;
    }

    .gold {
    border-top: 4px solid #ffc107;
    }

    .gold .tier-icon {
    color: #ffc107;
    }

    .gold .benefits i {
    color: #ffc107;
    }

    .gold .select-tier-btn {
    background: linear-gradient(135deg, #ffc107 0%, #ffce3a 100%);
    color: white;
    }

    .platinum {
    border-top: 4px solid #b0bec5;
    }

    .platinum .tier-icon {
    color: #78909c;
    }

    .platinum .benefits i {
    color: #78909c;
    }

    .platinum .select-tier-btn {
    background: linear-gradient(135deg, #78909c 0%, #b0bec5 100%);
    color: white;
    }

    .diamond {
    border-top: 4px solid #00bcd4;
    }

    .diamond .tier-icon {
    color: #00bcd4;
    }

    .diamond .benefits i {
    color: #00bcd4;
    }

    .diamond .select-tier-btn {
    background: linear-gradient(135deg, #00bcd4 0%, #80deea 100%);
    color: white;
    }

    .philanthropist {
    border-top: 4px solid #ff7b02;
    /* position: relative;
    overflow: hidden;
    } */
    }


    .philanthropist .tier-icon {
    color: #ff7b02;
    background: linear-gradient(135deg, #fff9f4 0%, #fff5f9 100%);
    }

    .philanthropist .benefits i {
    color: #ff7b02;
    }

    .philanthropist .select-tier-btn {
    background: linear-gradient(135deg, #ff7b02 0%, #ff0080 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255,123,2,0.25);
    }


    .custom-donation-container {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
    }

    .custom-donation-header {
    margin-bottom: 25px;
    }

    .custom-donation-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
    }

    .custom-donation-header p {
    color: #6c757d;
    margin-bottom: 0;
    }

    .custom-icon {
    background: linear-gradient(135deg, rgba(106,17,203,0.08) 0%, rgba(37,117,252,0.08) 100%);
    color: #2575fc;
    }

    .custom-donation-input {
    margin-bottom: 25px;
    }

    .input-wrapper {
    position: relative;
    max-width: 240px;
    margin: 0 auto;
    }

    .currency-symbol {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #6a11cb;
    }

    #custom-amount {
    width: 100%;
    padding: 15px 15px 15px 40px;
    font-size: 2rem;
    font-weight: 700;
    border: none;
    outline: none;
    text-align: center;
    transition: all 0.3s ease;
    background: transparent;
    }

    .input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    transform-origin: center;
    transition: all 0.3s ease;
    }

    .input-wrapper:hover .input-border,
    #custom-amount:focus + .input-border {
    height: 4px;
    transform: scaleX(1.05);
    }

    .custom-donate-btn {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 8px 20px rgba(106,17,203,0.25);
    transition: all 0.3s ease;
    cursor: pointer;
    }

    .custom-donate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(106,17,203,0.35);
    }

    .donation-frequency-cards{
        display:flex;
         flex-direction:row;
         height:7%;
         justify-content: space-around;
         align-items: center;

        }
        .frequency-card{
            background-color:#19345E;
            color:white;
            border-radius: 10px;
            padding-left:2rem;
            padding-right:2rem;
            padding-top:.3rem;
            padding-bottom: .3rem;
            cursor: pointer;
        }
        .frequency-card.active{
            background-color:#f05522;
        }

    /* Mobile Responsive */
    @media (max-width: 767px) {
    .card-badge {
    padding: 4px 10px;
    font-size: 0.65rem;
    }

    .tier-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    }

    .tier-title {
    font-size: 1.1rem;
    }

    .amount {
    font-size: 2rem;
    }

    .benefits li {
    font-size: 0.85rem;
    }

    .card-header {
    padding: 1.5rem 1rem 1rem;
    }

    .card-body {
    padding: 1.2rem;
    }

    .custom-donation-container {
    padding: 1.5rem;
    }

    #custom-amount {
    font-size: 1.7rem;
    }
    }


    .select-tier-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }



    #full-screen-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.75);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            color: white;
            font-size: 1.5rem;
            transition: opacity 0.3s ease-in-out;
        }
        .success-animation {
            animation: success-pulse 1s ease-in-out infinite;
        }
        @keyframes success-pulse {
            0% {
                transform: scale(0.9);
                opacity: 0.5;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(0.9);
                opacity: 0.5;
            }
        }

