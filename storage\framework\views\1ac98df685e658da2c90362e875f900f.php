<section class="sec recent-activities">
    <div class="container">
        <div class="head text-center mb-5 mb-md-0">
            <h2 class="text-uppercase">Recent Activity</h2>
        </div>

        <!--[if BLOCK]><![endif]--><?php if($recentDonors->count() > 0): ?>
            <div class="row activity-head text-uppercase d-none d-md-flex mt-5">
                <div class="col-md-3 donor ps-5">Donor</div>
                <div class="col-md-3 athlete">Athlete</div>
                <div class="col-md-2 amount">Amount</div>
                <div class="col-md-4 comment pe-md-5">Comment</div>
            </div>

            <div wire:loading.class="opacity-50" class="activities-container">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recentDonors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="row activity-row d-flex align-items-center mt-2 donor-row">
                        <div class="col-md-3 label py-2 py-md-3 donor ps-md-5" title="Donor"><?php echo e($donor['name']); ?></div>
                        <div class="col-md-3 label py-2 py-md-3 athlete" title="Athlete"><?php echo e($donor['donated_to']); ?></div>
                        <div class="col-md-2 label py-2 py-md-3 amount" title="Amount">$<?php echo e(number_format($donor['amount'], 2)); ?></div>
                        <div class="col-md-4 label py-2 py-md-3 comment pe-md-5" title="Comment"><?php echo e($donor['message'] ?? ''); ?></div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <div class="cta-row mt-5 text-center">
                <!--[if BLOCK]><![endif]--><?php if($recentDonors->previousPageUrl()): ?>
                    <button class="cta orange" wire:click.prevent="previousPage" wire:loading.attr="disabled">
                        <img src="images/arrow-cta-left.svg" alt="Previous Page" />
                    </button>
                <?php else: ?>
                    <button class="cta orange disabled">
                        <img src="images/arrow-cta-left.svg" alt="No Previous Page" />
                    </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if($recentDonors->nextPageUrl()): ?>
                    <button class="cta orange" wire:click.prevent="nextPage" wire:loading.attr="disabled">
                        <img src="images/arrow-cta-right.svg" alt="Next Page" />
                    </button>
                <?php else: ?>
                    <button class="cta orange disabled">
                        <img src="images/arrow-cta-right.svg" alt="No Next Page" />
                    </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php else: ?>
            <div class="row justify-content-center">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-hand-holding-heart fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">No donation activity found.</p>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</section>






<?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/livewire/all-donors.blade.php ENDPATH**/ ?>