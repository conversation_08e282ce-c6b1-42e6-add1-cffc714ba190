- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet';
- let heroName = 'Register';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName} ${heroName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/heroText.pug
include ../layouts/inputField.pug
include ../partials/footer.pug

html(lang="en")
    +head(pageTitle)

    body
        +header()

        +heroText(heroName)

        section.sec.content-row
            .container
                .copy.text-center
                    p Join the A Shot for Life Gauntlet: Fundraise, Inspire, and#[br.d-none.d-md-block] Make a Difference in the Fight Against Cancer!

        section.form
            .container
                form.row.register-form.gx-lg-5(action="")
                    .col-md-6.mb-4
                        +inputField('Name')
                    .col-md-6.mb-4
                        +inputField('Email', 'email')
                    .col-md-4.mb-4
                        +inputField('Address')
                    .col-md-4.mb-4
                        +inputField('City')
                    .col-md-4.mb-4
                        +inputField('State')
                    .col-md-12.mb-4
                        label.form-label(for="formFile") Profile Photo
                        input.form-control(type="file", id="formFile")
                    .col-md-12.mb-4
                        label.form-label(for="message") Message to Donors
                        textarea.form-control
                    .col-md-6.mb-4
                        label.form-label(for="amount") Goal Amount #[i.fw-normal (Minimum $1000)]
                        input.form-control(type="text", id="amount")
                    .col-md-12.mb-4
                        .form-check
                            input.form-check-input(type="checkbox", value="", id="fundRise")
                            label.form-check-label(for="fundRise") By submitting this form, I agree and understand that I am committing to raise a minimum of $1,000 for the ASFL Gauntlet Fundraiser.
                    .col-md-12.mb-4
                        .form-check
                            input.form-check-input(type="checkbox", value="", id="photoUse")
                            label.form-check-label(for="photoUse") I grant ASFL permission to use photos and/or videos of me from the event for their marketing purposes; the release form can be #[a.fw-bold(href="#") downloaded here].
                    .col-md-12.mb-4
                        .form-check
                            input.form-check-input(type="checkbox", value="", id="liability")
                            label.form-check-label(for="liability") I affirm that I have read and understand the liability waiver, which can be #[a.fw-bold(href="#") downloaded here].
                    .col-md-12.my-5.text-center
                        button.cta.orange.border-0(type="submit") Register Now

        +footer()
