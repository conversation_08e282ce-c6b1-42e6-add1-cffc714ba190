<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class isAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

           if (Auth::check() && Auth::user()->roles()->pluck('name')->contains('admin')) {
            return $next($request);
        }

        return redirect()->back()->with('error', 'You are not authorized to access this page.');

    }
}
