.cta {
	border-radius: 20px;
	color: #fff;
	font: 700 14px $anaheim;
	letter-spacing: 2px;
	min-width: 160px;

	&.orange {
		background: #f05522;
	}

	&.blue {
		background: #154da3;
	}
}

.cta-row {
	.cta {
		+ .cta {
			margin-left: 1rem;

			@media (min-width: 768px) {
				margin-left: 2rem;
			}
		}
	}
}

.sec {
	padding-block: 2.5rem;

	@media (min-width: 768px) {
		padding-block: 5rem;
	}

	.head {
		h2 {
			color: #154da3;
			font: 700 40px $anaheim;
		}

		h3 {
			color: #363636;
			font: 24px $inter;
		}
	}
}

.content-row {
	padding-block: 3rem;
}

.contact-meta {
	h2 {
		color: #f05522;
		font: 700 22px $inter;
	}

	p {
		color: #000;
		font: 22px $inter;
	}

	a {
		color: inherit;
		text-decoration: none;
	}
}
