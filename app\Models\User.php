<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'address',
        'city',
        'state',
        'fund_raise_message',
        'profile_photo',
        'commit_fundraising',
        'use_photo_videos',
        'fundraising_goal',
        'liability_waiver',
        'slug',
        'phone_number',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($user) {
            $user->slug = self::generateSlug($user->name);
        });
    }

    private static function generateSlug($name)
    {
        $slug = Str::slug($name);
        $count = User::where('slug', 'LIKE', "{$slug}%")->count();
        return $count ? "{$slug}-" . ($count + 1) : $slug;
    }


    public function scopeFindBySlug(Builder $query, string $slug): Builder
    {
        return $query->where('slug', $slug);
    }


    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_user', 'user_id', 'role_id');
    }

    public function hasRole($role)
    {
        return $this->roles()->where('name', $role)->exists();
    }


    public function donors()
    {
        return $this->belongsToMany(Donor::class, 'user_donors')
            ->withPivot('amount')
            ->withTimestamps();
    }

    public function programs()
    {
        return $this->belongsToMany(Program::class, 'user_programs');
    }

    public function scopeAdmins($query)
    {

        return $query->whereHas('roles', function ($q) {

            $q->where('name', 'admin');
        });
    }
}
