<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticatedByRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();

            return match (true) {
                $user->hasRole('admin') => redirect()->route('admin.dashboard'),
                $user->hasRole('user')  => redirect()->route('user.dashboard'),
                default                 => redirect()->route('home'),
            };
        }

        return $next($request);
    }
}
