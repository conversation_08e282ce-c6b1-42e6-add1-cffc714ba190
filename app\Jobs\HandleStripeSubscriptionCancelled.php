<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class HandleStripeSubscriptionCancelled implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public $subscription;
    public function __construct($subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::table('subscriptions')
            ->where('subscription_id', $this->subscription->id)
            ->update(['status' => 'canceled']);
    }
}
