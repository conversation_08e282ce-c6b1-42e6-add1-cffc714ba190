.header {
	@media (max-width: 991px) {
		position: relative;
		z-index: 1000;
	}

	.brand {
		@media (max-width: 768px) {
			img {
				height: auto;
				width: 80px;
			}
		}
	}

	.site_nav {
		@media (max-width: 991px) {
			background: #fff;
			border-bottom: 1px solid #ccc;
			left: 0;
			position: absolute;
			top: 100%;
			width: 100%;
		}

		li {
			color: #000;
			font: 700 16px/1 $anaheim;

			@media (max-width: 991px) {
				border-top: 1px solid #ccc;
				padding-block: 0.8rem;
			}

			a {
				padding: 0.5rem 1rem;

				@media (min-width: 992px) {
					border: 1px solid #fff;
					border-radius: 20px;
				}

				@media (min-width: 992px) {
					&:hover {
						color: #f05522;
					}
				}
			}

			&.active {
				a {
					color: #f05522;
					font-weight: 800;

					@media (min-width: 992px) {
						border-color: #f05522;
					}
				}
			}

			@media (min-width: 992px) {
				+ li {
					margin-left: 0.625rem;
				}
			}
		}

		a {
			color: inherit;
			text-decoration: none;
		}
	}

	@import 'hamIcon';
}
