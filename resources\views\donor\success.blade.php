<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f0f0;
            margin: 0;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        .success-container {
            text-align: center;
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }

        .checkmark-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: #4CAF50;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 20px;
            animation: pop-in 0.6s ease-out;
            position: relative;
        }

        .checkmark {
            width: 75px;
            height: 37px;
            border-bottom: 8px solid white;
            border-left: 8px solid white;
            transform: rotate(-45deg);
            position: relative;
            top: -10px;
            left: 5px;
            animation: draw-checkmark 0.3s ease-out 0.6s forwards;
            opacity: 0;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #ff6b6b;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            animation: fall 1s linear infinite;
        }

        @keyframes pop-in {
            0% { transform: scale(0); opacity: 0; }
            80% { transform: scale(1.1); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes draw-checkmark {
            from { opacity: 0; transform: rotate(-45deg) scale(0); }
            to { opacity: 1; transform: rotate(-45deg) scale(1); }
        }

        @keyframes fall {
            to {
                transform: translateX(calc(-50% + 100px)) translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body onclick="playSuccessSound()">
    <div class="success-container">
        <div class="checkmark-circle">
            <div class="checkmark"></div>
        </div>
        <h2>Payment Successful!</h2>
        <p>Your transaction has been completed.</p>
        <p>You can return Back safely <a href='{{route('home')}}'>Return to the Gauntlet site</a></p>
        <div id="confetti-container"></div>
    </div>

    <!-- Bootstrap JS and Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>
    <script>
        // Success sound
        const successSound = new Audio('data:audio/wav;base64,UklGRnQFAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAAABkYXRhUAUAAP/+/wAA//3+AP/+/wD+/f8A/v3/AP79/wD//v4A/v3/Af/+/QH+/f4B//79Av/+/AL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QL//v0C//79Av/+/QEA//8AAAAAAA==');

        // Confetti function
        function createConfetti() {
            const container = document.getElementById('confetti-container');
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.classList.add('confetti');
                confetti.style.left = `${Math.random() * 100}%`;
                confetti.style.backgroundColor = `hsl(${Math.random() * 360}, 50%, 50%)`;
                confetti.style.animationDelay = `${Math.random() * 1}s`;
                container.appendChild(confetti);
            }
        }

        // Play sound and create confetti when page loads
        window.addEventListener('load', () => {
            // successSound.play();
            createConfetti();
        });


    function playSuccessSound() {
    successSound.play();

    // Remove the event so it doesn't keep firing
    document.body.removeEventListener('click', playSuccessSound);
}

window.addEventListener('load', () => {
    document.body.addEventListener('click', playSuccessSound);
});
    </script>
</body>
</html>

