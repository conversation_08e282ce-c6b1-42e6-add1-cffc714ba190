@extends('layouts.app')

@section('title', 'Email Templates')
@section('no-header-footer', 'true')

@section('content')

@include('layouts.admin-nav')

<div class="container py-4">
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header text-white" style="background-color:#19345E; border-bottom: 3px solid #F05522;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Email Templates</h4>
                        <a href="{{ route('admin.send-mail') }}" class="btn" style="background-color: #F05522; color: white;">
                            <i class="fa fa-plus"></i> Create New Template
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tabs for Templates and Sent Emails -->
                    <ul class="nav nav-tabs mb-4" id="emailTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab" aria-controls="templates" aria-selected="true" style="color: #19345E; font-weight: 500;">
                                <i class="fa fa-save me-2"></i>Saved Templates
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab" aria-controls="sent" aria-selected="false" style="color: #19345E; font-weight: 500;">
                                <i class="fa fa-paper-plane me-2"></i>Sent Emails
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="emailTabsContent">
                        <!-- Saved Templates Tab -->
                        <div class="tab-pane fade show active" id="templates" role="tabpanel" aria-labelledby="templates-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" id="templateSearch" class="form-control" placeholder="Search templates...">
                                        <button class="btn btn-outline-secondary" type="button" id="templateSearchBtn">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <select id="templateCategoryFilter" class="form-select">
                                        <option value="">All Categories</option>
                                        <option value="marketing">Marketing</option>
                                        <option value="transactional">Transactional</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="announcement">Announcement</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Category</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="templatesTableBody">
                                        @if(isset($templates) && count($templates) > 0)
                                            @foreach($templates as $template)
                                                @if(isset($template->is_template) && $template->is_template)
                                                    <tr>
                                                        <td>{{ $template->name }}</td>
                                                        <td><span class="badge bg-secondary">{{ ucfirst($template->category) }}</span></td>
                                                        <td>{{ $template->created_at->format('M d, Y') }}</td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="{{ route('admin.send-mail') }}?template={{ $template->id }}" class="btn btn-sm btn-primary">
                                                                    <i class="fa fa-edit"></i> Use
                                                                </a>
                                                                <button type="button" class="btn btn-sm btn-info preview-template" data-id="{{ $template->id }}">
                                                                    <i class="fa fa-eye"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-danger delete-template" data-id="{{ $template->id }}">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="4" class="text-center">No templates found. <a href="{{ route('admin.send-mail') }}">Create your first template</a></td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Sent Emails Tab -->
                        <div class="tab-pane fade" id="sent" role="tabpanel" aria-labelledby="sent-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" id="sentSearch" class="form-control" placeholder="Search sent emails...">
                                        <button class="btn btn-outline-secondary" type="button" id="sentSearchBtn">
                                            <i class="fa fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <select id="recipientGroupFilter" class="form-select">
                                        <option value="">All Recipients</option>
                                        <option value="subscribers">All Subscribers</option>
                                        <option value="athletes">All Athletes</option>
                                        <option value="athletes_completed_goals">Athletes with Completed Goals</option>
                                        <option value="athletes_incomplete_goals">Athletes with Incomplete Goals</option>
                                    </select>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Subject</th>
                                            <th>Recipient Group</th>
                                            <th>Sent Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sentTableBody">
                                        @if(isset($templates) && count($templates) > 0)
                                            @foreach($templates as $template)
                                                @if(isset($template->is_template) && !$template->is_template)
                                                    <tr>
                                                        <td>{{ $template->subject ?? 'No Subject' }}</td>
                                                        <td>
                                                            @if(isset($template->recipient_group))
                                                                @if($template->recipient_group == 'subscribers')
                                                                    <span class="badge bg-primary">All Subscribers</span>
                                                                @elseif($template->recipient_group == 'athletes')
                                                                    <span class="badge bg-success">All Athletes</span>
                                                                @elseif($template->recipient_group == 'athletes_completed_goals')
                                                                    <span class="badge bg-info">Athletes with Completed Goals</span>
                                                                @elseif($template->recipient_group == 'athletes_incomplete_goals')
                                                                    <span class="badge bg-warning">Athletes with Incomplete Goals</span>
                                                                @else
                                                                    <span class="badge bg-secondary">{{ $template->recipient_group }}</span>
                                                                @endif
                                                            @else
                                                                <span class="badge bg-secondary">Unknown</span>
                                                            @endif
                                                        </td>
                                                        <td>{{ $template->created_at->format('M d, Y H:i') }}</td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <a href="{{ route('admin.send-mail') }}?template={{ $template->id }}" class="btn btn-sm btn-primary">
                                                                    <i class="fa fa-copy"></i> Reuse
                                                                </a>
                                                                <button type="button" class="btn btn-sm btn-info preview-template" data-id="{{ $template->id }}">
                                                                    <i class="fa fa-eye"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="4" class="text-center">No sent emails found.</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewTemplateModal" tabindex="-1" aria-labelledby="previewTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #19345E; color: white;">
                <h5 class="modal-title" id="previewTemplateModalLabel">Email Template Preview</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Subject:</strong> <span id="previewTemplateSubject"></span>
                </div>
                <div class="border p-3" id="previewTemplateContent">
                    <!-- Email content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" style="background-color: #19345E; color: white;" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1" aria-labelledby="deleteTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #19345E; color: white;">
                <h5 class="modal-title" id="deleteTemplateModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this template? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" style="background-color: #19345E; color: white;" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn" style="background-color: #F05522; color: white;" id="confirmDeleteTemplate">Delete</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
    .badge {
        font-size: 0.8rem;
        padding: 0.4em 0.8em;
    }

    .card {
        border: none;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }

    .table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .table th {
        background-color: rgba(25, 52, 94, 0.05);
        color: #19345E;
        font-weight: 600;
        border-bottom: 2px solid #19345E;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(25, 52, 94, 0.03);
    }

    .btn-group .btn {
        border-radius: 4px;
        margin-right: 3px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    .form-control:focus, .form-select:focus {
        border-color: rgba(25, 52, 94, 0.5);
        box-shadow: 0 0 0 0.25rem rgba(25, 52, 94, 0.25);
    }

    .nav-tabs .nav-link.active {
        border-bottom: 2px solid #F05522;
        font-weight: 600;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        border-color: transparent;
        color: #F05522;
    }

    .btn {
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .badge.bg-primary {
        background-color: #19345E !important;
    }

    .badge.bg-success {
        background-color: #28a745 !important;
    }

    .badge.bg-info {
        background-color: #17a2b8 !important;
    }

    .badge.bg-warning {
        background-color: #F05522 !important;
        color: white;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener("DOMContentLoaded", function () {
    // Preview template functionality
    const previewButtons = document.querySelectorAll('.preview-template');
    previewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateId = this.getAttribute('data-id');
            previewTemplate(templateId);
        });
    });

    // Delete template functionality
    const deleteButtons = document.querySelectorAll('.delete-template');
    let templateToDelete = null;

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            templateToDelete = this.getAttribute('data-id');
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteTemplateModal'));
            deleteModal.show();
        });
    });

    // Confirm delete button
    const confirmDeleteButton = document.getElementById('confirmDeleteTemplate');
    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function() {
            if (templateToDelete) {
                deleteTemplate(templateToDelete);
            }
        });
    }

    // Search and filter functionality
    const templateSearchBtn = document.getElementById('templateSearchBtn');
    const templateCategoryFilter = document.getElementById('templateCategoryFilter');

    if (templateSearchBtn) {
        templateSearchBtn.addEventListener('click', function() {
            filterTemplates();
        });
    }

    if (templateCategoryFilter) {
        templateCategoryFilter.addEventListener('change', function() {
            filterTemplates();
        });
    }

    const sentSearchBtn = document.getElementById('sentSearchBtn');
    const recipientGroupFilter = document.getElementById('recipientGroupFilter');

    if (sentSearchBtn) {
        sentSearchBtn.addEventListener('click', function() {
            filterSentEmails();
        });
    }

    if (recipientGroupFilter) {
        recipientGroupFilter.addEventListener('change', function() {
            filterSentEmails();
        });
    }
});

function previewTemplate(templateId) {
    // Get the CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Fetch the template data
    fetch(`{{ url('admin/get-template') }}/${templateId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        }
    })
    .then(response => response.json())
    .then(template => {
        // Populate the preview modal
        document.getElementById('previewTemplateSubject').textContent = template.subject || '(No subject)';
        document.getElementById('previewTemplateContent').innerHTML = template.content || '(No content)';

        // Show the preview modal
        const previewModal = new bootstrap.Modal(document.getElementById('previewTemplateModal'));
        previewModal.show();
    })
    .catch(error => {
        console.error('Error loading template:', error);
        alert('Error loading template. Please try again.');
    });
}

function deleteTemplate(templateId) {
    // This function would be implemented if you add a delete route
    alert('Delete functionality would be implemented here for template ID: ' + templateId);

    // Close the modal
    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
    deleteModal.hide();
}

function filterTemplates() {
    // This would filter the templates table based on search and category
    const searchTerm = document.getElementById('templateSearch').value.toLowerCase();
    const category = document.getElementById('templateCategoryFilter').value;

    const rows = document.querySelectorAll('#templatesTableBody tr');

    rows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const rowCategory = row.cells[1].textContent.toLowerCase();

        const nameMatch = name.includes(searchTerm);
        const categoryMatch = category === '' || rowCategory.includes(category);

        row.style.display = nameMatch && categoryMatch ? '' : 'none';
    });
}

function filterSentEmails() {
    // This would filter the sent emails table based on search and recipient group
    const searchTerm = document.getElementById('sentSearch').value.toLowerCase();
    const recipientGroup = document.getElementById('recipientGroupFilter').value;

    const rows = document.querySelectorAll('#sentTableBody tr');

    rows.forEach(row => {
        const subject = row.cells[0].textContent.toLowerCase();
        const rowRecipientGroup = row.cells[1].textContent.toLowerCase();

        const subjectMatch = subject.includes(searchTerm);
        const recipientMatch = recipientGroup === '' || rowRecipientGroup.includes(recipientGroup);

        row.style.display = subjectMatch && recipientMatch ? '' : 'none';
    });
}
</script>
@endpush
