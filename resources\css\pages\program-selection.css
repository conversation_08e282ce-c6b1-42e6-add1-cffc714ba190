.program-selection {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.program-cards {
    margin-bottom: 2rem;
}

.program-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    border: 3px solid transparent;
}

.program-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #f05522;
}

.program-card.selected {
    border-color: #19345e;
    box-shadow: 0 20px 40px rgba(25, 52, 94, 0.2);
}

.program-card__header {
    background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    text-align: center;
    position: relative;
}

.program-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    backdrop-filter: blur(10px);
}

.program-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: "Anaheim", sans-serif;
}

.program-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #f05522;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.program-badge.current-selection {
    background: #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.program-card__body {
    padding: 1.5rem;
    flex-grow: 1;
}

.program-description {
    margin-bottom: 1.5rem;
}

.program-description p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0;
}

.program-features {
    margin-bottom: 1.5rem;
}

.program-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.program-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 0.9rem;
}

.program-features li i {
    color: #28a745;
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

.program-requirements {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #19345e;
}

.program-requirements h4 {
    color: #19345e;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.program-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.program-requirements li {
    color: #666;
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
    padding-left: 1rem;
    position: relative;
}

.program-requirements li:before {
    content: "•";
    color: #19345e;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.program-card__footer {
    padding: 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.program-selection-form {
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.selected-program-summary .alert {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #19345e;
    border-radius: 15px;
}

.selected-program-summary h5 {
    color: #19345e;
    font-weight: 600;
}

.form-check-input:checked {
    background-color: #19345e;
    border-color: #19345e;
}

.form-check-input:focus {
    border-color: #19345e;
    box-shadow: 0 0 0 0.25rem rgba(25, 52, 94, 0.25);
}

@media (max-width: 768px) {
    .program-selection {
        padding: 2rem 0;
    }

    .program-card {
        margin-bottom: 1.5rem;
    }

    .program-card__header {
        padding: 1.5rem 1rem 1rem;
    }

    .program-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .program-title {
        font-size: 1.25rem;
    }
}
