<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Athlete Registration - A Shot for Life</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #19345e;
            font-weight: bold;
            position: relative;
            z-index: 2;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            position: relative;
            z-index: 2;
        }

        .content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 24px;
            color: #19345e;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .message {
            font-size: 16px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .user-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #19345e;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .user-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .info-item {
            background: #ffffff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .info-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 16px;
            color: #19345e;
            font-weight: 600;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(25, 52, 94, 0.3);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
        }

        .action-buttons {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 52, 94, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #19345e;
            font-size: 20px;
            text-decoration: none;
        }

        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight h3 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .highlight p {
            color: #856404;
            font-size: 14px;
        }

        @media (max-width: 600px) {
            .user-info {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="{{asset('images/ball-brand.png')}}" alt="">
            </div>
            <h1>New Athlete Registration</h1>
            <p>A Shot for Life - Making a Difference Together</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ $adminName }}! 👋</div>

            <div class="message">
                <p>We have exciting news! A new athlete has joined the <strong>A Shot for Life</strong> community and is ready to make a difference in the fight against cancer.</p>

                <p>This new registration brings us one step closer to our mission of supporting cancer research and helping those affected by this disease.</p>
            </div>

            <!-- User Information Card -->
            <div class="user-card">
                <h3 style="color: #19345e; margin-bottom: 20px; font-size: 20px;">🎯 New Athlete Details</h3>

                <div class="user-info">
                    <div class="info-item">
                        <div class="info-label">Full Name</div>
                        <div class="info-value">{{ $newUser->name }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Email Address</div>
                        <div class="info-value">{{ $newUser->email }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Location</div>
                        <div class="info-value">{{ $newUser->city }}, {{ $newUser->state }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Fundraising Goal</div>
                        <div class="info-value">${{ number_format($newUser->fundraising_goal) }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Registration Date</div>
                        <div class="info-value">{{ $newUser->created_at->format('M d, Y') }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Profile Status</div>
                        <div class="info-value" style="color: #28a745;">✅ Complete</div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ $newUser->fundraising_goal ? '$' . number_format($newUser->fundraising_goal) : 'N/A' }}</div>
                    <div class="stat-label">Fundraising Goal</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">{{ $newUser->city }}</div>
                    <div class="stat-label">Location</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number">{{ $newUser->created_at->diffForHumans() }}</div>
                    <div class="stat-label">Registered</div>
                </div>
            </div>

            <!-- Highlight Box -->
            <div class="highlight">
                <h3>🌟 Welcome to the Team!</h3>
                <p>This new athlete is ready to start their fundraising journey and make a real impact in the fight against cancer. Let's support them in reaching their goals!</p>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ route('admin.dashboard') }}" class="btn btn-primary">View Dashboard</a>
                <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">Manage Users</a>
            </div>

            <div class="message">
                <p><strong>Next Steps:</strong></p>
                <ul style="margin-left: 20px; color: #555;">
                    <li>Review the new athlete's profile and fundraising message</li>
                    <li>Ensure all required documents are uploaded</li>
                    <li>Send a welcome message if needed</li>
                    <li>Monitor their fundraising progress</li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
      <div class="footer" style="background: #f05522; padding: 30px 20px; text-align: center; border-top: 1px solid #e9ecef;">
    <div style="max-width: 600px; margin: 0 auto;">
        <div style="margin-bottom: 20px;">
            <div style="font-size: 24px; font-weight: bold; color: white; text-transform: uppercase; margin-bottom: 10px;">
                <strong>#ASFL</strong>
            </div>
        </div>

        <div style="margin-bottom: 20px;">
            <p style="color: white; margin: 0; font-size: 14px;">&copy; A Shot For Life, Inc. All rights reserved.</p>
        </div>

        <div style="margin-bottom: 20px;">
            <div style="display: inline-block;">
                <table style="border-collapse: collapse;">
                    <tr>
                        <td style="padding: 0 8px;">
                            <a href="https://www.instagram.com/ashotforlife/" target="_blank" rel="noopener noreferrer">
                                <img src="{{ asset('images/instagram.png') }}" alt="Instagram" width="20" style="display:inline-block;">
                            </a>
                        </td>
                        <td style="padding: 0 8px;">
                            <a href="https://www.tiktok.com/@ashotforlife" target="_blank" rel="noopener noreferrer">
                                <img src="{{ asset('images/tik-tok.png') }}" alt="TikTok" width="20" style="display:inline-block;">
                            </a>
                        </td>
                        <td style="padding: 0 8px;">
                            <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                                <img src="{{ asset('images/facebook.png') }}" alt="Facebook" width="20" style="display:inline-block;">
                            </a>
                        </td>
                        <td style="padding: 0 8px;">
                            <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                                <img src="{{ asset('images/X.png') }}" alt="Twitter" width="20" style="display:inline-block;">
                            </a>
                        </td>
                        <td style="padding: 0 8px;">
                            <a href="mailto:<EMAIL>">
                                <img src="{{ asset('images/mail.png') }}" alt="Email" width="20" style="display:inline-block;">
                            </a>
                        </td>
                        <td style="padding: 0 8px;">
                            <a href="https://www.youtube.com/@ashotforlife/featured" target="_blank" rel="noopener noreferrer">
                                <img src="{{ asset('images/youtube.png') }}" alt="YouTube" width="20" style="display:inline-block;">
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div style="border-top: 1px solid rgba(255,255,255,0.3); padding-top: 20px;">
            <p style="color: rgba(255,255,255,0.8); font-size: 12px; margin: 0;">
                This is an automated notification. Please do not reply to this email.
            </p>
        </div>
    </div>
</div>

    </div>
</body>
</html>
