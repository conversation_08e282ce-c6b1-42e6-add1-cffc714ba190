     document.addEventListener("DOMContentLoaded", function () {
    const contactForm = document.getElementById("contactForm");
    const sendButton = document.getElementById("sendMessage");
    const successModalEl = document.getElementById("successModal");
    const errorAlert = document.getElementById("errorAlert");


    let successModal;
    if (successModalEl) {
        successModal = new bootstrap.Modal(successModalEl);
    }

    if (contactForm) {
        contactForm.addEventListener("submit", async function (e) {
            e.preventDefault();

            sendButton.disabled = true;
            sendButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...`;

            try {
                const formData = new FormData(contactForm);
                const response = await fetch(contactForm.action, {
                    method: "POST",
                    body: formData,
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "Accept": "application/json",
                        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').getAttribute("content"),
                    },
                });

                const result = await response.json();

                if (response.ok) {
                    if (successModal) successModal.show();
                    contactForm.reset();
                    if (errorAlert) errorAlert.classList.add("d-none");
                } else {
                    if (result.errors) showValidationErrors(result.errors);
                    sendButton.disabled=false;
                    sendButton.innerHTML = `CONTACT`;

                }
            } catch (error) {
                console.error("Error submitting form:", error);
                sendButton.disabled=false;
                sendButton.innerHTML = `CONTACT`;
            }

            sendButton.disabled = false;
            sendButton.innerHTML = `CONTACT`;
        });
    }

    function showValidationErrors(errors) {
        if (!errorAlert) return;
        let errorMessages = "";
        for (let field in errors) {
            errorMessages += `<li>${errors[field].join(", ")}</li>`;
        }
        errorAlert.innerHTML = `<ul>${errorMessages}</ul>`;
        errorAlert.classList.remove("d-none");
    }
});
