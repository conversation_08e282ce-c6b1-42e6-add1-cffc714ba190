<?php

namespace App\Services;

use App\Models\Donor;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class HomePageService
{


    public function getHomePageData(): array
    {

        $currentYear = date('Y');
        $goal = DB::table('settings')
            ->where('year', $currentYear)
            ->value('goal_amount');




        $participants = User::whereHas('roles', function ($query) {
            $query->where('name', 'user');
        })->count();


        $totalDonated = DB::table('donors')->sum('amount_donate');


        $topFundraisers = User::leftJoin('user_donors', 'users.id', '=', 'user_donors.user_id')
            ->select(
                'users.id',
                'users.name',
                'users.slug',
                'users.profile_photo',
                'users.fundraising_goal',
                DB::raw('COALESCE(SUM(user_donors.amount), 0) as total_collected')
            )
            ->groupBy('users.id', 'users.name', 'users.slug', 'users.profile_photo', 'users.fundraising_goal')
            ->having('total_collected', '>', 0)
            ->orderByDesc('total_collected')
            ->limit(5)
            ->get()
            ->each(function ($user) {
                $user->donationPageUrl = route('donationPage', ['slug' => $user->slug]);
            });



        $recentDonors = Donor::latest('created_at')->limit(5)->get();

        return [
            'goal' => $goal,
            'totalDonated' => $totalDonated,
            'participants' => $participants,
            'topFundraisers' => $topFundraisers,
            'recentDonors' => $recentDonors,
        ];
    }
}
