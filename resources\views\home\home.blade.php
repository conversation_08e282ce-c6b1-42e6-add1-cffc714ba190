@extends('layouts.app')

@section('title', '2025 ASFL Gauntlet | Home')

@section('meta_description', 'Step onto the court for 24 hours of purpose. The ASFL Gauntlet brings players together to honor, uplift, and fight for those facing cancer.')

@section('content')



    <section class="hero">
        <div class="container-fluid gx-0">
            <div class="hero-img">
               <img class="w-100 d-none d-md-block"
                    src="{{ asset('images/newHero.webp') }}"
                    alt="banner desktop"
                    style="aspect-ratio: 1351 / 461;"
                    decoding="async" />
                <img class="w-100  d-md-none" src="{{ asset('images/Banner-mob1.jpg') }}" alt="banner mobile"
                  loading="lazy"
                   decoding="async" />
            </div>
        </div>
        <div class="hero__text">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h1 class="text-uppercase mb-3">A Shot for Life <strong class="d-block">Gauntlet</strong></h1>
                        <p><strong>Join the A Shot for Life Gauntlet:</strong> Fundraise, Inspire, and Make a Difference in
                            the Fight Against Cancer!</p>
                        <div class="cta-row"><a class="cta orange hoverbutton" href="{{ route('register') }}">Register</a><a
                                class="cta blue hoverbutton" href="{{ route('donate') }}">Donate</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="achieve-goal">
        <div class="container-fluid gx-0">
            <div class="bg-blue">
                <div class="container">
                    <div class="meta-timeline d-flex justify-content-between">
                        <div class="meta text-center">
                            <h2>
                                Help Us Achieve Our Goal:<br />
                                <strong>${{ preg_replace('/\.00$/', '', number_format($data['goal'], 2)) }}</strong>


                            </h2>
                        </div>
                        <div class="meta text-center">
                            <h2>
                                Active Participants:<br />

                                <a href="{{route('leaderBoard')}}" style="text-decoration: none">
                                <strong>{{ $data['participants'] }}</strong>
                                </a>
                            </h2>
                        </div>
                    </div>
                    <div class="time-line mt-5">
                        <div class="meta-list d-flex justify-content-between"><span class="label first">Let’s
                                Go!</span><span class="label">Keep Going!</span><span class="label">Almost
                                there!</span><span class="label last">You Did it!</span></div>
                        <div class="bar mt-4">
                            <span class="fill align-items-center d-flex"
                                style="width: {{ min(($data['totalDonated'] / $data['goal']) * 100, 100) }}%">
                                <!-- Sum positioned based on width -->
                                @if (($data['totalDonated'] / $data['goal']) * 100 < 15)
                                    <span class="sum position-absolute"
                                        style="left: calc({{ min(($data['totalDonated'] / $data['goal']) * 100, 100) }}% + 10px);">${{ number_format($data['totalDonated']) }}</span>
                                @else
                                    <span class="sum">${{ number_format($data['totalDonated']) }}</span>
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec leaderboard">
        <div class="container">
            <div class="head text-center">
                <h2 class="text-uppercase">LEADERBOARD</h2>
            </div>
            <div class="row justify-content-center">

                @forelse($data['topFundraisers'] as $fundraiser)
                    <div class="col-sm-6 col-lg-4 col-xl-3 mt-5">
                        <div class="leader_box d-flex flex-column text-center align-items-center">
                            <a href="{{ $fundraiser->donationPageUrl }}" class="text-decoration-none">
                                <div class="img mb-3">
                                    <img src="{{ Storage::url($fundraiser->profile_photo) }}"
                                        alt="{{ "fundraiser". ' '. $fundraiser->name }}" />
                                </div>
                                <div class="text">
                                    <h2 class="mb-0">{{ $fundraiser->name }}</h2>
                                </div>
                            </a>
                            <div class="text">
                                <h3 class="price">${{ $fundraiser->total_collected }} out of
                                    ${{ $fundraiser->fundraising_goal }}</h3>
                            </div>
                        </div>
                    </div>

                @empty
                    <div class="custom-slider w-100" id="noFundraisers">
                        <div class="py-5 px-4 text-center mx-auto"
                            style="
                background-color: rgba(106,17,203,0.05);
                border-radius: 12px;
                height: 420px;
                max-width: 500px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            ">
                            <img src="https://cdn-icons-png.flaticon.com/512/2748/2748558.png" alt="No fundraisers"
                                class="mb-4" style="opacity: 0.5; width: 100px;">
                            <h4 style="color: #19345e;">No Fundraisers Yet</h4>
                            <p class="text-muted mb-4">Be the first to start fundraising!</p>
                            <a href="{{ route('register') }}" class="btn text-white px-4 py-2"
                                style="
                    background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
                    border-radius: 50px;
                ">
                                Start Now
                            </a>
                        </div>
                    </div>
                @endforelse
                <div class="cta-row mt-5 text-center"><a class="cta orange hoverbutton"
                        href="{{ route('leaderBoard') }}">See All</a></div>
            </div>
        </div>
    </section>
    <section class="hording">
        <div class="container-fluid gx-0">
            <div class="img">
                <img class="w-100 d-none d-md-block" src="{{ asset('images/wGauntlet.jpg') }}" alt="" />
                <img class="w-100  d-md-none" src="{{ asset('images/Banner-mob2.jpg') }}" alt="" />
            </div>
        </div>
        <div class="hording__text">
            <div class="container">
                <div class="row justify-content-md-end">
                    <div class="col-md-7 text-md-end py-5 d-flex flex-column align-items-md-end">
                        <h2 class="text-uppercase mb-0">What is A Shot for Life <strong
                                class="d-md-block">Gauntlet?</strong></h2>
                        <div class="p-size my-3 my-lg-4">
                            <p>The A Shot for Life Gauntlet is your chance to step up, unleash your inner champion, and
                                score big in the fight against cancer with every dollar you raise. Join a thrilling showdown
                                of grit and heart, where your passion fuels a game-changing impact for an unbeatable cause!
                            </p>
                        </div>
                        <div class="cta-row"><a class="cta orange hoverbutton" href="{{ route('register') }}">Register
                                Now</a><a class="cta blue hoverbutton" href="{{ route('about') }}">Find Out More</a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @livewire('all-donors')

    <section class="hording">
        <div class="container-fluid gx-0">
            <div class="img">
                   <img class="w-100 d-none d-md-block" src="{{ asset('images/ourImpact.jpg') }}" alt="" />
                <img class="w-100  d-md-none" src="{{ asset('images/Banner-mob3.jpg') }}" alt="" />
            </div>
        </div>
        <div class="hording__text">
            <div class="container">
                <div class="row">
                  <div class="col-md-7 py-5">
                   <h2 class="text-uppercase mb-0">Our <strong>Impact</strong></h2>
                    <div class="p-size my-3 my-lg-4">
                        <blockquote class="blockquote mb-3">
                            <p class="mb-2">
                                “The tremendous support from A Shot For Life is instrumental in enabling our group to execute
                                studies that enhance our understanding of the immune system in the context of malignant
                                brain tumors and provides hope that we can continue to provide meaningful, new therapies for
                                our patients. Thanks to ASFL for leading the way and making it all possible.”
                            </p>
                            <footer class="blockquote-footer mt-2">MGH Cancer Center</footer>
                        </blockquote>
                    </div>
                    <div class="cta-row">
                        <a class="cta blue hoverbutton" href="{{ route('about') }}">Find Out More</a>
                    </div>
                </div>

                </div>
            </div>
        </div>
    </section>
    <section class="make-difference">
        <div class="container-fluid gx-0">
            <div class="bg-blue text-center">
                <div class="container">
                    <h2 class="text-uppercase mb-4">How to make <strong>a difference!</strong></h2>
                    <p>
                        Join the A Shot for Life Gauntlet: Fundraise, Inspire, and<br class="d-none d-md-block" />
                        Make a Difference in the Fight Against Cancer!
                    </p>
                    <div class="cta-row mt-5"><a class="cta orange hoverbutton"
                            href="{{ route('register') }}">Register</a><a class="cta blue hoverbutton"
                            href="{{ route('donate') }}">Donate</a></div>
                </div>
            </div>
        </div>
    </section>

@endsection

@push('scripts')
 @vite('resources/js/pages/home.js')
@endpush






{{-- <section class="hero-section text-white"
style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); padding: 100px 0;">
<div class="container">
    <div class="row align-items-center">
        <div class="col-lg-8 text-center mx-auto">
            <h1 class="display-4 mb-4 fw-bold" style="
            font-family: system-ui, sans-serif;
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: none;
          ">
            A Shot for Life Gauntlet
          </h1>
            <p class="lead mb-5" style="color: rgba(255,255,255,0.9);">
                Fundraise, inspire, and make a difference in the fight against cancer!
            </p>
            <div class="d-flex justify-content-center gap-4">
                <a href="{{ route('register') }}" class="btn btn-light btn-lg px-5 py-3 rounded-pill"
                    style="
                color: #6a11cb;
                font-weight: 600;
                box-shadow: 0 4px 6px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            "
                    onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    Register
                </a>
                <a href="{{route('donate')}}" class="btn btn-outline-light btn-lg px-5 py-3 rounded-pill"
                    style="
                border: 2px solid white;
                transition: all 0.3s ease;
            "
                    onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='scale(1.05)'"
                    onmouseout="this.style.backgroundColor='transparent'; this.style.transform='scale(1)'">
                    Donate
                </a>
            </div>
        </div>
    </div>
</div>
</section>


<section class="goal-section py-5">
<div class="container">
    <div class="row g-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100" style="background: linear-gradient(145deg, #f0f0f0, #ffffff);">
                <div class="card-body">
                    <h3 class="card-title mb-4" style="color: #6a11cb;">Help Us Achieve Our Goal</h3>
                    <h2 class="mb-3" style="color: #2575fc;"> ${{ $data['goal'] }}</h2>
                    <div class="progress"
                    style="height: 30px; background-color: rgba(106,17,203,0.1); position: relative; border-radius: 5px; overflow: hidden;">
                    <div class="progress-bar" role="progressbar"
                        style="width: 0%; background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                               transition: width 0.8s ease-in-out; height: 100%;">
                    </div>

                    <span class="progress-text"
                        style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                               display: flex; align-items: center; justify-content: center;
                               font-weight: bold; font-size: 16px; color: #020203; z-index: 2;">
                        ${{ number_format($data['totalDonated'], 2) }} raised
                    </span>
                </div>

                </div>
            </div>
        </div>


        <div class="col-md-6">
            <div class="card shadow-sm h-100" style="background: linear-gradient(145deg, #f0f0f0, #ffffff);">
                <div class="card-body text-center d-flex flex-column justify-content-center">
                    <h1 style="color: #6a11cb;">{{ $data['participants'] }}</h1>
                    <p class="lead" style="color: #2575fc;">Active Participants</p>
                    <small class="text-muted">Helping us reach our goal</small>
                </div>
            </div>
        </div>
    </div>
</div>
</section>

<section class="leaderboard-section py-5" style="background: linear-gradient(to right, #f8f9fa, #eef1f8);">
<div class="container">
    <div class="text-center mb-5">
        <h2 class="display-5 fw-bold" style="color: #6a11cb; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">Top
            Fundraisers</h2>
        <div class="mx-auto"
            style="width: 80px; height: 4px; background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);"></div>
    </div>

    <!-- Custom Slider for Fundraisers -->
    <div class="custom-slider-container position-relative">
        <div class="custom-slider-wrapper">
            <div class="custom-slider" id="fundraiserSlider">
                @forelse($data['topFundraisers'] as $fundraiser)
                    <div class="custom-slide">
                        <a href="{{ $fundraiser->donationPageUrl }}" class="text-decoration-none">
                            <div class="fundraiser-card position-relative"
                                style="
                            border-radius: 12px;
                            overflow: hidden;
                            box-shadow: 0 10px 20px rgba(0,0,0,0.08);
                            transition: all 0.4s ease;
                            height: 420px;
                            background: white;
                            margin: 0 15px;
                        ">
                                <div style="height: 60%; overflow: hidden;">
                                    <img src="{{ Storage::url($fundraiser->profile_photo) }}"
                                        style="
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                        transition: transform 0.5s ease;
                                    "
                                        onmouseover="this.style.transform='scale(1.05)'"
                                        onmouseout="this.style.transform='scale(1)'">
                                </div>

                                <div
                                    style="
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                height: 60%;
                                background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 40%, rgba(0,0,0,0.4) 100%);
                            ">
                                </div>

                                <div
                                    style="
                                position: absolute;
                                bottom: 40%;
                                left: 0;
                                right: 0;
                                background: linear-gradient(135deg, rgba(106,17,203,0.9) 0%, rgba(37,117,252,0.9) 100%);
                                padding: 12px 15px;
                            ">
                                    <h5 class="text-white fw-bold mb-0">{{ $fundraiser->name }}</h5>
                                </div>

                                <div class="p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="fw-bold"
                                            style="color: #6a11cb;">${{ $fundraiser->total_collected }}</span>
                                        <span class="text-muted small">of
                                            ${{ $fundraiser->fundraising_goal }}</span>
                                    </div>

                                    <div class="progress mb-3"
                                        style="height: 8px; border-radius: 4px; background-color: rgba(106,17,203,0.1);">
                                        <div class="progress-bar" role="progressbar"
                                            style="width: {{ ($fundraiser->total_collected / $fundraiser->fundraising_goal) * 100 }}%;
                                        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                                        border-radius: 4px;"
                                            aria-valuenow="{{ $fundraiser->total_collected }}" aria-valuemin="0"
                                            aria-valuemax="{{ $fundraiser->fundraising_goal }}">
                                        </div>
                                    </div>

                                    <button class="btn text-white px-4 py-2 w-100"
                                        style="
                                    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                                    border-radius: 50px;
                                    transition: all 0.3s ease;
                                    box-shadow: 0 4px 10px rgba(106,17,203,0.2);
                                "
                                        onmouseover="this.style.transform='scale(1.03)'; this.style.boxShadow='0 6px 15px rgba(106,17,203,0.3)'"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 10px rgba(106,17,203,0.2)'">
                                        Support Now
                                    </button>
                                </div>
                            </div>
                        </a>
                    </div>
                @empty
                    <div class="custom-slider w-100" id="noFundraisers">
                        <div class="py-5 px-4 text-center mx-auto"
                            style="
                        background-color: rgba(106,17,203,0.05);
                        border-radius: 12px;
                        height: 420px;
                        max-width: 500px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                    ">
                            <img src="https://cdn-icons-png.flaticon.com/512/2748/2748558.png" alt="No fundraisers"
                                class="mb-4" style="opacity: 0.5; width: 100px;">
                            <h4 style="color: #6a11cb;">No Fundraisers Yet</h4>
                            <p class="text-muted mb-4">Be the first to start fundraising!</p>
                            <a href="{{ route('register') }}" class="btn text-white px-4 py-2"
                                style="
                            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                            border-radius: 50px;
                        ">
                                Start Now
                            </a>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>

        @if (count($data['topFundraisers']) > 0)
            <button class="custom-slider-nav custom-slider-prev" id="sliderPrev" aria-label="Previous slide">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
            </button>
            <button class="custom-slider-nav custom-slider-next" id="sliderNext" aria-label="Next slide">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
            </button>

            <div class="custom-slider-dots" id="sliderDots"></div>
        @endif
    </div>

    <div class="text-center mt-5 pt-4">
        <a href="{{ route('leaderBoard') }}" class="btn btn-outline-primary btn-lg rounded-pill"
            style="
        color: #6a11cb;
        border: 2px solid #6a11cb;
        transition: all 0.3s ease;
        font-weight: 600;
        padding: 12px 30px;
    "
            onmouseover="this.style.backgroundColor='rgba(106,17,203,0.1)'; this.style.transform='scale(1.05)'"
            onmouseout="this.style.backgroundColor='transparent'; this.style.transform='scale(1)'">
            View All Fundraisers
        </a>
    </div>
</div>
</section>

{{-- About Gauntlet Section --}}
{{-- <section class="about-section py-5" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);">
<div class="container text-white text-center">
    <h2 class="mb-4">What is A Shot for Life Gauntlet?</h2>
    <p class="lead mb-5">
        A transformative fundraising initiative dedicated to supporting cancer research
        and providing hope for those battling this challenging disease.
    </p>
    <div class="d-flex justify-content-center gap-4">
        <a href="#" class="btn btn-light btn-lg rounded-pill"
            style="
        color: #6a11cb;
        transition: all 0.3s ease;
    "
            onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
            Find Out More
        </a>
        <a href="#" class="btn btn-outline-light btn-lg rounded-pill"
            style="
        border: 2px solid white;
        transition: all 0.3s ease;
    "
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'"
            onmouseout="this.style.backgroundColor='transparent'">
            Register Now
        </a>
    </div>
</div>
</section> --}}

{{-- Recent Donors Table --}}
{{-- <section class="recent-donors py-5">
@livewire('all-donors')
</section>


<section class="about-section py-5" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);">
<div class="container text-white text-center">
    <h2 class="mb-4">Our Impact</h2>
    <p class="lead mb-5">
        The tremendous support from A shot for life is instrumental in enabling our group to execute studies that
        enhance our understanding of the immune system in the context of maligant brain tumors and provides hope
        that we
        can continue to provide meaningful, new therappies for our patients. Thanks to ASFl for leading the way and
        making
        it all possible.
    </p>
    <div class="d-flex justify-content-center gap-4">
        <a href="#" class="btn btn-light btn-lg rounded-pill"
            style="
        color: #6a11cb;
        transition: all 0.3s ease;
    "
            onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
            Find Out More
        </a>
    </div>
</div>
</section>
<!-- Email Signup Section -->
<section class="email-signup py-5" style="background: linear-gradient(to right, #f8f9fa, #eef1f8);">
<div class="container">
<div class="row justify-content-center">
    <div class="col-lg-8 text-center">
        <h3 class="mb-4" style="color: #6a11cb;">Stay Updated with Gauntlet Events</h3>
        <div class="mx-auto mb-4"
            style="width: 60px; height: 3px; background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);"></div>
        <p class="lead mb-4">Get the latest updates about upcoming events, fundraising milestones, and success stories.</p>

        <form id="newsletterForm" action="{{route('subscribeForLatestEvents')}}" method="POST" class="d-flex flex-column flex-md-row gap-2 justify-content-center align-items-center">

            @csrf
            <div class="form-group flex-grow-1" style="max-width: 400px;">
                <input type="email" class="form-control form-control-lg rounded-pill"
                       placeholder="Your email address" required
                       style="padding: 12px 25px; border: 1px solid rgba(106,17,203,0.2);">
            </div>
            <button type="submit" id="subscribeForNewsLetter" class="btn text-white px-4 py-2 rounded-pill"
                style="
                    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 10px rgba(106,17,203,0.2);
                    min-width: 150px;
                "
                onmouseover="this.style.transform='scale(1.03)'; this.style.boxShadow='0 6px 15px rgba(106,17,203,0.3)'"
                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 10px rgba(106,17,203,0.2)'">
                Subscribe
            </button>
        </form>

        <p class="mt-3">
            <a href="#" id="optOutLink" data-bs-toggle="modal" data-bs-target="#optOutModal"
               style="color: #2575fc; text-decoration: underline; font-size: 0.9rem;">
                Already subscribed? Click here to opt out
            </a>
        </p>
    </div>
</div>
</div>
</section>

<!-- Opt Out Modal -->
<div class="modal fade" id="optOutModal" tabindex="-1" aria-labelledby="optOutModalLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered">
<div class="modal-content" style="border-radius: 15px; overflow: hidden;">
    <div class="modal-header" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); border: none;">
        <h5 class="modal-title text-white" id="optOutModalLabel">Unsubscribe from Updates</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body p-4">
        <p class="text-muted mb-4">Please enter your email address to unsubscribe from our newsletter.</p>
        <form id="optOutForm">
            <div class="form-group mb-4">
                <label for="optOutEmail" class="form-label" style="color: #6a11cb;">Email Address</label>
                <input type="email" class="form-control form-control-lg" id="optOutEmail"
                       placeholder="<EMAIL>" required
                       style="border: 1px solid rgba(106,17,203,0.2); border-radius: 8px;">
            </div>
            <div class="d-grid">
                <button type="submit" id="optOutButton" class="btn text-white py-2"
                    style="
                        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
                        border-radius: 8px;
                        transition: all 0.3s ease;
                    "
                    onmouseover="this.style.transform='scale(1.02)'"
                    onmouseout="this.style.transform='scale(1)'">
                    Unsubscribe
                </button>
            </div>
        </form>
    </div>
</div>
</div>
</div>  --}}

{{-- <style>
        body {
            background-color: #f4f4f8;
        }

        .progress {
            height: 30px;
            background-color: rgba(106, 17, 203, 0.1);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            border-radius: 15px;
            background-size: 15px 15px;
            background-image: linear-gradient(45deg,
                    rgba(255, 255, 255, 0.15) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, 0.15) 50%,
                    rgba(255, 255, 255, 0.15) 75%,
                    transparent 75%,
                    transparent);
            animation: progress-bar-stripes 2s linear infinite;
        }

        @keyframes progress-bar-stripes {
            from {
                background-position: 15px 0;
            }

            to {
                background-position: 0 0;
            }
        }

        .fundraiser-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .fundraiser-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(106, 17, 203, 0.2);
        }

        .btn-gradient {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            box-shadow: 0 4px 10px rgba(106, 17, 203, 0.2);
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: scale(1.03);
            box-shadow: 0 6px 15px rgba(106, 17, 203, 0.3);
        }

        /* Custom Slider Styles */
        .custom-slider-container {
            position: relative;
            padding: 0 60px;
            margin-bottom: 40px;
        }

        .custom-slider-wrapper {
            overflow: hidden;
            position: relative;
        }

        .custom-slider {
            display: flex;
            transition: transform 0.5s ease;
        }

        .custom-slide {
            flex: 0 0 100%;
            max-width: 100%;
            transition: all 0.3s ease;
        }

        .custom-slider-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(106, 17, 203, 0.3);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .custom-slider-nav:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 15px rgba(106, 17, 203, 0.4);
        }

        .custom-slider-prev {
            left: 0;
        }

        .custom-slider-next {
            right: 0;
        }

        .custom-slider-dots {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .custom-slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(106, 17, 203, 0.3);
            margin: 0 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .custom-slider-dot.active {
            background-color: #6a11cb;
            transform: scale(1.2);
        }

        @media (min-width: 768px) {
            .custom-slide {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }

        @media (min-width: 1024px) {
            .custom-slide {
                flex: 0 0 33.333%;
                max-width: 33.333%;
            }
        }

        .centered-cards {
            display: flex !important;
            justify-content: center !important;
            flex-wrap: wrap !important;
            gap: 20px !important;
        }

        .slider-active {
            overflow: hidden;
            position: relative;
        }



        .slider-active .custom-slider {
            display: flex;
            justify-content: flex-start;
            flex-wrap: nowrap;
            transition: transform 0.5s ease;
        }

        .custom-slider-container {
            position: relative;
            padding: 0 20px;
        }

        .custom-slider {
            gap: 20px;
        }

        @media (min-width: 768px) {
            .custom-slide {
                flex: 0 0 calc(50% - 10px);
                max-width: calc(50% - 10px);
            }
        }

        @media (min-width: 1024px) {
            .custom-slide {
                flex: 0 0 calc(33.333% - 14px);
                max-width: calc(33.333% - 14px);
            }
        }


        .custom-slider:not(.slider-active) {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 20px;
        }

        .custom-slider:not(.slider-active) .custom-slide {
            flex: 0 0 calc(33.333% - 14px);
            max-width: calc(33.333% - 14px);
        }

        .single-slide {
            display: flex;
            justify-content: center;

        }
    </style> --}}
