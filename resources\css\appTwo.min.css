/* .pagination {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    margin: 30px 0;
    padding: 0;
}

.page-item {
    list-style: none;
    transition: transform 0.2s ease;
}

.page-item:not(.disabled):not(.prev):not(.next):hover {
    transform: translateY(-2px);
}

.page-item:not(.prev):not(.next) .page-link {
    border: none;
    border-radius: 50%;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 15px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background-color: #f05522;
    color: white;
    box-shadow: 0 3px 6px rgba(240, 85, 34, 0.2);
    position: relative;
    overflow: hidden;
}

.page-item .page-link::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0) 70%
    );
    transform: scale(0);
    opacity: 0;
    transition: transform 0.4s ease, opacity 0.3s ease;
    border-radius: 50%;
}

.page-item:not(.disabled):hover .page-link::before {
    transform: scale(2);
    opacity: 1;
}

.page-item.active .page-link {
    background-color: #19345e;
    color: white;
    box-shadow: 0 4px 10px rgba(25, 52, 94, 0.4);
    font-weight: 700;
}

.page-item:not(.prev):not(.next):not(.active) .page-link:hover {
    background-color: #e6491e;
    color: white;
    box-shadow: 0 4px 8px rgba(240, 85, 34, 0.3);
}

.page-item.prev .page-link,
.page-item.next .page-link {
    border: none;
    background-color: transparent;
    color: #19345e;
    box-shadow: none;
    transition: all 0.3s ease;
    font-size: 18px;
    width: auto;
    padding: 0 10px;
}

.page-item.prev .page-link:hover,
.page-item.next .page-link:hover {
    background-color: transparent;
    color: #f05522;
    transform: scale(1.2);
}

.page-item.disabled .page-link {
    color: #d5d5d5;
    background-color: #f5f5f5;
    pointer-events: none;
    opacity: 0.6;
    box-shadow: none;
}

.pagination-lg .page-link {
    line-height: 1.5;
}

.table-hover tbody tr:hover {
    background-color: #f9f5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(106, 17, 203, 0.05);
} */
/* loader css */

body.loading {
    overflow: hidden;
}

#loader {
    position: fixed;
    inset: 0;
    background: #f5f5f5;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease;
}

.loader {
    display: flex;
    align-items: flex-end;
    gap: 20px;
}

.ball {
    width: 60px;
    height: 60px;
    position: relative;
    border-radius: 50%;
    background: #e67232;
    background: url("data:image/svg+xml,%3Csvg width='153' height='210' viewBox='0 0 153 210' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_2382_3)'%3E%3Cpath d='M76.4984 152.29C117.646 152.29 151.002 118.934 151.002 77.7865C151.002 36.6392 117.646 3.28271 76.4984 3.28271C35.3511 3.28271 1.99463 36.6392 1.99463 77.7865C1.99463 118.934 35.3511 152.29 76.4984 152.29Z' fill='%23FF2D00'/%3E%3Cpath d='M76.4984 154.285C34.3181 154.285 0 119.967 0 77.7831C0 35.5995 34.3181 1.28467 76.4984 1.28467C118.679 1.28467 153 35.6028 153 77.7831C153 119.963 118.682 154.281 76.4984 154.281V154.285ZM76.4984 5.27387C36.5193 5.27387 3.9892 37.8039 3.9892 77.7831C3.9892 117.762 36.516 150.292 76.4984 150.292C116.481 150.292 149.008 117.765 149.008 77.7831C149.008 37.8007 116.481 5.27387 76.4984 5.27387Z' fill='%23231F20'/%3E%3Cpath d='M112.353 145.606C111.669 145.606 110.988 145.328 110.497 144.78C109.577 143.753 109.661 142.178 110.684 141.258C110.923 141.042 134.79 118.853 127.451 76.0886C120.057 32.995 75.656 5.68379 75.2106 5.41591C74.0326 4.70263 73.6549 3.16956 74.3682 1.99152C75.0783 0.813482 76.6146 0.432636 77.7894 1.14592C79.6968 2.29814 124.582 29.874 132.37 75.243C140.196 120.844 115.093 144.002 114.021 144.967C113.544 145.396 112.95 145.606 112.356 145.606H112.353Z' fill='%23231F20'/%3E%3Cpath d='M143.205 43.2937C111.517 40.4438 73.6711 60.1316 70.2661 61.9454C25.2004 77.0179 10.6959 90.3507 6.46145 98.8843C6.06124 99.6879 5.76431 100.437 5.53516 101.134C6.67769 102.502 7.31674 104.255 7.44907 106.053L9.90197 105.22L9.93425 105.323C9.89552 105.159 6.68738 88.4336 72.0476 66.6092L72.4414 66.4381C72.5414 66.3833 82.5661 61.0062 96.3508 56.1133C108.961 51.6336 127.383 46.6503 143.572 48.3254C143.976 48.3641 144.36 48.2995 144.712 48.164C143.74 46.7536 143.243 45.0301 143.208 43.2937H143.205Z' fill='%23231F20'/%3E%3Cpath d='M152.955 81.5594C151.938 78.1543 149.537 74.801 145.964 71.8736C139.867 66.8774 131.524 64.0275 125.666 64.9312C123.539 65.2604 121.609 66.0512 119.999 66.7806C107.799 72.3158 98.0808 81.9208 92.6393 93.8303C92.068 95.0826 92.6167 96.564 93.8722 97.1353C94.2078 97.2902 94.5596 97.3612 94.9082 97.3612C95.8539 97.3612 96.7576 96.819 97.1771 95.9024C102.118 85.0903 110.955 76.3598 122.061 71.3217C123.365 70.7311 124.901 70.0952 126.434 69.8596C130.918 69.169 137.805 71.6348 142.808 75.7337C146.358 78.6417 148.385 81.895 148.517 84.8934C148.559 85.8358 149.114 86.6298 149.908 87.0203C150.205 84.8869 151.219 82.873 152.958 81.5626L152.955 81.5594Z' fill='%23231F20'/%3E%3Cpath d='M117.084 15.1079C116.987 15.2338 116.9 15.3726 116.826 15.5178C112.611 24.0029 103.49 31.4326 95.1371 33.1787C92.581 33.7145 89.9764 33.779 86.8392 33.8113C72.0282 33.9566 57.3075 33.3014 42.2803 32.5526C41.5831 32.5171 40.5987 32.4396 39.4078 32.3428C30.4837 31.6231 20.9981 31.1486 15.2789 33.3433C15.2015 36.1093 13.946 38.8398 11.4995 40.3018C11.5931 40.3632 11.6803 40.4277 11.7835 40.4761C12.1353 40.6472 12.5033 40.7246 12.868 40.7246C13.7943 40.7246 14.6851 40.205 15.1143 39.3174C15.9502 37.5907 20.7721 35.8446 39.0076 37.3164C40.2631 37.4164 41.3023 37.5004 42.035 37.5391C57.1429 38.2911 71.9443 38.9495 86.8909 38.8043C90.1894 38.772 93.1619 38.6945 96.1603 38.0684C106.043 36.0028 116.377 27.6435 121.296 17.7448C121.399 17.5382 121.461 17.322 121.499 17.1057C119.666 16.8959 118.191 16.1665 117.087 15.1144L117.084 15.1079Z' fill='%23231F20'/%3E%3Cpath d='M64.0435 92.3911L14.469 172.211L39.2563 206.981L78.8675 121.326L64.0435 92.3911Z' fill='%23A1B5B6'/%3E%3Cpath d='M39.5337 209.947L12.6711 172.265L64.1693 89.354L80.5295 121.29L39.5337 209.947ZM16.2634 172.152L38.9786 204.014L77.2019 121.358L63.9175 95.4314L16.2634 172.156V172.152Z' fill='%23231F20'/%3E%3Cpath d='M75.9626 64.7634C75.9626 64.7634 87.6946 47.7222 96.0022 23.6934C96.0022 23.6934 104.788 30.5034 101.673 54.3805L91.3224 85.2807L75.9626 64.7634Z' fill='%23A1B5B6'/%3E%3Cpath d='M91.8322 88.4629L74.1196 64.7988L74.7296 63.9144C74.8458 63.7466 86.4487 46.7441 94.5853 23.2059L95.2663 21.2339L96.9155 22.512C97.3028 22.8121 106.34 30.1289 103.151 54.5773L103.087 54.8581L91.8322 88.4629ZM77.7925 64.7116L90.8091 82.0982L100.204 54.0447C102.293 37.833 98.7165 29.8416 96.5444 26.6367C89.4342 46.2729 80.2035 61.0129 77.7893 64.7116H77.7925Z' fill='%23231F20'/%3E%3Cpath d='M47.5091 38.0621C48.2417 26.9757 51.9921 17.164 60.3062 9.37928C69.0914 1.15882 84.619 -2.35271 93.6431 7.39759C101.018 15.3663 102.035 27.9149 102.332 38.2267C101.954 25.2167 89.9379 11.1738 76.1435 12.3163C63.6047 13.3556 48.3773 24.923 47.5059 38.0621H47.5091Z' fill='%23A1B5B6'/%3E%3Cpath d='M100.84 38.2719C100.84 38.2719 100.84 38.2719 100.84 38.2654C100.647 31.5877 97.1254 24.4033 91.658 19.5136C87.0524 15.3953 81.585 13.3652 76.2725 13.8074C64.5889 14.7756 49.8198 25.8524 49.0033 38.1589L46.0178 37.9621C46.0178 37.9621 46.0178 37.9556 46.0178 37.9524C46.8408 25.5781 51.1786 15.873 59.2861 8.28835C65.0214 2.921 73.1547 -0.238728 80.4618 0.0130174C86.1422 0.21635 91.0803 2.41751 94.7467 6.38089C102.151 14.3819 104.255 26.6367 103.561 39.0755C103.561 39.082 100.843 38.2719 100.843 38.2719H100.84ZM77.7087 10.7542C83.302 10.7542 88.9017 13.036 93.6494 17.2802C96.0055 19.3845 98.0323 21.8826 99.6461 24.584C98.5552 18.3582 96.4993 12.681 92.5488 8.41099C89.4343 5.04793 85.2191 3.17597 80.3521 3.00169C73.7873 2.75317 66.5028 5.62565 61.3291 10.4669C57.1818 14.3464 54.0963 18.8391 52.0339 24.0257C58.0726 16.7154 67.6324 11.5159 76.024 10.8187C76.5855 10.7735 77.1471 10.7509 77.7087 10.7509V10.7542Z' fill='%23231F20'/%3E%3Cpath d='M120.357 202.316C120.357 202.316 140.6 160.275 140.6 160.271C141.19 159.048 133.296 149.156 132.547 148.017C126.492 138.802 120.512 129.533 114.373 120.376C102.376 102.483 90.6122 84.4314 79.0835 66.2315C69.6205 51.2914 64.7373 34.8053 64.7373 16.9927C64.7373 16.9927 47.1667 27.2626 47.6864 38.2264C48.2931 51.0719 49.1807 63.1751 55.0322 74.991C76.182 117.717 98.6035 159.855 120.344 202.287C120.338 202.274 120.357 202.313 120.357 202.313V202.316Z' fill='%23A1B5B6'/%3E%3Cpath d='M120.399 205.679L58.8472 85.5321C44.5719 63.3398 46.0372 40.1534 46.1889 38.2007C45.737 26.5075 63.2366 16.1407 63.9854 15.705L66.2382 14.3882V16.996C66.2382 48.6482 81.9109 68.2972 82.0691 68.4909L82.1595 68.6135L142.317 160.159L120.399 205.679ZM63.2818 19.7136C58.7374 22.8023 48.8193 30.4547 49.184 38.1588V38.2588L49.1808 38.3589C49.1614 38.5945 47.3346 62.1521 61.4066 83.9733L61.4808 84.1023L120.318 198.956L138.886 160.388L79.7001 70.3177C78.5479 68.8847 64.1048 50.2845 63.2818 19.7136Z' fill='%23231F20'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_2382_3'%3E%3Crect width='153' height='209.946' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
    background-size: cover;

    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: bounce 0.8s infinite ease-in-out;
    overflow: hidden;
    animation-delay: 0.1s;
}

/* Basketball seams */
.ball::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
}

/* Basketball texture */
.ball::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    /* background: radial-gradient(
        circle at 30% 30%,
        rgba(255, 175, 125, 0.8) 5%,
        rgba(255, 140, 80, 0.4) 30%,
        rgba(230, 114, 50, 0.2) 60%
    ); */
    background-size: 100% 100%;
}

.ball:nth-child(2) {
    animation-delay: 0.2s;
}
.ball:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%,
    100% {
        transform: translateY(0);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: translateY(40px);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
}

/* Floor shadow effect */
.floor {
    position: absolute;
    width: 100%;
    height: 10px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.1));
    bottom: -50px;
    left: 0;
    border-radius: 50%;
    filter: blur(3px);
}

/* button css */
.hoverbutton {
    cursor: pointer;
    transition: background-color 0.25s ease, box-shadow 0.25s ease,
        transform 0.2s ease;
    color: white;
}

.hoverbutton:hover::before {
    transform: translateX(100%);
}

.hoverbutton:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.hoverbutton {
    background-color: currentColor;
}

.hoverbutton.orange {
    background-color: #f05522;
}

.hoverbutton.blue {
    background-color: #154da3;
}

.hoverbutton.orange:hover {
    background-color: #154da3;
}

.hoverbutton.blue:hover {
    background-color: #f05522;
}

/* checkbox css */
input[type="checkbox"]:checked {
    background-color: #f05522;
    border-color: #f05522;
}
/* input focus */
.form-control:focus {
    border-color: #19345e !important;
    box-shadow: 0 0 0 0.25rem rgba(3, 48, 121, 0.25) !important;
    outline: none !important;
}

/* tinymce css */
.tox-tinymce {
    border: 1px solid #19345e !important;
    border-radius: 4px !important;
    overflow: hidden;
}

.tox-tinymce:focus-within {
    border-color: #19345e !important;
    box-shadow: 0 0 0 0.2rem rgba(21, 77, 163, 0.25) !important;
}

.tox-tinymce.is-invalid {
    border: 1px solid #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

input.is-invalid,
textarea.is-invalid,
select.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.cta.orange {
    width: 47px !important;
    height: 33px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
    border: none;
}

.cta-button.clicked {
    transform: scale(0.95);
    opacity: 0.8;
    transition: transform 0.2s ease, opacity 0.2s ease;
}

.cta.orange.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    width: 47px !important;
    height: 33px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
    border: none;
}

.activities-container {
    transition: opacity 0.3s ease;
    min-height: 300px;
}

@media (min-width: 992px) {
    .header .site_nav li + li {
        margin-left: 0.5rem;
    }
    .header .site_nav li a {
        padding: 0.5rem 0.3rem;
    }
}
@media (min-width: 1200px) {
    .header .site_nav li + li {
        margin-left: 0.8rem;
    }
    .header .site_nav li a {
        padding: 0.5rem 0.5rem;
    }
}
