 import { createSlider } from '../appTwo.js';

const passwordInput = document.getElementById("password");
const toggleButton = document.querySelector(".password-toggle");

let isPasswordVisible = false;

toggleButton.addEventListener("click", function () {
    isPasswordVisible = !isPasswordVisible;
    passwordInput.type = isPasswordVisible ? "text" : "password";

    toggleButton.innerHTML = isPasswordVisible
        ? `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye" viewBox="0 0 16 16">
                                                   <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8M1.173 8a13 13 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5s3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5s-3.879-1.168-5.168-2.457A13 13 0 0 1 1.172 8z"/>
                                                   <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5M4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"/>
                                                 </svg> `
        : `
   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye-slash" viewBox="0 0 16 16">
                                                   <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7 7 0 0 0-2.79.588l.77.771A6 6 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755q-.247.248-.517.486z"/>
                                                   <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829"/>
                                                   <path d="M3.35 5.47q-.27.24-.518.487A13 13 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7 7 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12z"/>
                                                 </svg>
   `;
});

const loginForm = document.getElementById("loginForm");
const loginButton = loginForm
    ? loginForm.querySelector('button[type="submit"]')
    : null;
const emailInput = document.getElementById("email");

function checkInputsNotEmpty() {
    const email = emailInput ? emailInput.value.trim() : "";
    const password = passwordInput ? passwordInput.value.trim() : "";
    return email !== "" && password !== "";
}

if (loginForm && loginButton) {
    loginForm.addEventListener("submit", function (event) {
        event.preventDefault();

        if (!checkInputsNotEmpty()) {
            email.classList.add("was-validated");
            return;
        }

        const originalText = loginButton.innerHTML;

        loginButton.classList.add("btn-loading");
        loginButton.disabled = true;

        const originalMouseOver = loginButton.onmouseover;
        const originalMouseOut = loginButton.onmouseout;

        loginButton.onmouseover = null;
        loginButton.onmouseout = null;

        if (!this.checkValidity()) {
            event.stopPropagation();

            loginButton.classList.remove("btn-loading");
            loginButton.disabled = false;
            loginButton.innerHTML = originalText;
            loginButton.onmouseover = originalMouseOver;
            loginButton.onmouseout = originalMouseOut;
        } else {
            setTimeout(() => {
                loginForm.submit();
            }, 500);
        }
    });
}

if (emailInput) {
    emailInput.addEventListener("input", function () {
        this.classList.remove("is-valid");
        this.classList.remove("is-invalid");

        const existingFeedback = this.nextElementSibling;
        if (
            existingFeedback &&
            existingFeedback.classList.contains("invalid-feedback")
        ) {
            existingFeedback.remove();
        }
    });

    emailInput.addEventListener("blur", function () {
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        const value = this.value.trim();

        this.classList.remove("is-valid");
        this.classList.remove("is-invalid");

        const existingFeedback = this.nextElementSibling;
        if (
            existingFeedback &&
            existingFeedback.classList.contains("invalid-feedback")
        ) {
            existingFeedback.remove();
        }

        if (value !== "") {
            if (!emailPattern.test(value)) {
                this.classList.add("is-invalid");

                let feedback = document.createElement("div");
                feedback.classList.add("invalid-feedback");
                feedback.textContent = "Please enter a valid email address.";
                this.parentNode.appendChild(feedback);
            } else {
                this.classList.add("is-valid");
            }
        }
    });
}

if (passwordInput) {
    passwordInput.classList.remove("is-valid");

    passwordInput.addEventListener("input", function () {
        this.classList.remove("is-valid");

    });

    passwordInput.addEventListener("blur", function () {
        this.classList.remove("is-valid");

    });
}
