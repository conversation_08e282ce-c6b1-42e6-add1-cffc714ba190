- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet Home';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/secHead.pug
include ../layouts/leaderBox.pug
include ../partials/footer.pug

html(lang="en")
	+head(pageTitle)

	body
		+header()

		section.hero
			.container-fluid.gx-0
				.hero-img
					img.w-100(src="images/hero-img.jpg", alt="")
			.hero__text
				.container
					.row
						.col-md-8
							h1.text-uppercase.mb-3 A Shot for Life #[strong.d-block Gauntlet]
							p #[strong Join the A Shot for Life Gauntlet:] Fundraise, Inspire, and Make a Difference in the Fight Against Cancer!
							.cta-row
								a.cta.orange(href="#") Register
								a.cta.blue(href="#") Donate

		section.achieve-goal
			.container-fluid.gx-0
				.bg-blue
					.container
						.meta-timeline.d-flex.justify-content-between
							.meta.text-center
								h2 Help Us Achieve Our Goal:#[br] #[strong $250,000]
							.meta.text-center
								h2 Active Participants:#[br] #[strong 351]
						.time-line.mt-5
							.meta-list.d-flex.justify-content-between
								- const timelineLabels = ['Let’s Go!', 'Keep Going!', 'Almost there!', 'You Did it!']
								each label, index in timelineLabels
									- const classes = ['label']
									if index === 0
										- classes.push('first')
									else if index === timelineLabels.length - 1
										- classes.push('last')
									span(class=classes.join(' '))= label
							.bar.mt-4
								span.fill.align-items-center.d-flex.justify-content-end(style="width:80%") #[span.sum $179,850]

		section.sec.leaderboard
			.container
				+secHead('Leaderboard')
				.row.justify-content-center
					+leaderBox('lbp1', 'Brady Manning', 4750, 1000)
					+leaderBox('lbp2', 'Michael Bryant', 4625, 2000)
					+leaderBox('lbp3', 'Stephen James', 4125, 1500)
					+leaderBox('lbp4', 'Wayne Lemieux', 3750, 3000)

					.cta-row.mt-5.text-center
						a.cta.orange(href="leaderboard.html") See More

		section.hording
			.container-fluid.gx-0
				.img
					img.w-100(src="images/hp1.jpg", alt="")
			.hording__text
				.container
					.row.justify-content-md-end
						.col-md-7.text-md-end.py-5.d-flex.flex-column.align-items-md-end
							h2.text-uppercase.mb-0 What is A Shot for Life #[strong.d-md-block Gauntlet?]
							.p-size.my-3.my-lg-4
								p The A Shot for Life Gauntlet is your chance to step up, unleash your inner champion, and score big in the fight against cancer with every dollar you raise. Join a thrilling showdown of grit and heart, where your passion fuels a game-changing impact for an unbeatable cause!
							.cta-row
								a.cta.orange(href="#") Register Now
								a.cta.blue(href="#") Find Out More

		section.sec.recent-activities
			.container
				+secHead('Recent Activity', false, 'mb-5 mb-md-0')
				.row.activity-head.text-uppercase.d-none.d-md-flex.mt-5
					.col-md-3.donor.ps-5 Donor
					.col-md-3.athlete Athlete
					.col-md-1.amount Amount
					.col-md.comment.pe-md-5 Comment

				mixin activityRow(donor, athlete, amount, comment)
					.row.activity-row.d-flex.align-items-center.mt-2
						.col-md-3.label.py-2.py-md-3.donor.ps-md-5(title='Donor')=donor
						.col-md-3.label.py-2.py-md-3.athlete(title='Athlete')=athlete
						.col-md-1.label.py-2.py-md-3.amount(title='Amount') $#{amount}
						.col-md.label.py-2.py-md-3.comment.pe-md-5(title='Comment')=comment
				+activityRow('Joan Smith', 'Michael Bryant', '50', 'Way to go Michael!')
				+activityRow('Ted Jones', 'Robert James', '25', 'Let’s go Bobby!')
				+activityRow('Lorna Haller', 'Ali McCarthy', '50', 'We’re so proud of you. We can’t wait to see what you do next. Keep up the good work. Good luck!')
				+activityRow('David Munn', 'Mike Gluck', '50', 'We’re so proud of you.')
				+activityRow('Oliver Bates', 'Megan Lailer', '500', 'She shoots, she scores!')
				+activityRow('Ray Winslow', 'Andy Sadler', '500', 'Way to go Andy. Very proud of all you are accomplishing, keep going!')

				.cta-row.mt-5.text-center
					a.cta.orange(href="#") #[img(src="images/arrow-cta-left.svg", alt="") ]
					a.cta.orange(href="#") #[img(src="images/arrow-cta-right.svg", alt="") ]

		section.hording
			.container-fluid.gx-0
				.img
					img.w-100(src="images/hp2.jpg", alt="")
			.hording__text
				.container
					.row
						.col-md-7.py-5
							h2.text-uppercase.mb-0 Our #[strong Impact]
							.p-size.my-3.my-lg-4
								p The tremendous support from A Shot For Life is instrumental in enabling our group to execute studies that enhance our understanding of the immune system in the context of malignant brain tumors and provides hope that we can continue to provide meaningful, new therapies for our patients. Thanks to ASFL for leading the way and making it all possible.​
							.cta-row
								a.cta.blue(href="#") Find Out More

		section.make-difference
			.container-fluid.gx-0
				.bg-blue.text-center
					.container
						h2.text-uppercase.mb-4 How to make #[strong a difference!]
						p Join the A Shot for Life Gauntlet: Fundraise, Inspire, and#[br.d-none.d-md-block] Make a Difference in the Fight Against Cancer!
						.cta-row.mt-5
							a.cta.orange(href="#") Register
							a.cta.blue(href="#") Donate

		+footer()
