
mixin header
    header.header.py-3
        .container.d-flex.align-items-center
            .brand
                a(href="/") #[img(src="images/logo.png", alt="", height="126", width="165") ]
            #navCall.call_nav.align-items-center.d-flex.flex-column.d-lg-none.justify-content-center.ms-auto
                -for (i=0; i<3; i++)
                    span.line
            nav#siteNav.site_nav.ms-lg-auto.d-none.d-lg-flex.align-items-center
                ul.nav.text-uppercase
                    - const pages = ['ASFL Home', 'Gauntlet Home', 'About', 'Register', 'Donate', 'Leaderboard', 'Contact']
                    each page, index in pages
                        - const isHome = index === 1
                        - const otherPage = index === 2 || index === 3 || index === 4 || index === 5 || index === 6
                        - let href = '#'
                        if isHome
                            - href = '/'
                        else if otherPage
                            - href = `${page.replaceAll(' ', '-').toLowerCase()}.html`
                        li(class=(pageName === page || heroName === page ? 'active' : ''))
                            a(href=href)= page

