.achieve-goal {
	.bg-blue {
		background: #19345e;
		padding-block: 1.25rem 2.5rem;

		@media (min-width: 768px) {
			padding-block: 2.25rem 3.5rem;
		}
	}

	.meta-timeline {
		h2 {
			color: #fff;
			font: 700 16px $anaheim;

			@media (min-width: 768px) {
				font-size: 20px;
			}

			@media (min-width: 1240px) {
				font-size: 28px;
			}

			strong {
				color: #f05522;
				font-size: 140%;
				font-weight: 700;
			}
		}
	}

	.time-line {
		.meta-list {
			.label {
				color: #fff;
				font: 700 12px $inter;
				position: relative;

				@media (min-width: 768px) {
					font-size: 15px;
				}

				&:after {
					background: #fff;
					content: '';
					height: 45px;
					left: 50%;
					position: absolute;
					top: calc(100% + 10px);
					transform: translateX(-50%);
					width: 2px;

					@media (min-width: 768px) {
						width: 3px;
					}
				}

				&.first {
					&:after {
						left: 2px;
					}
				}

				&.last {
					&:after {
						left: calc(100% - 2px);
					}
				}
			}
		}

		.bar {
			background: #19345e;
			border: 2px solid #fff;
			border-radius: 50px;
			height: 50px;
			padding: 0.5rem 0.8rem;
			position: relative;
			z-index: 1;

			@media (min-width: 768px) {
				border-width: 3px;
				height: 60px;
				padding: 0.75rem 1rem;
			}

			.fill {
				background: #f05522;
				border-radius: 30px;
				height: 100%;
				padding: 0 1rem;
			}

			.sum {
				color: #fff;
				font: 700 18px/1 $inter;

				@media (min-width: 768px) {
					font-size: 24px;
				}
			}
		}
	}
}
