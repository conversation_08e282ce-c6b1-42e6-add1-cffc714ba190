@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

:root {
    --primary: #f05522;
    --primary-light: rgba(240, 85, 34, 0.1);
    --primary-gradient: linear-gradient(135deg, #f05522 0%, #ff7a50 100%);
    --secondary: #19345e;
    --secondary-light: rgba(25, 52, 94, 0.1);
    --secondary-gradient: linear-gradient(135deg, #19345e 0%, #2c5491 100%);
    --white: #ffffff;
    --light-bg: #f9fafc;
    --success: #00c389;
    --warning: #ffb547;
    --danger: #ff5a5a;
    --card-shadow: 0 10px 30px rgba(25, 52, 94, 0.07);
    --hover-shadow: 0 15px 35px rgba(25, 52, 94, 0.12);
}

body {
    font-family: "Poppins", sans-serif;
    background-color: var(--light-bg);
    color: #444;
    overflow-x: hidden;
}

.header-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: float 15s linear infinite;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c9c9c9;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Header Section */
.dashboard-header {
    background: var(--secondary-gradient);
    padding: 30px 40px;
    border-radius: 12px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: "";
    position: absolute;
    right: -50px;
    top: -50px;
    width: 300px;
    height: 300px;
    background: var(--primary);
    opacity: 0.1;
    border-radius: 50%;
}

.dashboard-header::after {
    content: "";
    position: absolute;
    left: 40%;
    bottom: -80px;
    width: 200px;
    height: 200px;
    background: var(--primary);
    opacity: 0.05;
    border-radius: 50%;
}

.dashboard-title {
    color: var(--white);
    font-weight: 700;
    margin-bottom: 15px;
    font-size: 2.2rem;
    position: relative;
    z-index: 5;
}

.export-btn {
    background: var(--primary-gradient);
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    color: var(--white);
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(240, 85, 34, 0.3);
    transition: all 0.3s ease;
    position: relative;
    z-index: 5;
}

.export-btn:hover {
    box-shadow: 0 8px 20px rgba(240, 85, 34, 0.4);
    transform: translateY(-2px);
}

.export-btn i {
    margin-right: 8px;
}

.ripple {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple-effect 0.6s linear;
    width: 100px;
    height: 100px;
}

@keyframes ripple-effect {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Card Styling */
.stat-card {
    background: var(--white);
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: none;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--hover-shadow);
    transform: translateY(-5px);
}

.stat-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 16px 0 0 16px;
    opacity: 0;
    transition: all 0.3s ease;
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-card.premium::after {
    background: var(--secondary-gradient);
}

.stat-card .card-title {
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 1px;
    color: #97a4b7;
    margin-bottom: 12px;
    font-weight: 600;
}

.stat-card .stat-value {
    color: var(--secondary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.stat-icon {
    width: 54px;
    height: 54px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    color: var(--primary);
    font-size: 1.5rem;
}

.stat-card.premium .stat-icon {
    background: var(--secondary-light);
    color: var(--secondary);
}

.trend-indicator {
    background: rgba(0, 195, 137, 0.1);
    color: var(--success);
    padding: 5px 10px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.8rem;
    display: inline-block;
}

.trend-text {
    color: #97a4b7;
    font-size: 0.85rem;
    margin-left: 5px;
}

/* Chart Card Styling */
.chart-card {
    background: var(--white);
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: all 0.4s ease;
    border: none;
    height: 100%;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.chart-card:hover {
    box-shadow: var(--hover-shadow);
}

.chart-card::before {
    content: "";
    position: absolute;
    width: 150px;
    height: 150px;
    background: var(--primary);
    opacity: 0.03;
    border-radius: 50%;
    bottom: -50px;
    right: -50px;
    transition: all 0.4s ease;
}

.chart-card:hover::before {
    transform: scale(1.2);
}

.chart-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
    background: transparent;
    padding: 0 0 20px 0;
}

.chart-card .card-title {
    color: var(--secondary);
    font-weight: 600;
    font-size: 1.2rem;
    margin: 0;
}

.chart-card .card-link {
    color: var(--primary);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.chart-card .card-link:hover {
    opacity: 0.8;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Table Styling */
.data-table {
    width: 100%;
}

.data-table th {
    font-weight: 600;
    padding: 15px;
    color: var(--secondary);
    border: none;
    background: var(--secondary-light);
    font-size: 0.9rem;
}

.data-table td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: #444;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background: var(--light-bg);
}

.donor-avatar {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 12px;
}

.donor-avatar.primary {
    background: var(--primary-light);
    color: var(--primary);
}

.donor-avatar.anonymous {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.donor-name {
    font-weight: 500;
}

/* Donor Stats Section */
.donor-stat-item {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.donor-stat-item:last-child {
    margin-bottom: 0;
}

.stat-indicator {
    width: 12px;
    height: 12px;
    border-radius: 4px;
    margin-right: 12px;
}

.stat-indicator.primary {
    background: var(--secondary);
}

.stat-indicator.secondary {
    background: var(--primary);
}

.stat-indicator.tertiary {
    background: #6c757d;
}

.stat-label {
    color: #97a4b7;
    font-size: 0.9rem;
}

.stat-number {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--secondary);
    margin-top: 2px;
}

/* Time Period Section */
.time-period-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.time-period-item:last-child {
    margin-bottom: 0;
}

.period-label {
    color: #97a4b7;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.period-value {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--secondary);
}

.progress-container {
    width: 40%;
}

.custom-progress {
    height: 8px;
    border-radius: 4px;
    background: rgba(240, 85, 34, 0.15);
    overflow: hidden;
}

.progress-bar-custom {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .dashboard-header {
        padding: 20px;
    }

    .dashboard-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 20px;
    }

    .chart-card {
        margin-bottom: 20px;
    }

    .progress-container {
        width: 30%;
    }
}

/* Animation Effects */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated {
    animation: fadeIn 0.6s ease forwards;
}

.delay-1 {
    animation-delay: 0.1s;
}
.delay-2 {
    animation-delay: 0.2s;
}
.delay-3 {
    animation-delay: 0.3s;
}
.delay-4 {
    animation-delay: 0.4s;
}
.delay-5 {
    animation-delay: 0.5s;
}

.data-table th,
.data-table td {
    vertical-align: middle;
}

.data-table th {
    font-weight: 600;
    color: #19345e;
    border-bottom: 2px solid #e9ecef;
}

.user-table th:nth-child(1),
.data-table td:nth-child(1) {
    width: 18%;
}
.user-table th:nth-child(2),
.data-table td:nth-child(2) {
    width: 20%;
}
.user-table th:nth-child(3),
.data-table td:nth-child(3) {
    width: 18%;
}
.user-table th:nth-child(4),
.data-table td:nth-child(4) {
    width: 20%;
}
.user-table th:nth-child(5),
.data-table td:nth-child(5) {
    width: 12%;
}
.user-table th:nth-child(6),
.data-table td:nth-child(6) {
    width: 12%;
}

.user-table {
    width: 100%;
    table-layout: fixed;
}

/* Handle text overflow */
.user-table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pagination .page-item .page-link {
    border-radius: 50%;
    margin: 0 5px;
    color: #19345e;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}
.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #f05522 0%, #f05522 100%);
    color: white;
    box-shadow: 0 4px 10px #fab7a1;
}
.pagination .page-item .page-link:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
}
.pagination .page-item.active .page-link:hover {
    background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
}

#search:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}
