<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DuplicatePaymentPreventionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user for donations
        $this->testUser = User::factory()->create([
            'slug' => 'test-user',
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_payment_intent_creation()
    {
        $paymentData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'city' => 'New York',
            'state' => 'NY',
            'address' => '123 Main St',
            'amount' => 50,
            'donatedTo' => 'test-user',
            'message' => 'Test donation'
        ];

        // Mock <PERSON> to avoid actual API calls
        $this->mockStripe();

        // First request should create a new payment intent
        $response1 = $this->postJson('/create-payment-intent', $paymentData);
        $response1->assertStatus(200);
        $response1->assertJsonStructure(['clientSecret']);

        // Second request with same data should return the cached payment intent
        $response2 = $this->postJson('/create-payment-intent', $paymentData);
        $response2->assertStatus(200);
        $response2->assertJsonStructure(['clientSecret']);

        // Both responses should have the same client secret
        $this->assertEquals(
            $response1->json('clientSecret'),
            $response2->json('clientSecret')
        );
    }

    /** @test */
    public function it_prevents_duplicate_transaction_storage()
    {
        $paymentIntentId = 'pi_test_123456789';
        
        $transactionData = [
            'amount' => 50,
            'donatedTo' => 'test-user',
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'address' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'message' => 'Test donation',
            'paymentIntentId' => $paymentIntentId
        ];

        // Mock Stripe PaymentIntent retrieval
        $this->mockStripePaymentIntent($paymentIntentId);

        // First request should create a new transaction
        $response1 = $this->postJson('/store-transaction', $transactionData);
        $response1->assertStatus(201);
        $response1->assertJson(['success' => true]);

        // Verify transaction was created
        $this->assertDatabaseHas('transactions', [
            'payment_intent_id' => $paymentIntentId,
            'amount' => 50
        ]);

        // Second request with same payment intent should return success without creating duplicate
        $response2 = $this->postJson('/store-transaction', $transactionData);
        $response2->assertStatus(200);
        $response2->assertJson(['success' => true]);

        // Verify only one transaction exists
        $this->assertEquals(1, Transaction::where('payment_intent_id', $paymentIntentId)->count());
    }

    /** @test */
    public function it_allows_different_users_to_make_same_amount_donations()
    {
        $paymentData1 = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'city' => 'New York',
            'state' => 'NY',
            'address' => '123 Main St',
            'amount' => 50,
            'donatedTo' => 'test-user',
            'message' => 'Test donation'
        ];

        $paymentData2 = [
            'firstName' => 'Jane',
            'lastName' => 'Smith',
            'email' => '<EMAIL>', // Different email
            'city' => 'New York',
            'state' => 'NY',
            'address' => '456 Oak St',
            'amount' => 50, // Same amount
            'donatedTo' => 'test-user',
            'message' => 'Test donation'
        ];

        $this->mockStripe();

        // Both requests should succeed and create different payment intents
        $response1 = $this->postJson('/create-payment-intent', $paymentData1);
        $response2 = $this->postJson('/create-payment-intent', $paymentData2);

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        // Should have different client secrets
        $this->assertNotEquals(
            $response1->json('clientSecret'),
            $response2->json('clientSecret')
        );
    }

    /** @test */
    public function it_allows_same_user_to_donate_after_time_window()
    {
        $paymentData = [
            'firstName' => 'John',
            'lastName' => 'Doe',
            'email' => '<EMAIL>',
            'city' => 'New York',
            'state' => 'NY',
            'address' => '123 Main St',
            'amount' => 50,
            'donatedTo' => 'test-user',
            'message' => 'Test donation'
        ];

        $this->mockStripe();

        // First request
        $response1 = $this->postJson('/create-payment-intent', $paymentData);
        $response1->assertStatus(200);

        // Clear cache to simulate time window expiration
        Cache::flush();

        // Second request after time window should create new payment intent
        $response2 = $this->postJson('/create-payment-intent', $paymentData);
        $response2->assertStatus(200);

        // Should have different client secrets
        $this->assertNotEquals(
            $response1->json('clientSecret'),
            $response2->json('clientSecret')
        );
    }

    private function mockStripe()
    {
        // Mock Stripe PaymentIntent creation
        $this->mock(\Stripe\PaymentIntent::class, function ($mock) {
            $mock->shouldReceive('create')
                ->andReturn((object) [
                    'id' => 'pi_test_' . uniqid(),
                    'client_secret' => 'pi_test_' . uniqid() . '_secret_test'
                ]);
        });
    }

    private function mockStripePaymentIntent($paymentIntentId)
    {
        $this->mock(\Stripe\PaymentIntent::class, function ($mock) use ($paymentIntentId) {
            $mock->shouldReceive('retrieve')
                ->with($paymentIntentId)
                ->andReturn((object) [
                    'id' => $paymentIntentId,
                    'payment_method_types' => ['card']
                ]);
        });
    }
}
