<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Donor;
use Illuminate\Support\Facades\Log;

class HandleStripePaymentSucceeded implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */

    public $invoice;
    public function __construct($invoice)
    {
        $this->invoice = $invoice;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::transaction(function () {
            $subscriptionId = $this->invoice->subscription;
            $amount = $this->invoice->amount_paid / 100;
            $paymentIntentId = $this->invoice->payment_intent;

            // Always update subscription status regardless of transaction existence
            $updatedRows = DB::table('subscriptions')
                ->where('subscription_id', $subscriptionId)
                ->update([
                    'status' => 'active',
                    'next_payment_at' => date('Y-m-d H:i:s', $this->invoice->lines->data[0]->period->end),
                    'updated_at' => now(),
                ]);

            // Log if subscription was not found
            if ($updatedRows === 0) {
                Log::warning('Subscription not found in database during payment success webhook', [
                    'subscription_id' => $subscriptionId,
                    'invoice_id' => $this->invoice->id,
                ]);
            }

            // Only create transaction if it doesn't already exist
            if (!DB::table('transactions')->where('payment_intent_id', $paymentIntentId)->exists()) {
                DB::table('transactions')->insert([
                    'payment_intent_id' => $this->invoice->payment_intent,
                    'amount' => $amount,
                    'currency' => $this->invoice->currency,
                    'status' => 'success',
                    'payment_method' => $this->invoice->payment_method_types[0] ?? 'card',
                    'description' => 'Recurring donation',
                    'metadata' => null,
                    'is_refunded' => false,
                    'refund_amount' => 0,
                    'refund_id' => null,
                    'refunded_at' => null,
                ]);
            }

            $existingDonor = Donor::where('subscription_id', $subscriptionId)->first();
            if ($existingDonor) {
                Donor::create([
                    'name' => $existingDonor->name,
                    'email' => $existingDonor->email,
                    'address' => $existingDonor->address,
                    'city' => $existingDonor->city,
                    'state' => $existingDonor->state,
                    'message_for_fundraiser' => $existingDonor->message_for_fundraiser,
                    'amount_donate' => $amount,
                    'anonymous_for_public' => $existingDonor->anonymous_for_public,
                    'anonymous_for_all' => $existingDonor->anonymous_for_all,
                    'payment_frequency' => $existingDonor->payment_frequency,
                    'customer_id' => $existingDonor->customer_id,
                    'subscription_id' => $existingDonor->subscription_id,
                ]);
            }
        });
    }
}
