{{-- <section class="recent-donors py-5" style="background: linear-gradient(135deg, #f4f7fc 0%, #e6eeff 100%);">
    <div class="container">
        <h2 class="text-center mb-4 fw-bold" style="color: #19345e; font-family: 'Anaheim', sans-serif; font-size: 2.5rem; position: relative;">
            <i class="fas fa-users me-3" style="color: #f05522;"></i>Recent Donors
            <span class="position-absolute w-25 h-1 rounded"
                style="bottom: -10px; left: 50%; transform: translateX(-50%); height: 3px; background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%) !important;"></span>
        </h2>

        <div class="row mb-4">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="search-wrapper position-relative">
                    <i class="fas fa-search position-absolute" style="left: 15px; top: 50%; transform: translateY(-50%); color: #f05522;"></i>
                    <input type="text" wire:model.live="search" placeholder="Search by name"
                        class="form-control py-3 ps-5 rounded-pill border-0"
                        style="box-shadow: 0 4px 15px rgba(240, 85, 34, 0.1); font-family: 'Inter', sans-serif; font-size: 1rem;">
                </div>
            </div>
        </div>

        <div class="card border-0 rounded-lg shadow-lg" style="border-radius: 20px; overflow: hidden;">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead style="background: linear-gradient(135deg, #19345e 0%, #154da3 100%);">
                            <tr>
                                <th class="py-4 text-white fw-normal border-0" style="font-family: 'Inter', sans-serif; font-weight: 600;">Name</th>
                                <th class="py-4 text-white fw-normal border-0" style="font-family: 'Inter', sans-serif; font-weight: 600;">Amount</th>
                                <th class="py-4 text-white fw-normal border-0" style="font-family: 'Inter', sans-serif; font-weight: 600;">Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{-- Check if there are donors --}}
                            {{-- @forelse ($recentDonors as $donor)
                                <tr class="align-middle" style="transition: all 0.3s ease; border-bottom: 1px solid rgba(240, 85, 34, 0.1);">
                                    <td class="border-0 py-4">
                                        <div class="d-flex align-items-center">
                                            <div class="donor-avatar me-3">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 40px; height: 40px; background: linear-gradient(135deg, #f05522 0%, #ff7a50 100%); color: white; font-weight: bold; font-family: 'Inter', sans-serif;">
                                                    {{ strtoupper(substr($donor['name'], 0, 1)) }}
                                                </div>
                                            </div>
                                            <span class="fw-medium text-dark" style="font-family: 'Inter', sans-serif; font-weight: 600;">{{ $donor['name'] }}</span>
                                        </div>
                                    </td>
                                    <td class="border-0 py-4">
                                        <span class="fw-bold" style="color: #f05522; font-family: 'Anaheim', sans-serif; font-size: 1.2rem;">
                                            ${{ number_format($donor['amount'], 2) }}
                                        </span>
                                    </td>
                                    <td class="border-0 py-4 text-muted" style="font-family: 'Inter', sans-serif;">
                                        <i class="fas fa-calendar-alt me-2" style="color: #154da3;"></i>
                                        {{ \Carbon\Carbon::parse($donor['created_at'])->format('F d, Y') }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="3" class="text-center py-5 text-muted border-0">
                                        <div class="py-4">
                                            <div class="empty-state-icon mb-3">
                                                <i class="fas fa-search fa-3x" style="color: #d1d1d1;"></i>
                                            </div>
                                            <h5 style="color: #6c7a90; font-family: 'Inter', sans-serif; font-weight: 600;">No donors found</h5>
                                            <p class="mb-0" style="color: #6c7a90; font-family: 'Inter', sans-serif;">Start sharing your fundraising page to receive donations!</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div> --}}

        {{-- Pagination Links with Custom Styling --}}
        {{-- @if($recentDonors->hasPages())

            <div class="mt-4 d-flex justify-content-center">
                <div class="pagination-wrapper">
                    {{ $recentDonors->links('pagination::bootstrap-5') }}
                </div>
            </div>
        @endif
    </div>
</section> --}}
<section class="sec recent-activities">
    <div class="container">
        <div class="head text-center mb-5 mb-md-0">
            <h2 class="text-uppercase">Recent Activity</h2>
        </div>


         <div class="row mb-4">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="search-wrapper position-relative">
                    <i class="fas fa-search position-absolute" style="left: 15px; top: 50%; transform: translateY(-50%); color: #f05522;"></i>
                    <input type="text" wire:model.live="search" placeholder="Search by name or email"
                        class="form-control py-3 ps-5 rounded-pill border-0"
                        style="box-shadow: 0 4px 15px rgba(240, 85, 34, 0.1); font-family: 'Inter', sans-serif; font-size: 1rem;">
                </div>
            </div>
        </div>

        @if($recentDonors->count() > 0)
            <div class="row activity-head text-uppercase d-none d-md-flex mt-5">
                <div class="col-md-3 donor ps-5">Donor</div>
                <div class="col-md-3 date">Date</div>
                <div class="col-md-2 amount">Amount</div>
                <div class="col-md-4 comment pe-md-5">Comment</div>
            </div>

            <div wire:loading.class="opacity-50" class="activities-container">
                @foreach($recentDonors as $donor)
                    <div class="row activity-row d-flex align-items-center mt-2 donor-row">
                        <div class="col-md-3 label py-2 py-md-3 donor ps-md-5" title="Donor">{{ $donor['name'] }}</div>
                       <div class="col-md-3 label py-2 py-md-3 athlete" title="date">
                                {{ \Carbon\Carbon::parse($donor['created_at'])->format('F j Y') }}
                            </div>
                        <div class="col-md-2 label py-2 py-md-3 amount" title="Amount">${{ number_format($donor['amount'], 2) }}</div>
                        <div class="col-md-4 label py-2 py-md-3 comment pe-md-5" title="Comment">{{ $donor['message'] ?? '' }}</div>
                    </div>
                @endforeach
            </div>

            <div class="cta-row mt-5 text-center">
                @if($recentDonors->previousPageUrl())
                    <button class="cta orange" wire:click.prevent="previousPage" wire:loading.attr="disabled">
                        <img src="{{asset('images/arrow-cta-left.svg')}}" alt="Previous Page" />
                    </button>
                @else
                    <button class="cta orange disabled">
                        <img src="{{asset('images/arrow-cta-left.svg')}}" alt="No Previous Page" />
                    </button>
                @endif

                @if($recentDonors->nextPageUrl())
                    <button class="cta orange" wire:click.prevent="nextPage" wire:loading.attr="disabled">
                        <img src="{{asset('images/arrow-cta-right.svg')}}" alt="Next Page" />
                    </button>
                @else
                    <button class="cta orange disabled">
                        <img src="{{asset('images/arrow-cta-right.svg')}}" alt="No Next Page" />
                    </button>
                @endif
            </div>
        @else
            <div class="row justify-content-center">
                <div class="col-12 text-center py-5">
                    <i class="fas fa-hand-holding-heart fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">No donation activity found.</p>
                </div>
            </div>
        @endif
    </div>
</section>



