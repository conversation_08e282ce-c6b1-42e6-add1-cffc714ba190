form {
	.form-label {
		color: #19345e;
		font: 700 16px $inter;

		@media (min-width: 768px) {
			font-size: 17px;
		}
	}

	.form-control {
		border-radius: 30px;
		height: 46px;

		&[type='file'] {
			line-height: 33px;
		}
	}

	.form-control,
	.form-check-input[type='checkbox'] {
		border-color: #696969;
	}

	.form-check-label {
		color: #363636;
		font: 15px $inter;

		@media (min-width: 768px) {
			font-size: 17px;
		}
	}

	textarea.form-control {
		min-height: 120px;
	}

	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
		margin: 0;
	}

	input[type='number'] {
		appearance: textfield;
	}

	a {
		color: #000;
	}
}
