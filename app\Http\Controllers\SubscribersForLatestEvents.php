<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SubscriberForLatestEvents;
use Illuminate\Support\Facades\Validator;

class SubscribersForLatestEvents extends Controller
{
    /**
     * Subscribe a user to the newsletter
     */
    public function subscribeForLatestEvents(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:subscribers_for_latest_events,email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'This email is already subscribed or is invalid.'
            ], 422);
        }

        try {
            SubscriberForLatestEvents::create([
                'email' => $request->email,
                'active' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Thank you! You have successfully subscribed to our newsletter'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request.'
            ], 500);
        }
    }

    /**
     * Unsubscribe a user from the newsletter
     */
    public function unSubscribeForLatestEvents(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid email address.'
            ], 422);
        }

        try {
            $subscription = SubscriberForLatestEvents::where('email', $request->email)->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'This email is not subscribed to our newsletter.'
                ], 404);
            }

            $subscription->active = false;
            $subscription->save();

            return response()->json([
                'success' => true,
                'message' => 'You have been successfully unsubscribed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request.'
            ], 500);
        }
    }
}
