
<header class="header py-3">
    <div class="container d-flex align-items-center">
        <div class="brand">
            <a href="/"><img src="{{asset('images/logo.png')}}" alt="logo" height="126" width="165" /></a>
        </div>
        <div class="call_nav align-items-center d-flex flex-column d-lg-none justify-content-center ms-auto" id="navCall"><span class="line"></span><span class="line"></span><span class="line"></span></div>
        <nav class="site_nav ms-lg-auto d-none d-lg-flex align-items-center" id="siteNav">
            <ul class="nav text-uppercase">
                <li class="{{ request()->is('/asfl-home') ? 'active' : '' }}">
                    <a href="https://ashotforlife.org" target="blank">ASFL Home</a>
                </li>
                <li class="{{ request()->routeIs('home') ? 'active' : '' }}">
                    <a href="{{ route('home') }}">Gauntlet Home</a>
                </li>
                <li class="{{ request()->routeIs('about') ? 'active' : '' }}">
                    <a href="{{ route('about') }}">About</a>
                </li>
                <li class="{{ request()->routeIs('register') ? 'active' : '' }}">
                    <a href="{{ route('register') }}">Register</a>
                </li>
                <li class="{{ request()->routeIs('donate') ? 'active' : '' }}">
                    <a href="{{ route('donate') }}">Donate</a>
                </li>
                <li class="{{ request()->routeIs('leaderBoard') ? 'active' : '' }}">
                    <a href="{{ route('leaderBoard') }}">Leaderboard</a>
                </li>
                <li class="{{ request()->routeIs('contact') ? 'active' : '' }}">
                    <a href="{{ route('contact') }}">Contact</a>
                </li>
                 <li class="{{ request()->routeIs('login') ? 'active' : '' }}">
                    <a href="{{ route('login') }}">Login</a>
                </li>

            </ul>
        </nav>
    </div>
</header>

<div id="loader">
    <div class="loader">
        <div class="ball">
            <div class="floor"></div>
        </div>
        <div class="ball">
            <div class="floor"></div>
        </div>
        <div class="ball">
            <div class="floor"></div>
        </div>
    </div>
</div>




{{-- <nav class="navbar navbar-expand-lg navbar-dark"
    style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
    <div class="container-fluid px-4">

        <a class="navbar-brand d-flex align-items-center" href="#"
            style="
              margin-left: 2.5rem;
            color: #ffffff;
            font-weight: 600;
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        "
            onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
            <img src="#" alt="ASFL Logo" height="50" class="me-3 rounded-circle"
                style="border: 2px solid rgba(255,255,255,0.3);">
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
            aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto align-items-center">


                @if (Auth::check())
                    <li class="nav-item mx-1">
                        @if (Auth::user()->hasRole('admin'))
                            <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('admin.dashboard') ? 'active-nav-link' : '' }}"
                                href="{{ route('admin.dashboard') }}"
                                style="color: #ffffff; transition: all 0.3s ease; {{ request()->routeIs('admin.dashboard') ? 'border: 2px solid #ffffff;' : '' }}"
                                onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                                onmouseout="this.style.backgroundColor='transparent'; this.style.transform='translateY(0)'">
                                <i class="fas fa-sliders-h me-1"></i> Control Panel
                            </a>
                        @else
                            <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('user.dashboard') ? 'active-nav-link' : '' }}"
                                href="{{ route('user.dashboard') }}"
                                style="color: #ffffff; transition: all 0.3s ease; {{ request()->routeIs('user.dashboard') ? 'border: 2px solid #ffffff;' : '' }}"
                                onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                                onmouseout="this.style.backgroundColor='transparent'; this.style.transform='translateY(0)'">
                                <i class="fas fa-th-large me-1"></i> My Space
                            </a>
                        @endif
                    </li>
                @endif



                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('home') ? 'active-nav-link' : '' }}"
                        href="{{ route('home') }}"
                        style="
                        color: #ffffff;
                        transition: all 0.3s ease;
                        background-color:transparent; {{ request()->routeIs('home') ? 'border: 2px solid #ffffff;' : '' }};
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('home') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-home me-1"></i> Home
                    </a>
                </li>
                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('gauntlet') ? 'active-nav-link' : '' }}"
                        href="{{ route('gauntlet') }}"
                        style="
                        color: #ffffff;
                        transition: all 0.3s ease;
                        background-color: transparent; {{ request()->routeIs('gauntlet') ? 'border: 2px solid #ffffff;' : '' }};
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('gauntlet') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-basketball-ball me-1"> </i> Gauntlet
                    </a>
                </li>
                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('about') ? 'active-nav-link' : '' }}"
                        href="{{ route('about') }}"
                        style="
                        color: #ffffff;
                        transition: all 0.3s ease;
                        background-color: transparent; {{ request()->routeIs('about') ? 'border: 2px solid #ffffff;' : '' }};
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('about') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-info-circle me-1"> </i> About
                    </a>
                </li>



                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('donate') ? 'active-donate' : '' }}"
                        href="{{ route('donate') }}"
                        style="
                         color: #ffffff;
                            transition: all 0.3s ease;
                            background-color: transparent;
                        {{ request()->routeIs('donate') ? 'border: 2px solid #ffffff;' : '' }}
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('donate') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-hand-holding-heart me-1"> </i> Donate
                    </a>
                </li>
                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('leaderBoard') ? 'active-nav-link' : '' }}"
                        href="{{ route('leaderBoard') }}"
                        style="
                        color: #ffffff;
                        transition: all 0.3s ease;
                        background-color: transparent; {{ request()->routeIs('leaderBoard') ? 'border: 2px solid #ffffff;' : '' }};
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('leaderBoard') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-trophy me-1"></i> Leaderboard
                    </a>
                </li>
                <li class="nav-item mx-1">
                    <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('contact') ? 'active-nav-link' : '' }}"
                        href="{{ route('contact') }}"
                        style="
                        color: #ffffff;
                        transition: all 0.3s ease;
                        background-color: transparent;{{ request()->routeIs('contact') ? 'border: 2px solid #ffffff;' : '' }};
                    "
                        onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                        onmouseout="this.style.backgroundColor='{{ request()->routeIs('contact') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                        <i class="fas fa-envelope me-1"></i> Contact
                    </a>
                </li>

                <li class="nav-item mx-1">
                    @guest
                        <a class="nav-link px-4 py-2 rounded-pill {{ request()->routeIs('login') ? 'active-nav-link' : '' }}"
                            href="{{ route('login') }}"
                            style="
                            color: #ffffff;
                            transition: all 0.3s ease;
                            background-color: transparent; {{ request()->routeIs('login') ? 'border: 2px solid #ffffff;' : '' }};
                        "
                            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                            onmouseout="this.style.backgroundColor='{{ request()->routeIs('login') ? 'rgba(255,255,255,0.2)' : 'transparent' }}'; this.style.transform='translateY(0)'">
                            <i class="fas fa-sign-in-alt me-1"></i> Login
                        </a>
                    @else
                        <a class="nav-link px-4 py-2 rounded-pill" href="#"
                            onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                            style="
                            color: #ffffff;
                            transition: all 0.3s ease;
                            background-color: transparent;
                        "
                            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.2)'; this.style.transform='translateY(-3px)'"
                            onmouseout="this.style.backgroundColor='transparent'; this.style.transform='translateY(0)'">
                            <i class="fas fa-sign-out-alt me-1"> </i> Logout
                        </a>

                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            @csrf
                        </form>
                    @endguest
                </li>
            </ul>
        </div>
    </div>
</nav> --}}
