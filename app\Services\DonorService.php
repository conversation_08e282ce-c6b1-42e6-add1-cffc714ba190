<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use Illuminate\Validation\ValidationException;

class DonorService
{


    public function getDetailViewData(Request $request)
    {
        $data = $request->validate([
            'amount' => 'required|numeric|min:1',
            'slug' => 'required|string',
        ]);

        $amount = (float) $data['amount'];
        if ($amount <= 0) {
            throw ValidationException::withMessages([
                'amount' => 'Invalid or undefined donation amount.',
            ]);
        }

        $user = User::where('slug', $data['slug'])->first();
        if (!$user) {
            throw ValidationException::withMessages([
                'slug' => 'User not found with the provided slug.',
            ]);
        }

        return compact('user', 'amount');
    }
}
