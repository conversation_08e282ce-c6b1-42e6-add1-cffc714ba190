<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class HandleStripeSubscriptionUpdated implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */

    public $subscription;
    public function __construct($subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $nextPaymentAt = null;
        if ($this->subscription->current_period_end) {
            $nextPaymentAt = date('Y-m-d H:i:s', $this->subscription->current_period_end);
        }

        DB::table('subscriptions')
            ->where('subscription_id', $this->subscription->id)
            ->update([
                'status' => $this->subscription->status,
                'next_payment_at' => $nextPaymentAt,
            ]);
    }
}
