<?php

namespace App\Http\Controllers;

use App\Services\DonorService;
use App\Models\DonationToken;
use Illuminate\Http\Request;

class DonorController extends Controller
{


    protected $donorService;

    public function __construct(DonorService $donorService)
    {
        $this->donorService = $donorService;
    }

    public function detailView(Request $request)
    {
        $data = $this->donorService->getDetailViewData($request);

        // Generate a donation token
        $donationToken = DonationToken::generateToken(
            $data['amount'],
            $data['user']->slug
        );

        // Redirect to the GET route with the token
        return redirect()->route('donor.payment', ['token' => $donationToken->token]);
    }

    public function paymentView($token)
    {
        // Find the valid token
        $donationToken = DonationToken::findValidToken($token);

        if (!$donationToken) {
            return redirect()->route('home')->with('error', 'Invalid or expired donation link. Please try again.');
        }

        // Get the user data using the relationship
        $user = \App\Models\User::where('slug', $donationToken->user_slug)->first();
        $amount = $donationToken->amount;

        // Mark token as used to prevent reuse
        $donationToken->update(['used_at' => now()]);

        $response = response()->view('donor.detail', compact('user', 'amount'));

        // Add headers to prevent caching and back navigation
        $response->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        $response->header('Pragma', 'no-cache');
        $response->header('Expires', '0');

        return $response;
    }
}
