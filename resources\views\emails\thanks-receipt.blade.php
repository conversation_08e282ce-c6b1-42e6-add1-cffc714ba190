<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - A Shot for Life</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .logo {
            width: 120px;
            height: 120px;

            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #19345e;
            font-weight: bold;
            position: relative;
            z-index: 2;

        }

          .logo img{
                height: 153px;
              width: 125px
        }

        .header h1 {
            color: #ffffff;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            position: relative;
            z-index: 2;
        }

        .content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 24px;
            color: #19345e;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .message {
            font-size: 16px;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .receipt-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #28a745;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .receipt-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .receipt-title {
            font-size: 24px;
            color: #19345e;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .receipt-subtitle {
            color: #6c757d;
            font-size: 14px;
        }

        .amount-display {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 12px;
            color: white;
        }

        .amount-label {
            font-size: 14px;
            opacity: 0.9;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .amount-value {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .amount-status {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
        }

        .receipt-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .detail-item {
            background: #ffffff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .detail-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 16px;
            color: #19345e;
            font-weight: 600;
        }

        .donor-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .donor-info h3 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .donor-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .donor-item {
            background: rgba(255, 255, 255, 0.7);
            padding: 10px;
            border-radius: 8px;
        }

        .donor-label {
            font-size: 11px;
            color: #856404;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .donor-value {
            font-size: 14px;
            color: #856404;
            font-weight: 600;
        }

        .thank-you {
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            border: 2px solid #2196f3;
        }

        .thank-you h3 {
            color: #1565c0;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .thank-you p {
            color: #1565c0;
            font-size: 16px;
            line-height: 1.6;
        }

        .action-buttons {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #19345e 0%, #154da3 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 52, 94, 0.4);
        }

        .footer {
            background: #f05522;
            padding: 30px 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .footer-brand {
            font-size: 24px;
            font-weight: bold;
            color: white;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .footer-tagline {
            color: white;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .footer-note {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            margin-bottom: 20px;
        }

        .social-links {
            display: inline-block;
        }

        .social-links table {
            border-collapse: collapse;
        }

        .social-links td {
            padding: 0 8px;
        }

        .social-links a {
            color: white;
            text-decoration: none;
            font-size: 20px;
        }

        @media (max-width: 600px) {
            .receipt-details {
                grid-template-columns: 1fr;
            }

            .donor-details {
                grid-template-columns: 1fr;
            }

            .action-buttons .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="{{ asset('images/ball-brand.png') }}" alt="A Shot for Life" width="80" height="80" style="border-radius: 50%;">
            </div>
            <h1>Thanks Note</h1>
            <p>Thank you for your generous donation!</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ $donor->name }}! 👋</div>

            <div class="message">
                <p>Thank you for your generous donation to <strong>A Shot for Life</strong>! Your contribution will make a real difference in the fight against cancer.</p>


            </div>



            <!-- Donor Information -->
            <div class="donor-info">
                <h3>📋 Donor Information</h3>
                <div class="donor-details">
                    <div class="donor-item">
                        <div class="donor-label">Name</div>
                        <div class="donor-value">{{ $donor->name }}</div>
                    </div>

                    <div class="donor-item">
                        <div class="donor-label">Email</div>
                        <div class="donor-value">{{ $donor->email }}</div>
                    </div>

                    @if($donor->city && $donor->state)
                    <div class="donor-item">
                        <div class="donor-label">Location</div>
                        <div class="donor-value">{{ $donor->city }}, {{ $donor->state }}</div>
                    </div>
                    @endif

                    @if($donor->message_for_fundraiser)
                    <div class="donor-item" style="grid-column: 1 / -1;">
                        <div class="donor-label">Message</div>
                        <div class="donor-value">{{ $donor->message_for_fundraiser }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Thank You Section -->
            <div class="thank-you">
                <h3>🌟 Thank You for Making a Difference!</h3>
                <p>Your generous donation of <strong>${{ $amount }}</strong> will help support cancer research and provide hope to those affected by this disease. Every contribution brings us closer to finding a cure.</p>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{{ route('home') }}" class="btn btn-primary">Visit Our Website</a>
                <a href="{{ route('donate') }}" class="btn btn-primary">Make Another Donation</a>
            </div>


        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div class="footer-brand">#ASFL</div>
                <div class="footer-tagline">Making a difference, one shot at a time</div>
                <div class="footer-note">This is an automated receipt. Please do not reply to this email.</div>

                <div class="social-links">
                    <table>
                        <tr>
                            <td>
                                <a href="https://www.instagram.com/ashotforlife/" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ asset('images/instagram.png') }}" alt="Instagram" width="20" style="display:inline-block;">
                                </a>
                            </td>
                            <td>
                                <a href="https://www.tiktok.com/@ashotforlife" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ asset('images/tik-tok.png') }}" alt="TikTok" width="20" style="display:inline-block;">
                                </a>
                            </td>
                            <td>
                                <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ asset('images/facebook.png') }}" alt="Facebook" width="20" style="display:inline-block;">
                                </a>
                            </td>
                            <td>
                                <a href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ asset('images/X.png') }}" alt="Twitter" width="20" style="display:inline-block;">
                                </a>
                            </td>
                            <td>
                                <a href="mailto:<EMAIL>">
                                    <img src="{{ asset('images/mail.png') }}" alt="Email" width="20" style="display:inline-block;">
                                </a>
                            </td>
                            <td>
                                <a href="https://www.youtube.com/@ashotforlife/featured" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ asset('images/youtube.png') }}" alt="YouTube" width="20" style="display:inline-block;">
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
