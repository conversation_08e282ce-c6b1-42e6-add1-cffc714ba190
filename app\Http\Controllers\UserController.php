<?php

namespace App\Http\Controllers;


use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

use App\Services\UserService;

class UserController extends Controller
{

    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }
    public function dashboard(Request $request): View
    {
        try {
            $data = $this->userService->getDashboardData($request);
            return view('User.dashboard', compact('data'));
        } catch (\Exception $e) {


            return view('Errors.errors500', compact('e'));
        }
    }


    public function donationPage(String $slug)
    {

        $user = $this->userService->getDonationPage($slug);
        $donationPageUrl = route('donationPage', ['slug' => $user->slug]);

        $totalAmount = $user->donors()->sum('amount');
        if (!$user) {
            return redirect()->back()->with('error', 'Donation page not found.');
        }
        return view('User.donationPage', compact('user', 'totalAmount'));
    }



    public function editProfile(Request $request): JsonResponse
    {
        $user = $this->userService->editUserProfile($request);


        return response()->json([
            'success' => true,
            'message' => 'User Account updated successfully!',
            'data' => $user,

        ], 201);
    }

    public function updatePassword(Request $request): JsonResponse
    {
        $user = $this->userService->updateUserPassword($request);


        return response()->json([
            'success' => true,
            'message' => 'User Pasword updated successfully!',
            'data' => $user,

        ], 201);
    }
}
