- let siteBranch = 'A Shot for Life';
- let pageName = 'Gauntlet';
- let heroName = 'Contact';
- let parentPage = siteBranch;
- let pageTitle = `${siteBranch} - ${pageName} ${heroName}`;

include ../partials/head.pug
include ../partials/header.pug
include ../layouts/heroText.pug
include ../layouts/inputField.pug
include ../partials/footer.pug

html(lang="en")
    +head(pageTitle)

    body
        +header()

        +heroText(heroName)

        section.sec.form
            .container
                form.row.register-form.gx-lg-5(action="")
                    .col-md-6.mb-4
                        +inputField('Name')
                    .col-md-6.mb-4
                        +inputField('Email', 'email')
                    .col-md-12.mb-4
                        label.form-label(for="message") Message
                        textarea.form-control
                    .col-md-12.mt-5.text-center
                        button.cta.orange.border-0(type="submit") Contact
        section.sec.contact-meta.pt-0
            .container.text-center
                h2.mb-4 #[a(href="mailto:<EMAIL>") <EMAIL>]
                p A Shot For Life, Inc.#[br] 80 Poplar St.#[br] Watertown, MA, 02472

        +footer()
