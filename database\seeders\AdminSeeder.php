<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User
        $admin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('G@XBXPYVP52Q!'),
            'address' => 'null',
            'city' => 'null',
            'state' => 'null',
            'fund_raise_message' => 'null',
            'last_login_at' => now(),
        ]);


        $admin->roles()->attach(Role::where('name', 'admin')->first());
    }
}
